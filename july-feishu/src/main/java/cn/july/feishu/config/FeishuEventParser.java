package cn.july.feishu.config;

import cn.july.core.utils.jackson.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

/**
 * <AUTHOR>
 * @description 飞书事件解析器
 * @date 2025-04-09
 */
public class FeishuEventParser {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private static final Gson GSON = new Gson();

    public static <T> T parseEvent(Object eventObj, Class<T> eventType) {
        String json = JsonUtils.toJson(eventObj); // 转换为标准 JSON 字符串
        return GSON.fromJson(json, eventType);
    }

    public static <T> T parseEvent(String json, Class<T> eventType) {
        return GSON.fromJson(json, eventType);
    }
}

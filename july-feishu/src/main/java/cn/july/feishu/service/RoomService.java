package cn.july.feishu.service;

import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.vc.v1.model.ListRoomReq;
import com.lark.oapi.service.vc.v1.model.ListRoomRespBody;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 会议室服务
 * @date 2025-08-30
 */
@Slf4j
public class RoomService {

    private final Client feishuClient;

    public RoomService(AppConfig appConfig) {
        this.feishuClient = appConfig.getFeishuClient();
    }

    public ListRoomRespBody listMeetingRooms(int pageSize, String pageToken) {
        // 创建请求对象
        ListRoomReq req = ListRoomReq.newBuilder()
                .pageSize(pageSize)
                .pageToken(pageToken)
                .userIdType("open_id")
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.vc().v1().room()::list, FeishuErrorCode.MEETING_ROOM_FAIL);
    }
}

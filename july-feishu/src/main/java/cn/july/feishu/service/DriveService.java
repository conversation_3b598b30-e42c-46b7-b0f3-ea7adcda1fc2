package cn.july.feishu.service;

import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.model.drive.*;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.drive.v1.model.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 飞书云盘服务
 */
@Slf4j
public class DriveService {

    private final Client feishuClient;

    public DriveService(AppConfig appConfig) {
        this.feishuClient = appConfig.getFeishuClient();
    }

    /**
     * 上传素材文件
     * 
     * @param request 上传请求
     * @return 上传结果
     */
    public UploadAllMediaRespBody uploadMedia(UploadMediaRequest request) {
        log.info("开始上传素材文件，文件名：{}", request.getFileName());
        
        UploadAllMediaReq uploadReq = UploadAllMediaReq.newBuilder()
                .uploadAllMediaReqBody(UploadAllMediaReqBody.newBuilder()
                        .fileName(request.getFileName())
                        .parentType(request.getParentType())
                        .size(request.getSize().intValue())
                        .extra(request.getExtra())
                        .file(request.getFile())
                        .build())
                .build();

        return FeishuInvokeUtil.executeRequest(uploadReq, feishuClient.drive().v1().media()::uploadAll, 
                FeishuErrorCode.DRIVE_UPLOAD_FAIL);
    }

    /**
     * 上传素材文件（使用用户token）
     * 
     * @param request 上传请求
     * @param userAccessToken 用户访问token
     * @return 上传结果
     */
    public UploadAllMediaRespBody uploadMedia(UploadMediaRequest request, String userAccessToken) {
        log.info("开始上传素材文件，文件名：{}", request.getFileName());
        
        UploadAllMediaReq uploadReq = UploadAllMediaReq.newBuilder()
                .uploadAllMediaReqBody(UploadAllMediaReqBody.newBuilder()
                        .fileName(request.getFileName())
                        .parentType(request.getParentType())
                        .size(request.getSize().intValue())
                        .extra(request.getExtra())
                        .file(request.getFile())
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(uploadReq, requestOptions, feishuClient.drive().v1().media()::uploadAll, 
                FeishuErrorCode.DRIVE_UPLOAD_FAIL);
    }

    /**
     * 创建导入任务
     * 
     * @param request 导入任务请求
     * @return 导入任务结果
     */
    public CreateImportTaskRespBody createImportTask(CreateImportTaskRequest request) {
        log.info("开始创建导入任务，文件名：{}", request.getFileName());
        
        CreateImportTaskReq importReq = CreateImportTaskReq.newBuilder()
                .importTask(ImportTask.newBuilder()
                        .fileExtension(request.getFileExtension())
                        .fileToken(request.getFileToken())
                        .type(request.getType())
                        .fileName(request.getFileName())
                        .point(ImportTaskMountPoint.newBuilder()
                                .mountType(request.getMountType())
                                .mountKey(request.getMountKey())
                                .build())
                        .build())
                .build();

        return FeishuInvokeUtil.executeRequest(importReq, feishuClient.drive().v1().importTask()::create, 
                FeishuErrorCode.DRIVE_IMPORT_FAIL);
    }

    /**
     * 创建导入任务（使用用户token）
     * 
     * @param request 导入任务请求
     * @param userAccessToken 用户访问token
     * @return 导入任务结果
     */
    public CreateImportTaskRespBody createImportTask(CreateImportTaskRequest request, String userAccessToken) {
        log.info("开始创建导入任务，文件名：{}", request.getFileName());
        
        CreateImportTaskReq importReq = CreateImportTaskReq.newBuilder()
                .importTask(ImportTask.newBuilder()
                        .fileExtension(request.getFileExtension())
                        .fileToken(request.getFileToken())
                        .type(request.getType())
                        .fileName(request.getFileName())
                        .point(ImportTaskMountPoint.newBuilder()
                                .mountType(request.getMountType())
                                .mountKey(request.getMountKey())
                                .build())
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(importReq, requestOptions, feishuClient.drive().v1().importTask()::create, 
                FeishuErrorCode.DRIVE_IMPORT_FAIL);
    }

    /**
     * 查询导入任务结果
     * 
     * @param ticket 任务票据
     * @return 导入任务结果
     */
    public GetImportTaskRespBody getImportTask(String ticket) {
        log.info("查询导入任务结果，票据：{}", ticket);
        
        GetImportTaskReq getReq = GetImportTaskReq.newBuilder()
                .ticket(ticket)
                .build();

        return FeishuInvokeUtil.executeRequest(getReq, feishuClient.drive().v1().importTask()::get, 
                FeishuErrorCode.DRIVE_IMPORT_QUERY_FAIL);
    }

    /**
     * 查询导入任务结果（使用用户token）
     * 
     * @param ticket 任务票据
     * @param userAccessToken 用户访问token
     * @return 导入任务结果
     */
    public GetImportTaskRespBody getImportTask(String ticket, String userAccessToken) {
        log.info("查询导入任务结果，票据：{}", ticket);
        
        GetImportTaskReq getReq = GetImportTaskReq.newBuilder()
                .ticket(ticket)
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(getReq, requestOptions, feishuClient.drive().v1().importTask()::get, 
                FeishuErrorCode.DRIVE_IMPORT_QUERY_FAIL);
    }

    /**
     * 批量添加协作者权限
     * 
     * @param request 权限请求
     * @return 权限添加结果
     */
    public BatchCreatePermissionMemberRespBody batchCreatePermissionMember(BatchCreatePermissionMemberRequest request) {
        log.info("开始批量添加协作者权限，文档token：{}", request.getToken());
        
        // 构建成员数组
        BaseMember[] members = request.getMembers().stream()
                .map(member -> BaseMember.newBuilder()
                        .memberType(member.getMemberType())
                        .memberId(member.getMemberId())
                        .perm(member.getPerm())
                        .permType(member.getPermType())
                        .type(member.getType())
                        .build())
                .toArray(BaseMember[]::new);

        BatchCreatePermissionMemberReq permissionReq = BatchCreatePermissionMemberReq.newBuilder()
                .token(request.getToken())
                .type(request.getType())
                .needNotification(request.getNeedNotification())
                .batchCreatePermissionMemberReqBody(BatchCreatePermissionMemberReqBody.newBuilder()
                        .members(members)
                        .build())
                .build();

        return FeishuInvokeUtil.executeRequest(permissionReq, feishuClient.drive().v1().permissionMember()::batchCreate, 
                FeishuErrorCode.DRIVE_PERMISSION_FAIL);
    }

    /**
     * 批量添加协作者权限（使用用户token）
     * 
     * @param request 权限请求
     * @param userAccessToken 用户访问token
     * @return 权限添加结果
     */
    public BatchCreatePermissionMemberRespBody batchCreatePermissionMember(BatchCreatePermissionMemberRequest request, String userAccessToken) {
        log.info("开始批量添加协作者权限，文档token：{}", request.getToken());
        
        // 构建成员数组
        BaseMember[] members = request.getMembers().stream()
                .map(member -> BaseMember.newBuilder()
                        .memberType(member.getMemberType())
                        .memberId(member.getMemberId())
                        .perm(member.getPerm())
                        .permType(member.getPermType())
                        .type(member.getType())
                        .build())
                .toArray(BaseMember[]::new);

        BatchCreatePermissionMemberReq permissionReq = BatchCreatePermissionMemberReq.newBuilder()
                .token(request.getToken())
                .type(request.getType())
                .needNotification(request.getNeedNotification())
                .batchCreatePermissionMemberReqBody(BatchCreatePermissionMemberReqBody.newBuilder()
                        .members(members)
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(permissionReq, requestOptions, feishuClient.drive().v1().permissionMember()::batchCreate, 
                FeishuErrorCode.DRIVE_PERMISSION_FAIL);
    }

    /**
     * 通过用户访问令牌上传素材文件
     * 
     * @param request 上传请求
     * @param userAccessToken 用户访问令牌
     * @return 上传结果
     */
    public UploadAllMediaRespBody uploadMediaWithUserToken(UploadMediaRequest request, String userAccessToken) {
        log.info("开始通过用户令牌上传素材文件，文件名：{}", request.getFileName());
        
        UploadAllMediaReq uploadReq = UploadAllMediaReq.newBuilder()
                .uploadAllMediaReqBody(UploadAllMediaReqBody.newBuilder()
                        .fileName(request.getFileName())
                        .parentType(request.getParentType())
                        .size(request.getSize().intValue())
                        .checksum(request.getChecksum())
                        .extra(request.getExtra())
                        .file(request.getFile())
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(uploadReq, requestOptions, feishuClient.drive().v1().media()::uploadAll, 
                FeishuErrorCode.DRIVE_UPLOAD_FAIL);
    }

    /**
     * 通过用户访问令牌创建导入任务
     * 
     * @param request 导入任务请求
     * @param userAccessToken 用户访问令牌
     * @return 导入任务结果
     */
    public CreateImportTaskRespBody createImportTaskWithUserToken(CreateImportTaskRequest request, String userAccessToken) {
        log.info("开始通过用户令牌创建导入任务，文件名：{}", request.getFileName());
        
        CreateImportTaskReq importReq = CreateImportTaskReq.newBuilder()
                .importTask(ImportTask.newBuilder()
                        .fileExtension(request.getFileExtension())
                        .fileToken(request.getFileToken())
                        .type(request.getType())
                        .fileName(request.getFileName())
                        .point(ImportTaskMountPoint.newBuilder()
                                .mountType(request.getMountType())
                                .mountKey(request.getMountKey())
                                .build())
                        .build())
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(importReq, requestOptions, feishuClient.drive().v1().importTask()::create, 
                FeishuErrorCode.DRIVE_IMPORT_FAIL);
    }

    /**
     * 通过用户访问令牌查询导入任务结果
     * 
     * @param ticket 任务票据
     * @param userAccessToken 用户访问令牌
     * @return 导入任务结果
     */
    public GetImportTaskRespBody getImportTaskWithUserToken(String ticket, String userAccessToken) {
        log.info("通过用户令牌查询导入任务结果，票据：{}", ticket);
        
        GetImportTaskReq getReq = GetImportTaskReq.newBuilder()
                .ticket(ticket)
                .build();

        RequestOptions requestOptions = RequestOptions.newBuilder()
                .userAccessToken(userAccessToken)
                .build();

        return FeishuInvokeUtil.executeRequest(getReq, requestOptions, feishuClient.drive().v1().importTask()::get, 
                FeishuErrorCode.DRIVE_IMPORT_QUERY_FAIL);
    }
}
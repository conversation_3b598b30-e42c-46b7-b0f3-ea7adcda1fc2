package cn.july.feishu.service;

import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.model.task.CreateTaskRequest;
import cn.july.feishu.model.task.UpdateTaskRequest;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.EmptyData;
import com.lark.oapi.service.task.v2.model.*;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;

@Slf4j
public class TaskService {

    private final Client feishuClient;

    public TaskService(AppConfig appConfig) {
        this.feishuClient = appConfig.getFeishuClient();
    }


    public EmptyData deleteTaskById(String taskGuid){
        DeleteTaskReq req = DeleteTaskReq.newBuilder()
                .taskGuid(taskGuid)
                .build();
        return FeishuInvokeUtil.executeRequest(req,feishuClient.task().v2().task()::delete, FeishuErrorCode.TASK_DELETED_FAIL);
    }

    /**
     * 更新任务
     */
    public PatchTaskRespBody patchTask(UpdateTaskRequest req) {
        PatchTaskReq patchTaskReq = PatchTaskReq.newBuilder()
                .taskGuid(req.getTaskGuid())
                .userIdType("open_id")
                .patchTaskReqBody(PatchTaskReqBody.newBuilder()
                        .task(InputTask.newBuilder()
                                .summary(req.getSummary())
                                .description(req.getDescription())
                                .start(req.getStartTime() != null ? Start.newBuilder().timestamp(String.valueOf(req.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                                .completedAt(req.getCompletedTime() != null ? String.valueOf(req.getCompletedTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()) : null)
                                .due(req.getEndTime() != null ? Due.newBuilder().timestamp(String.valueOf(req.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                                .extra(req.getExtra())
                                .members(req.getMembers() != null ? req.getMembers().toArray(new Member[0]) : null)
                                .build())
                        .updateFields(req.getUpdateFields())
                        .build()
                )
                .build();
        return FeishuInvokeUtil.executeRequest(patchTaskReq,feishuClient.task().v2().task()::patch, FeishuErrorCode.TASK_UPDATE_FAIL);
    }

    /**
     * 创建任务
     */
    public CreateTaskRespBody createTask(CreateTaskRequest req) {
        CreateTaskReq createTaskReq = CreateTaskReq.newBuilder()
                .userIdType("open_id")
                .inputTask(InputTask.newBuilder()
                        .summary(req.getSummary())
                        .description(req.getDescription())
                        .due(req.getDueTime() != null ? Due.newBuilder().timestamp(String.valueOf(req.getDueTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                        .extra(req.getExtra())
                        .members(req.getMembers().toArray(new Member[0]))
                        .build()).build();
        return FeishuInvokeUtil.executeRequest(createTaskReq,feishuClient.task().v2().task()::create, FeishuErrorCode.TASK_CREATE_FAIL);
    }
}

package cn.july.feishu.service;

import cn.hutool.core.util.StrUtil;
import cn.july.feishu.config.AppConfig;
import cn.july.feishu.config.FeishuAppContext;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.exception.ThirdException;
import cn.july.feishu.model.*;
import cn.july.feishu.util.AssertUtil;
import cn.july.feishu.util.BuildRequestUtil;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.calendar.v4.model.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 日程管理服务
 */
@Slf4j
public class CalendarEventService {

    private final Client feishuClient;

    public CalendarEventService(AppConfig appConfig){
        feishuClient = appConfig.getFeishuClient();
    }

    public CreateCalendarEventRespBody create(CreateCalendarEventModel createCalendarEvent) {
        AssertUtil.assertCreateCalendarEventModel(createCalendarEvent);

//        CreateCalendarAclReq createCalendarAclReq = BuildRequestUtil.buildCreateCalendarAclRequest(createCalendarEvent);
//        // 用户授权
//        FeishuInvokeUtil.executeRequest(createCalendarAclReq,
//                feishuClient.calendar().calendarAcl()::create,
//                FeishuErrorCode.CREATE_CALENDAR_EVENT_FAIL);

        CreateCalendarEventReq createCalendarEventReq = BuildRequestUtil.buildCreateCalendarEventRequest(createCalendarEvent);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
            return FeishuInvokeUtil.executeRequest(createCalendarEventReq,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEvent()::create,
                    FeishuErrorCode.CREATE_CALENDAR_EVENT_FAIL);
        }

        return FeishuInvokeUtil.executeRequest(createCalendarEventReq,
                feishuClient.calendar().v4().calendarEvent()::create,
                FeishuErrorCode.CREATE_CALENDAR_EVENT_FAIL);
    }

    public void delete(CalendarEventModel calendarEvent) {
        AssertUtil.assertCalendarEventModel(calendarEvent);

        DeleteCalendarEventReq request = BuildRequestUtil.buildDeleteCalendarEventRequest(calendarEvent);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
             FeishuInvokeUtil.executeRequest(request,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEvent()::delete,
                    FeishuErrorCode.DELETE_CALENDAR_EVENT_FAIL);
             return;
        }

        FeishuInvokeUtil.executeRequest(request,
                feishuClient.calendar().v4().calendarEvent()::delete,
                FeishuErrorCode.DELETE_CALENDAR_EVENT_FAIL);
    }

    public PatchCalendarEventRespBody update(UpdateCalendarEventModel updateCalendarEvent) {
        AssertUtil.assertUpdateCalendarEventModel(updateCalendarEvent);

        PatchCalendarEventReq request = BuildRequestUtil.buildPatchCalendarEventRequest(updateCalendarEvent);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
            return FeishuInvokeUtil.executeRequest(request,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEvent()::patch,
                    FeishuErrorCode.UPDATE_CALENDAR_EVENT_FAIL);
        }

        return FeishuInvokeUtil.executeRequest(request,
                feishuClient.calendar().v4().calendarEvent()::patch,
                FeishuErrorCode.UPDATE_CALENDAR_EVENT_FAIL);
    }

    /**
     * 只更新日历事件的描述信息
     * @param updateCalendarEvent 更新日历事件模型
     * @return PatchCalendarEventRespBody 更新响应
     */
    public PatchCalendarEventRespBody updateDescription(UpdateCalendarEventModel updateCalendarEvent) {
        AssertUtil.assertUpdateCalendarEventModel(updateCalendarEvent);

        // 确保只设置了描述字段
        UpdateCalendarEventModel descriptionOnlyModel = UpdateCalendarEventModel.builder()
                .calendarId(updateCalendarEvent.getCalendarId())
                .eventId(updateCalendarEvent.getEventId())
                .description(updateCalendarEvent.getDescription())
                .build();

        PatchCalendarEventReq request = BuildRequestUtil.buildPatchCalendarEventDescriptionRequest(descriptionOnlyModel);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
            return FeishuInvokeUtil.executeRequest(request,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEvent()::patch,
                    FeishuErrorCode.UPDATE_CALENDAR_EVENT_FAIL);
        }

        return FeishuInvokeUtil.executeRequest(request,
                feishuClient.calendar().v4().calendarEvent()::patch,
                FeishuErrorCode.UPDATE_CALENDAR_EVENT_FAIL);
    }

    public GetCalendarEventRespBody get(CalendarEventModel calendarEvent) {
        AssertUtil.assertCalendarEventModel(calendarEvent);

        return FeishuInvokeUtil.executeRequest(BuildRequestUtil.buildGetCalendarEventRequest(calendarEvent),
                feishuClient.calendar().v4().calendarEvent()::get,
                FeishuErrorCode.GET_CALENDAR_EVENT_FAIL);
    }

    public ListCalendarEventRespBody list(ListCalendarEventModel listCalendarEvent) {
        AssertUtil.assertListCalendarEventModel(listCalendarEvent);

        return FeishuInvokeUtil.executeRequest(BuildRequestUtil.buildListCalendarEventRequest(listCalendarEvent),
                feishuClient.calendar().v4().calendarEvent()::list,
                FeishuErrorCode.LIST_GET_CALENDAR_EVENT_FAIL);
    }

    public InstancesCalendarEventRespBody getInstances(GetCalendarEventInstancesModel getCalendarEventInstances) {
        AssertUtil.assertGetCalendarEventInstancesModel(getCalendarEventInstances);

        return FeishuInvokeUtil.executeRequest(BuildRequestUtil.buildInstancesCalendarEventRequest(getCalendarEventInstances),
                feishuClient.calendar().v4().calendarEvent()::instances,
                FeishuErrorCode.GET_CALENDAR_EVENT_INSTANCES_FAIL);
    }

    public ListCalendarEventAttendeeRespBody getAttendees(GetCalendarEventAttendeesModel getCalendarEventAttendees) {
        AssertUtil.assertGetCalendarEventAttendeesModel(getCalendarEventAttendees);

        return FeishuInvokeUtil.executeRequest(BuildRequestUtil.buildListCalendarEventAttendeeRequest(getCalendarEventAttendees),
                feishuClient.calendar().v4().calendarEventAttendee()::list,
                FeishuErrorCode.GET_CALENDAR_EVENT_ATTENDEES_FAIL);
    }

    public CreateCalendarEventAttendeeRespBody createAttendees(CreateCalendarEventAttendeesModel createCalendarEventAttendees) {
        AssertUtil.assertCreateCalendarEventAttendeesModel(createCalendarEventAttendees);

        CreateCalendarEventAttendeeReq request = BuildRequestUtil.buildCreateCalendarEventAttendeeRequest(createCalendarEventAttendees);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
            return FeishuInvokeUtil.executeRequest(request,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEventAttendee()::create,
                    FeishuErrorCode.CREATE_CALENDAR_EVENT_ATTENDEES_FAIL);
        }

        return FeishuInvokeUtil.executeRequest(request,
                feishuClient.calendar().v4().calendarEventAttendee()::create,
                FeishuErrorCode.CREATE_CALENDAR_EVENT_ATTENDEES_FAIL);
    }

    public void deleteAttendees(DeleteCalendarEventAttendeesModel deleteCalendarEventAttendees) {
        AssertUtil.assertDeleteCalendarEventAttendeesModel(deleteCalendarEventAttendees);

        BatchDeleteCalendarEventAttendeeReq request = BuildRequestUtil.buildBatchDeleteCalendarEventAttendeeRequest(deleteCalendarEventAttendees);

        String userAccessToken = FeishuAppContext.get().getUserAccessToken();
        if (userAccessToken != null) {
            FeishuInvokeUtil.executeRequest(request,
                    RequestOptions.newBuilder().userAccessToken(userAccessToken).build(),
                    feishuClient.calendar().v4().calendarEventAttendee()::batchDelete,
                    FeishuErrorCode.DELETE_CALENDAR_EVENT_ATTENDEES_FAIL);
            return;
        }

        FeishuInvokeUtil.executeRequest(request,
                feishuClient.calendar().v4().calendarEventAttendee()::batchDelete,
                FeishuErrorCode.DELETE_CALENDAR_EVENT_ATTENDEES_FAIL);
    }

    public void subscription(String calendarId) {
        if (StrUtil.isBlank(calendarId)) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }

        SubscriptionCalendarEventReq request = SubscriptionCalendarEventReq.newBuilder()
                .calendarId(calendarId)
                .build();
        RequestOptions requestOptions = RequestOptions.newBuilder().userAccessToken(FeishuAppContext.get().getUserAccessToken()).build();
        FeishuInvokeUtil.executeRequest(request, requestOptions, feishuClient.calendar().v4().calendarEvent()::subscription, FeishuErrorCode.SUBSCRIPTION_CALENDAR_EVENT_FAIL);
    }

    public void unSubscription(String calendarId) {
        if (StrUtil.isBlank(calendarId)) {
            throw new ThirdException(FeishuErrorCode.CALENDAR_ID_NOT_EXIST);
        }

        UnsubscriptionCalendarEventReq request = UnsubscriptionCalendarEventReq.newBuilder()
                .calendarId(calendarId)
                .build();
        FeishuInvokeUtil.executeRequest(request, feishuClient.calendar().v4().calendarEvent()::unsubscription, FeishuErrorCode.UN_SUBSCRIPTION_CALENDAR_EVENT_FAIL);
    }
}

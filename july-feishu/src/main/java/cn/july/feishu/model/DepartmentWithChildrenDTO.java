package cn.july.feishu.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部门信息DTO，包含是否有子部门标识
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "部门信息DTO，包含是否有子部门标识")
public class DepartmentWithChildrenDTO {

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "父部门ID")
    private String parentDepartmentId;

    private String openDepartmentId;

    @ApiModelProperty(value = "部门负责人ID")
    private String leaderUserId;

    @ApiModelProperty(value = "部门状态")
    private String status;

    @ApiModelProperty(value = "部门描述")
    private String description;

    @ApiModelProperty(value = "部门创建时间")
    private String createTime;

    @ApiModelProperty(value = "部门更新时间")
    private String updateTime;

    @ApiModelProperty(value = "部门排序")
    private String order;

    @ApiModelProperty(value = "部门类型")
    private String departmentType;

    @ApiModelProperty(value = "是否有子部门")
    private Boolean hasChildren;

}

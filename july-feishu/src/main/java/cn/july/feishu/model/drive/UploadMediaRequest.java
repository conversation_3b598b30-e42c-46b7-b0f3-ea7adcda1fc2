package cn.july.feishu.model.drive;

import lombok.Data;
import lombok.Builder;

import java.io.File;

/**
 * <AUTHOR> Assistant
 * @description 上传素材请求模型
 */
@Data
@Builder
public class UploadMediaRequest {
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 父类型，如：docx_image, ccm_import_open
     */
    private String parentType;
    
    /**
     * 文件大小
     */
    private Long size;
    
    /**
     * 文件校验和
     */
    private String checksum;
    
    /**
     * 额外参数，JSON格式
     */
    private String extra;
    
    /**
     * 文件对象
     */
    private File file;
}
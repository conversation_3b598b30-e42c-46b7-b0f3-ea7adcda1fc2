package cn.july.feishu.model.drive;

import lombok.Data;
import lombok.Builder;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 批量创建权限成员请求模型
 */
@Data
@Builder
public class BatchCreatePermissionMemberRequest {
    
    /**
     * 文档token
     */
    private String token;
    
    /**
     * 文档类型
     */
    private String type;
    
    /**
     * 是否需要通知
     */
    private Boolean needNotification;
    
    /**
     * 成员列表
     */
    private List<PermissionMember> members;
    
    /**
     * 权限成员模型
     */
    @Data
    @Builder
    public static class PermissionMember {
        /**
         * 成员类型
         */
        private String memberType;
        
        /**
         * 成员ID
         */
        private String memberId;
        
        /**
         * 权限类型
         */
        private String perm;
        
        /**
         * 权限范围类型
         */
        private String permType;
        
        /**
         * 成员类型
         */
        private String type;
    }
}
package cn.july.feishu.model.drive;

import lombok.Data;
import lombok.Builder;

/**
 * <AUTHOR> Assistant
 * @description 创建导入任务请求模型
 */
@Data
@Builder
public class CreateImportTaskRequest {
    
    /**
     * 文件扩展名
     */
    private String fileExtension;
    
    /**
     * 文件token
     */
    private String fileToken;
    
    /**
     * 文档类型，如：docx, sheet
     */
    private String type;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 挂载类型
     */
    private Integer mountType;
    
    /**
     * 挂载键
     */
    private String mountKey;
}
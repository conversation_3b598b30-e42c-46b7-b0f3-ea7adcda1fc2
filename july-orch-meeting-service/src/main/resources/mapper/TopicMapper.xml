<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.july.orch.meeting.mapper.TopicMapper">

    <!-- 根据议题名称统计数量（排除指定ID） -->
    <select id="countByName" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM topic
        WHERE name = #{name}
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>

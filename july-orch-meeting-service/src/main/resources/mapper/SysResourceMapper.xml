<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.SysResourceMapper">
    <resultMap id="SysResourceResultMap" type="cn.july.orch.meeting.domain.po.SysResourcePO">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="resource_sort" property="resourceSort"/>
        <result column="pid" property="pid"/>
        <result column="path" property="path"/>
        <result column="status" property="status"/>
        <result column="auths" property="auths"/>
        <result column="show_link" property="showLink"/>
        <result column="show_parent" property="showParent"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <!-- 查询所有启用的菜单资源（树形结构） -->
    <select id="findMenuTree" resultMap="SysResourceResultMap">
        SELECT
            id,
            type,
            title,
            resource_sort,
            pid,
            path,
            status,
            auths,
            show_link,
            show_parent,
            remark,
            deleted,
            create_time,
            create_user_id,
            create_user_name,
            update_time,
            update_user_id,
            update_user_name
        FROM sys_resource
        WHERE deleted = 0
          AND type = 1
          AND status = 1
        ORDER BY resource_sort ASC
    </select>

    <!-- 根据角色ID查询资源列表 -->
    <select id="findResourcesByRoleId" resultMap="SysResourceResultMap">
        SELECT
            sr.id,
            sr.type,
            sr.title,
            sr.resource_sort,
            sr.pid,
            sr.path,
            sr.status,
            sr.auths,
            sr.show_link,
            sr.show_parent,
            sr.remark,
            sr.deleted,
            sr.create_time,
            sr.create_user_id,
            sr.create_user_name,
            sr.update_time,
            sr.update_user_id,
            sr.update_user_name
        FROM sys_resource sr
        INNER JOIN sys_role_resource_rel srrr ON sr.id = srrr.resource_id
        WHERE sr.deleted = 0
          AND srrr.role_id = #{roleId}
        <if test="tenantId != null">
          AND srrr.tenant_id = #{tenantId}
        </if>
        ORDER BY sr.resource_sort ASC
    </select>

    <!-- 根据用户ID查询资源列表 -->
    <select id="findResourcesByUserId" resultMap="SysResourceResultMap">
        SELECT DISTINCT
            sr.id,
            sr.type,
            sr.title,
            sr.resource_sort,
            sr.pid,
            sr.path,
            sr.status,
            sr.auths,
            sr.show_link,
            sr.show_parent,
            sr.remark,
            sr.deleted,
            sr.create_time,
            sr.create_user_id,
            sr.create_user_name,
            sr.update_time,
            sr.update_user_id,
            sr.update_user_name
        FROM sys_resource sr
        INNER JOIN sys_role_resource_rel srrr ON sr.id = srrr.resource_id
        INNER JOIN sys_user_role_rel surr ON srrr.role_id = surr.role_id
        WHERE sr.deleted = 0
          AND surr.user_id = #{userId}
        <if test="tenantId != null">
          AND srrr.tenant_id = #{tenantId}
          AND surr.tenant_id = #{tenantId}
        </if>
        ORDER BY sr.resource_sort ASC
    </select>

    <!-- 查询资源权限列表 -->
    <select id="findResourceAuths" resultType="string">
        SELECT DISTINCT sr.auths
        FROM sys_resource sr
        INNER JOIN sys_role_resource_rel srrr ON sr.id = srrr.resource_id
        INNER JOIN sys_user_role_rel surr ON srrr.role_id = surr.role_id
        WHERE sr.deleted = 0
          AND sr.auths IS NOT NULL
          AND sr.auths != ''
          AND surr.user_id = #{userId}
        <if test="tenantId != null">
          AND srrr.tenant_id = #{tenantId}
          AND surr.tenant_id = #{tenantId}
        </if>
    </select>
</mapper>

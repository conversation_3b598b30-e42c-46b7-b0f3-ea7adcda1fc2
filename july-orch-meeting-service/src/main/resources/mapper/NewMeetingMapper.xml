<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.NewMeetingMapper">
    <resultMap id="NewMeetingListDTOResultMap" type="cn.july.orch.meeting.domain.dto.NewMeetingListDTO">
        <id column="id" property="id"/>
        <result column="meeting_name" property="meetingName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="meeting_location" property="meetingLocation"/>
        <result column="attendees" property="attendees" typeHandler="cn.july.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result column="attendee_count" property="attendeeCount"/>
        <result column="host_user_id" property="hostUserId"/>
        <result column="recorder_user_id" property="recorderUserId"/>
        <result column="meeting_url" property="meetingUrl"/>
        <result column="minute_url" property="minuteUrl"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="meeting_tags_json" property="meetingTags" typeHandler="cn.july.orch.meeting.config.SimpleMeetingTagListTypeHandler"/>
        <result column="pre_meeting_documents" property="preMeetingDocuments" typeHandler="cn.july.orch.meeting.config.PreMeetingDocumentListTypeHandler"/>
        <result column="actual_start_time" property="actualStartTime"/>
        <result column="actual_end_time" property="actualEndTime"/>
        <result column="actual_attendees" property="actualAttendees" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="report_status" property="reportStatus"/>
        <result column="ai_transcript_status" property="aiTranscriptStatus"/>
        <result column="enable_doc_ai_summary" property="enableDocAiSummary"/>
        <result column="plan_name" property="planName"/>
        <result column="department_id" property="departmentId"/>
    </resultMap>

    <select id="queryPage" resultMap="NewMeetingListDTOResultMap">
        SELECT
            nm.id,
            nm.meeting_name,
            nm.start_time,
            nm.end_time,
            nm.status,
            nm.meeting_location,
            nm.attendees,
            JSON_LENGTH(nm.attendees) as attendee_count,
            nm.host_user_id,
            nm.recorder_user_id,
            nm.meeting_url,
            nm.minute_url,
            nm.create_user_name,
            nm.create_time,
            nm.meeting_tags_json,
            nm.pre_meeting_documents,
            nm.actual_start_time,
            nm.actual_end_time,
            nm.actual_attendees,
            mar.status as report_status,
            mar.ai_transcript_status as ai_transcript_status,
            nm.enable_doc_ai_summary as enable_doc_ai_summary,
            mp.plan_name as plan_name,
            nm.department_id
        FROM new_meeting nm
        LEFT JOIN meeting_analysis_reports mar ON nm.id = mar.meeting_id
        LEFT JOIN meeting_plan mp ON nm.meeting_plan_id = mp.id
        <where>
            <!-- 逻辑删除条件：只查询未删除的数据 -->
            AND nm.deleted = 0
            <if test="query.meetingName != null and query.meetingName != ''">
                AND nm.meeting_name LIKE CONCAT('%', #{query.meetingName}, '%')
            </if>
            <if test="query.status != null">
                AND nm.status = #{query.status}
            </if>
            <if test="query.priorityLevel != null">
                AND nm.priority_level = #{query.priorityLevel}
            </if>
            <if test="query.meetingPlanId != null">
                AND nm.meeting_plan_id = #{query.meetingPlanId}
            </if>
            <if test="query.meetingStandardId != null">
                AND nm.meeting_standard_id = #{query.meetingStandardId}
            </if>
            <if test="query.departmentIds != null and query.departmentIds.size() > 0">
                AND nm.department_id IN
                <foreach collection="query.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
            <if test="query.startTimeFrom != null">
                AND nm.start_time &gt;= #{query.startTimeFrom}
            </if>
            <if test="query.startTimeTo != null">
                AND nm.start_time &lt;= #{query.startTimeTo}
            </if>
            <if test="query.createUserId != null and query.createUserId != ''">
                AND nm.create_user_id = #{query.createUserId}
            </if>
            <if test="query.meetingTagIds != null and query.meetingTagIds.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        nm.meeting_tags_json, '$[*]' 
                        COLUMNS (tag_id BIGINT PATH '$.id')
                    ) AS jt 
                    WHERE jt.tag_id IN 
                    <foreach collection="query.meetingTagIds" item="tagId" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                )
            </if>
            <if test="query.hostUserId != null and query.hostUserId.size() > 0">
                AND nm.host_user_id IN 
                <foreach collection="query.hostUserId" item="hostUserIdItem" open="(" separator="," close=")">
                    #{hostUserIdItem}
                </foreach>
            </if>
            <if test="query.recorderUserIds != null and query.recorderUserIds.size() > 0">
                AND nm.recorder_user_id IN 
                <foreach collection="query.recorderUserIds" item="recorderUserId" open="(" separator="," close=")">
                    #{recorderUserId}
                </foreach>
            </if>
            <if test="query.attendeeId != null and query.attendeeId.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        nm.attendees, '$[*]' 
                        COLUMNS (attendee_open_id VARCHAR(255) PATH '$')
                    ) AS jt 
                    WHERE jt.attendee_open_id IN 
                    <foreach collection="query.attendeeId" item="attendeeIdItem" open="(" separator="," close=")">
                        #{attendeeIdItem}
                    </foreach>
                )
            </if>
        </where>
        ORDER BY nm.create_time DESC
    </select>

    <select id="queryActiveAndCompletedMeetings" resultMap="NewMeetingListDTOResultMap">
        SELECT
            nm.id,
            nm.meeting_name,
            nm.start_time,
            nm.end_time,
            nm.status,
            nm.meeting_location,
            nm.attendees,
            JSON_LENGTH(nm.attendees) as attendee_count,
            nm.host_user_id,
            nm.recorder_user_id,
            nm.meeting_url,
            nm.minute_url,
            nm.create_user_name,
            nm.create_time,
            nm.meeting_tags_json,
            nm.pre_meeting_documents,
            nm.actual_start_time,
            nm.actual_end_time,
            nm.actual_attendees,
            mar.status as report_status,
            mar.ai_transcript_status as ai_transcript_status,
            nm.enable_doc_ai_summary as enable_doc_ai_summary,
            mp.plan_name as plan_name,
            nm.department_id
        FROM new_meeting nm
        LEFT JOIN meeting_analysis_reports mar ON nm.id = mar.meeting_id
        LEFT JOIN meeting_plan mp ON nm.meeting_plan_id = mp.id
        WHERE nm.deleted = 0
          AND nm.status IN (1, 2)
        ORDER BY nm.start_time DESC
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingPlanMapper">

    <select id="queryAllHistoricalPlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            planned_start_time >= #{query.startDate} AND planned_start_time &lt; #{query.endDate}
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagIds !=null and query.tagIds.size() > 0">
                and (
                <foreach collection="query.tagIds" item="tagId" separator=" OR ">
                    JSON_CONTAINS(tagIds, #{tagId})
                </foreach>
                )
            </if>
            <if test="query.departmentIds != null and query.departmentIds.size() > 0">
                AND department_id IN
                <foreach collection="query.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询未来的一次性会议规划 -->
    <select id="queryFutureOneTimePlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            planned_start_time >= #{query.startDate} AND planned_start_time &lt; #{query.endDate}
            and (cron is null or cron = '')
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagIds !=null and query.tagIds.size() > 0">
                and (
                <foreach collection="query.tagIds" item="tagId" separator=" OR ">
                    JSON_CONTAINS(tagIds, #{tagId})
                </foreach>
                )
            </if>
            <if test="query.departmentIds != null and query.departmentIds.size() > 0">
                AND department_id IN
                <foreach collection="query.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
        </where>

    </select>

    <!-- 查询未来的重复性会议规划 -->
    <select id="queryFutureRecurringPlans" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            and cron is not null and cron != ''
            and status = 0
            <if test="query.name !=null and query.name !=''">
                AND plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagIds !=null and query.tagIds.size() > 0">
                and (
                <foreach collection="query.tagIds" item="tagId" separator=" OR ">
                    JSON_CONTAINS(tagIds, #{tagId})
                </foreach>
                )
            </if>
            <if test="query.departmentIds != null and query.departmentIds.size() > 0">
                AND department_id IN
                <foreach collection="query.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询未开始的会议规划 -->
    <select id="selectNotStartMeeting" resultType="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        select * from meeting_plan
        <where>
            status = 0
            <if test="query.startDate != null">
                and planned_start_time > #{query.startDate}
            </if>
            <if test="query.endDate != null">
                and planned_start_time &lt; #{query.endDate}
            </if>
            <if test="query.name != null and query.name != ''">
                and plan_name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.tagIds != null and query.tagIds.size() > 0">
                and (
                <foreach collection="query.tagIds" item="tagId" separator=" OR ">
                    JSON_CONTAINS(tagIds, #{tagId})
                </foreach>
                )
            </if>
            <if test="query.departmentIds != null and query.departmentIds.size() > 0">
                AND department_id IN
                <foreach collection="query.departmentIds" item="departmentId" open="(" separator="," close=")">
                    #{departmentId}
                </foreach>
            </if>
        </where>
        order by planned_start_time desc
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.SysRoleResourceRelMapper">
    <resultMap id="SysRoleResourceRelResultMap" type="cn.july.orch.meeting.domain.po.SysRoleResourceRelPO">
        <id column="id" property="id"/>
        <result column="resource_id" property="resourceId"/>
        <result column="role_id" property="roleId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <!-- 批量插入角色资源关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_role_resource_rel (
            resource_id,
            role_id,
            tenant_id,
            create_time,
            create_user_id,
            create_user_name,
            update_time,
            update_user_id,
            update_user_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.resourceId},
                #{item.roleId},
                #{item.tenantId},
                #{item.createTime},
                #{item.createUserId},
                #{item.createUserName},
                #{item.updateTime},
                #{item.updateUserId},
                #{item.updateUserName}
            )
        </foreach>
    </insert>

    <!-- 根据角色ID和租户ID查询资源ID列表 -->
    <select id="findResourceIdsByRoleIdAndTenantId" resultType="long">
        SELECT resource_id
        FROM sys_role_resource_rel
        WHERE role_id = #{roleId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据资源ID和租户ID查询角色ID列表 -->
    <select id="findRoleIdsByResourceIdAndTenantId" resultType="long">
        SELECT role_id
        FROM sys_role_resource_rel
        WHERE resource_id = #{resourceId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据角色ID列表和租户ID查询资源ID列表 -->
    <select id="findResourceIdsByRoleIdsAndTenantId" resultType="long">
        SELECT DISTINCT resource_id
        FROM sys_role_resource_rel
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据资源ID列表和租户ID查询角色ID列表 -->
    <select id="findRoleIdsByResourceIdsAndTenantId" resultType="long">
        SELECT DISTINCT role_id
        FROM sys_role_resource_rel
        WHERE resource_id IN
        <foreach collection="resourceIds" item="resourceId" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计角色关联的资源数量 -->
    <select id="countResourcesByRoleId" resultType="int">
        SELECT COUNT(*)
        FROM sys_role_resource_rel
        WHERE role_id = #{roleId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计资源关联的角色数量 -->
    <select id="countRolesByResourceId" resultType="int">
        SELECT COUNT(*)
        FROM sys_role_resource_rel
        WHERE resource_id = #{resourceId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 检查角色资源关联是否存在 -->
    <select id="existsByRoleIdAndResourceId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_role_resource_rel
        WHERE role_id = #{roleId}
          AND resource_id = #{resourceId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingCheckInMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingCheckInPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="meeting_id" property="meetingId" jdbcType="BIGINT"/>
        <result column="attendee_open_id" property="attendeeOpenId" jdbcType="VARCHAR"/>
        <result column="attendee_name" property="attendeeName" jdbcType="VARCHAR"/>
        <result column="checkin_code" property="checkinCode" jdbcType="VARCHAR"/>
        <result column="checkin_status" property="checkinStatus" jdbcType="TINYINT"/>
        <result column="checkin_time" property="checkinTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, meeting_id, attendee_open_id, attendee_name, checkin_code, 
        checkin_status, checkin_time, create_time, update_time
    </sql>

    <!-- 根据会议ID查询签到信息 -->
    <select id="findByMeetingId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM meeting_checkin
        WHERE meeting_id = #{meetingId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据签到码查询签到信息 -->
    <select id="findByCheckinCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM meeting_checkin
        WHERE checkin_code = #{checkinCode}
        LIMIT 1
    </select>

    <!-- 根据会议ID和参会人open_id查询签到信息 -->
    <select id="findByMeetingIdAndAttendeeOpenId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM meeting_checkin
        WHERE meeting_id = #{meetingId} AND attendee_open_id = #{attendeeOpenId}
        LIMIT 1
    </select>

    <!-- 批量插入签到信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO meeting_checkin (
            meeting_id, attendee_open_id, attendee_name, checkin_code, 
            checkin_status, checkin_time, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.meetingId}, #{item.attendeeOpenId}, #{item.attendeeName}, 
                #{item.checkinCode}, #{item.checkinStatus}, #{item.checkinTime}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>

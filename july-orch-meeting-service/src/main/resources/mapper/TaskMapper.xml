<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.july.orch.meeting.mapper.TaskMapper">

    <!-- 查询超期任务 -->
    <select id="findOverdueTasks" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND due_date IS NOT NULL
          AND due_date &lt; #{currentTime}
          AND status != 2
        ORDER BY due_date ASC
    </select>

    <!-- 根据会议ID查询任务列表 -->
    <select id="findByMeetingId" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND meeting_id = #{meetingId}
    </select>

    <!-- 根据状态查询任务列表 -->
    <select id="findByStatus" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 根据负责人查询任务列表 -->
    <select id="findByOwner" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND owner_open_id = #{ownerOpenId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据飞书任务ID查询任务 -->
    <select id="findByFeishuTaskId" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND feishu_task_id = #{feishuTaskId}
        LIMIT 1
    </select>

    <!-- 统计任务清单下的任务总数 -->
    <select id="countByTaskListId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tasks
        WHERE deleted = 0
          AND task_list_id = #{taskListId}
    </select>

    <!-- 统计任务清单下已完成的任务数 -->
    <select id="countCompletedByTaskListId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM tasks
        WHERE deleted = 0
          AND task_list_id = #{taskListId}
          AND status = 2
    </select>

    <!-- 根据会议标签筛选任务（支持分页） -->
    <select id="selectTasksByMeetingTags" resultType="cn.july.orch.meeting.domain.po.TaskPO">
        SELECT DISTINCT t.* FROM tasks t
        LEFT JOIN new_meeting nm ON t.meeting_id = nm.id
        LEFT JOIN meeting_standard ms ON nm.meeting_standard_id = ms.id
        LEFT JOIN meeting_standard_tag_pivot mstp ON ms.id = mstp.standard_id AND mstp.deleted = 0
        <where>
            AND t.deleted = 0
            <!-- 会议标签筛选 -->
            <if test="meetingTagIds != null and meetingTagIds.size() > 0">
                AND mstp.tag_id IN
                <foreach collection="meetingTagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
            <!-- 任务标题模糊查询 -->
            <if test="title != null and title != ''">
                AND t.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <!-- 负责人查询 -->
            <if test="ownerOpenId != null and ownerOpenId != ''">
                AND t.owner_open_id = #{ownerOpenId}
            </if>
            <!-- 负责人名称模糊查询 -->
            <if test="ownerName != null and ownerName != ''">
                AND t.owner_name LIKE CONCAT('%', #{ownerName}, '%')
            </if>
            <!-- 优先级查询 -->
            <if test="priority != null">
                AND t.priority = #{priority}
            </if>
            <!-- 状态查询 -->
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <!-- 会议ID查询 -->
            <if test="meetingId != null">
                AND t.meeting_id = #{meetingId}
            </if>
            <!-- 任务清单ID查询 -->
            <if test="taskListId != null">
                AND t.task_list_id = #{taskListId}
            </if>
            <!-- 截止时间范围查询 -->
            <if test="dueDateStart != null">
                AND t.due_date &gt;= #{dueDateStart}
            </if>
            <if test="dueDateEnd != null">
                AND t.due_date &lt;= #{dueDateEnd}
            </if>
            <!-- 创建时间范围查询 -->
            <if test="createTimeStart != null">
                AND t.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND t.create_time &lt;= #{createTimeEnd}
            </if>
            <!-- 只查询超期任务 -->
            <if test="onlyOverdue != null and onlyOverdue == true">
                AND t.due_date &lt; NOW()
                AND t.status != 2
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

</mapper>

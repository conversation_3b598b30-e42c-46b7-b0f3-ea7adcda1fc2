<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.FmsTaskMapper">

    <!-- 分页查询任务列表 -->
    <select id="selectTaskPage" resultType="cn.july.orch.meeting.domain.dto.FmsTaskDTO">
        SELECT 
            t.id,
            t.task_code,
            c.task_name,
            t.task_type,
            c.process_type,
            t.task_status,
            t.task_params,
            t.file_id,
            t.file_name,
            t.file_url,
            t.error_message,
            t.start_time,
            t.end_time,
            t.create_user_id,
            t.create_user_name,
            t.create_time,
            t.update_user_id,
            t.update_user_name,
            t.update_time
        FROM fms_task t
        LEFT JOIN fms_task_config c ON t.task_code = c.task_code AND c.deleted = 0
        WHERE t.deleted = 0
        <if test="query.taskCode != null and query.taskCode != ''">
            AND t.task_code = #{query.taskCode}
        </if>
        <if test="query.taskType != null">
            AND t.task_type = #{query.taskType}
        </if>
        <if test="query.taskStatus != null">
            AND t.task_status = #{query.taskStatus}
        </if>
        ORDER BY t.create_time DESC
    </select>

    <!-- 根据任务代码查询配置 -->
    <select id="selectConfigByTaskCode" resultType="cn.july.orch.meeting.domain.po.FmsTaskConfigPO">
        SELECT 
            id,
            task_code,
            task_name,
            task_type,
            process_type,
            target_class,
            target_method,
            description,
            is_enabled,
            create_user_id,
            create_user_name,
            create_time,
            update_user_id,
            update_user_name,
            update_time,
            deleted
        FROM fms_task_config
        WHERE task_code = #{taskCode} AND deleted = 0 AND is_enabled = 1
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus">
        UPDATE fms_task 
        SET task_status = #{status.code},
            <if test="errorMessage != null">
                error_message = #{errorMessage},
            </if>
            <if test="status.code == 1">
                start_time = NOW(),
            </if>
            <if test="status.code == 2 or status.code == 3">
                end_time = NOW(),
            </if>
            update_time = NOW()
        WHERE id = #{taskId}
    </update>

    <!-- 更新任务文件信息 -->
    <update id="updateTaskFileInfo">
        UPDATE fms_task 
        SET file_id = #{fileId},
            file_name = #{fileName},
            file_url = #{fileUrl},
            update_time = NOW()
        WHERE id = #{taskId}
    </update>

</mapper>

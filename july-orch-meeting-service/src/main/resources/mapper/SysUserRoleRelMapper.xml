<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.SysUserRoleRelMapper">
    <resultMap id="SysUserRoleRelResultMap" type="cn.july.orch.meeting.domain.po.SysUserRoleRelPO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="role_type" property="roleType"/>
        <result column="role_id" property="roleId"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>

    <!-- 批量插入用户角色关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_user_role_rel (
            user_id,
            role_type,
            role_id,
            tenant_id,
            create_time,
            create_user_id,
            create_user_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userId},
                #{item.roleType},
                #{item.roleId},
                #{item.tenantId},
                #{item.createTime},
                #{item.createUserId},
                #{item.createUserName}
            )
        </foreach>
    </insert>

    <!-- 根据用户ID和租户ID查询角色ID列表 -->
    <select id="findRoleIdsByUserIdAndTenantId" resultType="long">
        SELECT role_id
        FROM sys_user_role_rel
        WHERE user_id = #{userId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据角色ID和租户ID查询用户ID列表 -->
    <select id="findUserIdsByRoleIdAndTenantId" resultType="string">
        SELECT user_id
        FROM sys_user_role_rel
        WHERE role_id = #{roleId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据用户ID列表和租户ID查询角色ID列表 -->
    <select id="findRoleIdsByUserIdsAndTenantId" resultType="long">
        SELECT DISTINCT role_id
        FROM sys_user_role_rel
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据角色ID列表和租户ID查询用户ID列表 -->
    <select id="findUserIdsByRoleIdsAndTenantId" resultType="string">
        SELECT DISTINCT user_id
        FROM sys_user_role_rel
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据角色类型和租户ID查询用户ID列表 -->
    <select id="findUserIdsByRoleTypeAndTenantId" resultType="string">
        SELECT DISTINCT user_id
        FROM sys_user_role_rel
        WHERE role_type = #{roleType}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计用户关联的角色数量 -->
    <select id="countRolesByUserId" resultType="int">
        SELECT COUNT(*)
        FROM sys_user_role_rel
        WHERE user_id = #{userId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 统计角色关联的用户数量 -->
    <select id="countUsersByRoleId" resultType="int">
        SELECT COUNT(*)
        FROM sys_user_role_rel
        WHERE role_id = #{roleId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 检查用户角色关联是否存在 -->
    <select id="existsByUserIdAndRoleId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_user_role_rel
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>

    <!-- 根据用户ID和角色类型查询角色ID列表 -->
    <select id="findRoleIdsByUserIdAndRoleType" resultType="long">
        SELECT role_id
        FROM sys_user_role_rel
        WHERE user_id = #{userId}
          AND role_type = #{roleType}
        <if test="tenantId != null">
          AND tenant_id = #{tenantId}
        </if>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingTagMapper">

    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingTagPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="color" jdbcType="VARCHAR" property="color" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>

    <!-- 根据标签名称统计数量（排除指定ID） -->
    <select id="countByName" resultType="int">
        SELECT COUNT(1)
        FROM meeting_tags
        WHERE name = #{name}
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 统计会议标签被会议标准使用的次数 -->
    <select id="countUsedByMeetingStandard" resultType="int">
        SELECT COUNT(1)
        FROM meeting_standard_tag_pivot
        WHERE tag_id = #{tagId}
        AND deleted = 0
    </select>

</mapper>
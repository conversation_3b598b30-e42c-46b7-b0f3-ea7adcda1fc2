<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingStandardTagPivotMapper">

    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingStandardTagPivotPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="standard_id" jdbcType="BIGINT" property="standardId" />
        <result column="tag_id" jdbcType="BIGINT" property="tagId" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>

    <!-- 根据会议标准ID查询关联的标签ID列表 -->
    <select id="selectTagIdsByStandardId" resultType="java.lang.Long">
        SELECT tag_id
        FROM meeting_standard_tag_pivot
        WHERE standard_id = #{standardId}
        AND deleted = 0
    </select>

    <!-- 根据会议标准ID删除关联关系 -->
    <delete id="deleteByStandardId">
        delete from meeting_standard_tag_pivot
        WHERE standard_id = #{standardId}
        AND deleted = 0
    </delete>

    <!-- 批量插入关联关系 -->
    <insert id="batchInsert">
        INSERT INTO meeting_standard_tag_pivot (
            standard_id, tag_id,
            create_user_id, create_user_name, create_time,
            update_user_id, update_user_name, update_time,
            deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.standardId}, #{item.tagId},
                #{item.createUserId}, #{item.createUserName}, #{item.createTime},
                #{item.updateUserId}, #{item.updateUserName}, #{item.updateTime},
                #{item.deleted}
            )
        </foreach>
    </insert>

</mapper>
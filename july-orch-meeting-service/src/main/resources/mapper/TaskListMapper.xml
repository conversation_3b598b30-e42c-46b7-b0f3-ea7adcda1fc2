<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.july.orch.meeting.mapper.TaskListMapper">

    <!-- 根据名称查询任务清单 -->
    <select id="findByName" resultType="cn.july.orch.meeting.domain.po.TaskListPO">
        SELECT * FROM task_lists
        WHERE deleted = 0
          AND name LIKE CONCAT('%', #{name}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 根据创建人ID查询任务清单 -->
    <select id="findByCreateUserId" resultType="cn.july.orch.meeting.domain.po.TaskListPO">
        SELECT * FROM task_lists
        WHERE deleted = 0
          AND create_user_id = #{createUserId}
        ORDER BY create_time DESC
    </select>

</mapper>
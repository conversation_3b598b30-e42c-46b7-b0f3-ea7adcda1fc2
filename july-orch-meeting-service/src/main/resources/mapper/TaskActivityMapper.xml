<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.july.orch.meeting.mapper.TaskActivityMapper">

    <!-- 根据任务ID查询动态列表 -->
    <select id="findByTaskId" resultType="cn.july.orch.meeting.domain.po.TaskActivityPO">
        SELECT * FROM task_activities
        WHERE deleted = 0
          AND task_id = #{taskId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID查询最新的动态 -->
    <select id="findLatestByTaskId" resultType="cn.july.orch.meeting.domain.po.TaskActivityPO">
        SELECT * FROM task_activities
        WHERE deleted = 0
          AND task_id = #{taskId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

</mapper>
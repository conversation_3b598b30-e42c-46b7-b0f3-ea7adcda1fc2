server:
  port: 8100
  servlet:
    context-path: /api/meeting
  tomcat:
    connection-timeout: 180000
    keep-alive-timeout: 5000
spring:
  profiles:
    active: @spring.profiles.active@
  application:
    name: july-orch-meeting
  servlet:
    multipart:
      resolve-lazily: true # multipart 懒加载
      max-file-size: 20MB
      max-request-size: 20MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations: classpath:mapper/*Mapper.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

knife4j:
  enable: true
  openapi:
    title: ${spring.application.name}
    description: 会议系统
    concat: july
    version: 1.0.0

july:
  database:
    mybatis-plus:
      enable-tenant-line: true
  spring:
    web:
      exception:
        # 需要自定义修改
        product-code: "01"
        service-code: "01"
      access-log:
        enabled: true
        global: true
      request-context-type: cn.july.orch.meeting.config.JulyRequestContext
  feishu:
    task:
      # 飞书任务同步开关
      sync-enabled: true
      # 任务卡片提醒开关
      notification-enabled: false
      # 系统跳转链接配置
      system-url: "https://your-domain.com/task"
      # 任务详情页面路径模板（{taskId}会被替换为实际任务ID）
      detail-path: "/detail/{taskId}"
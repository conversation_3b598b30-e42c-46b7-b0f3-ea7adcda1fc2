knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://************:6379",
            "password": "July123456.",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
#dromara:
#  x-file-storage: #文件存储配置
#    default-platform: minio-1 #默认使用的存储平台
#    thumbnail-suffix: ".min.jpg"
#    minio:
#      - platform: minio-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: FHZArYPFxUOjadtHNGbo
#        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
#        end-point: http://coe-file-sit.pengfeijituan.com:9000/
#        bucket-name: meeting-1
#        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
dromara:
  x-file-storage: #文件存储配置
    default-platform: huawei-obs-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    huawei-obs:
      - platform: huawei-obs-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ERMZYOVKRBTVCAIBTIJ2
        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
        end-point: obs.cn-north-4.myhuaweicloud.com
        bucket-name: test-kangjian
        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: genn-pf-orch-meeting
july:
  meeting:
    cardSend:
      jumpUrl:
        evaluationUrl: http://www.baidu.com

    agent:
      invokeDomain: https://cerebro-sit.genn.cn
      # 智能体应用ID配置
      meetingAnalysisAppId: 6892ae7c9e9b7bc7598f5c54
      aiTranscriptAppId: 68ad718ca364b37b2d3b5a53
      qaAppId: 688c63f47e9027870e39c7dd
      fileSummaryAppId: 688c63597e9027870e39c5d9
      extractTaskAppId: 68b8ebea5401eab68eab8c7e
      # 智能体鉴权配置
      summaryAuthorization: Bearer gennai-yRjuiUeSsiRQ2IXwke4UrWkpFx9zKBmrgh2yhLaOM2zpvTVNb5w4vqdje
      qaAuthorization: Bearer gennai-iFJdQucHdmkZijiNRhvmjYC6vf1oIALgqPdjFbSE59bTUpsZJ8RhOQN
      reportAuthorization: Bearer gennai-eXd1B6Hty4d70trGu3CuIGGkfSS2CMNYFkyHsbUYwY4WiftvxNoU56sYoh
      aiTranscriptAuthorization: Bearer gennai-uJELy30V6ttN6ZbiDAbClCOEoZh4mGOMrW6XzfkAeaku48l69OxbRO3YXANjkK
      extractTaskAuthorization: Bearer gennai-g60Jrolrb5kz8f6pwCrjacwz5jXbO3ECfXe7V1bJ0mJOzsS9vQm3sRjA
    permission:
      excludePatterns:
        - /user/getUserInfo
        - /file/**
        - /app/id
        - /analysis-report/**
        - /test/**
  database:
    multi:
      db:
        july_orch_meeting:
          primary: true
          master:
            jdbcUrl: jdbc:mysql://************:3306/july_orch_meeting_tenant?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=UTC&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true
            username: root
            password: July123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
    mybatis-plus:
      tenant-ignore-tables:
        - tenant_config

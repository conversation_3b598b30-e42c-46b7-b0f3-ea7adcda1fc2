package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议数据统计查询对象
 * @date 2025-01-24
 */
@Data
public class MeetingDataQuery implements Serializable {

    @ApiModelProperty(value = "开始日期", required = true, example = "2025-08-01")
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期", required = true, example = "2025-08-31")
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    @ApiModelProperty(value = "指定标签ID列表")
    private List<Long> tagIds;

    @ApiModelProperty(value = "部门ID列表（用于批量查询部门及其子部门）")
    private List<String> departmentIds;

    @ApiModelProperty(value = "是否使用模拟数据", example = "false")
    private Boolean useMockData = false;
}

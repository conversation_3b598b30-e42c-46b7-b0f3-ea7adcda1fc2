package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.domain.dto.TenantConfigDTO;
import cn.july.orch.meeting.service.TenantConfigService;
import cn.july.orch.meeting.service.TenantFeishuClientManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 租户缓存刷新任务
 * 定时刷新租户配置缓存，确保租户状态变更能及时生效
 */
@Slf4j
@Component
public class TenantCacheRefreshTask {

    @Resource
    private TenantConfigService tenantConfigService;
    
    @Resource
    private TenantFeishuClientManager tenantFeishuClientManager;

    /**
     * 每5分钟刷新一次租户缓存
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    @IgnoreTenant
    public void refreshTenantCache() {
        log.debug("开始刷新租户缓存");
        
        try {
            // 获取当前缓存中的所有租户ID
            Map<Long, Object> currentClients = tenantFeishuClientManager.getAllClientInfo();
            Set<Long> cachedTenantIds = currentClients.keySet();
            
            // 获取数据库中所有启用的租户配置
            List<TenantConfigDTO> enabledConfigs = tenantConfigService.getEnabledConfigs();
            Set<Long> enabledTenantIds = enabledConfigs.stream()
                    .map(TenantConfigDTO::getTenantId)
                    .collect(Collectors.toSet());
            
            // 找出需要移除的租户（在缓存中但不在启用列表中）
            Set<Long> toRemove = cachedTenantIds.stream()
                    .filter(tenantId -> !enabledTenantIds.contains(tenantId))
                    .collect(Collectors.toSet());
            
            // 找出需要添加的租户（在启用列表中但不在缓存中）
            Set<Long> toAdd = enabledTenantIds.stream()
                    .filter(tenantId -> !cachedTenantIds.contains(tenantId))
                    .collect(Collectors.toSet());
            
            // 移除已禁用或删除的租户
            for (Long tenantId : toRemove) {
                try {
                    tenantFeishuClientManager.removeClient(tenantId);
                    log.info("移除租户客户端: {}", tenantId);
                } catch (Exception e) {
                    log.error("移除租户客户端失败: {}", tenantId, e);
                }
            }
            
            // 添加新启用的租户
            for (Long tenantId : toAdd) {
                try {
                    tenantFeishuClientManager.getClient(tenantId);
                    log.info("添加租户客户端: {}", tenantId);
                } catch (Exception e) {
                    log.error("添加租户客户端失败: {}", tenantId, e);
                }
            }
            
            // 刷新所有现有租户的配置（处理配置更新）
            for (Long tenantId : enabledTenantIds) {
                if (cachedTenantIds.contains(tenantId)) {
                    try {
                        tenantFeishuClientManager.refreshClient(tenantId);
                        log.debug("刷新租户客户端配置: {}", tenantId);
                    } catch (Exception e) {
                        log.error("刷新租户客户端配置失败: {}", tenantId, e);
                    }
                }
            }
            
            log.info("租户缓存刷新完成，移除: {}, 添加: {}, 刷新: {}", 
                    toRemove.size(), toAdd.size(), enabledTenantIds.size());
            
        } catch (Exception e) {
            log.error("刷新租户缓存失败", e);
        }
    }
    
    /**
     * 应用启动后延迟30秒执行一次缓存预热
     */
    @Scheduled(initialDelay = 30000, fixedRate = Long.MAX_VALUE)
    public void warmUpCache() {
        log.info("开始预热租户缓存");
        try {
            tenantFeishuClientManager.warmUpClients();
        } catch (Exception e) {
            log.error("预热租户缓存失败", e);
        }
    }
}

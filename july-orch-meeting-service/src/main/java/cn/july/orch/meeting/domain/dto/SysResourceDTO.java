package cn.july.orch.meeting.domain.dto;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.ResourceTypeEnum;
import cn.july.orch.meeting.enums.StatusBooleanEnum;
import cn.july.orch.meeting.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SysResourceDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("资源类型（1: 菜单，2：按钮）")
    private ResourceTypeEnum type;

    @ApiModelProperty("菜单、面包屑、多标签页显示的名称")
    private String title;

    @ApiModelProperty("排序")
    private Integer resourceSort;

    @ApiModelProperty("上级菜单")
    private Long pid;

    @ApiModelProperty("前端path")
    private String path;

    @ApiModelProperty("状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty("按钮权限标识")
    private String auths;

    @ApiModelProperty("是否在菜单中显示（默认值：true）")
    private StatusBooleanEnum showLink;

    @ApiModelProperty("是否显示父级菜单（默认值：true）")
    private StatusBooleanEnum showParent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("逻辑删除（0：未删除；1：删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;

    @ApiModelProperty(value = "子菜单")
    private List<SysResourceDTO> children;
}

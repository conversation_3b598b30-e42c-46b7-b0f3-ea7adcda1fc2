package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * TaskActivityPO对象
 *
 * <AUTHOR> Assistant
 * @desc 任务动态表
 */
@Data
@Accessors(chain = true)
@TableName(value = "task_activities", autoResultMap = true)
public class TaskActivityPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 关联的任务ID (逻辑关联 tasks.id)
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 动态类型 (CREATE, UPDATE_STATUS, ADD_COMMENT等)
     */
    @TableField("activity_type")
    private String activityType;

    /**
     * 动态描述 (一句话简单描述)
     */
    @TableField("activity_description")
    private String activityDescription;

    /**
     * 动态内容的结构化描述 (JSON格式)
     */
    @TableField(value = "content_json", typeHandler = cn.july.orch.meeting.config.CustomMapTypeHandler.class)
    private Map<String, Object> contentJson;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间 (即动态发生时间)
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 操作用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 操作用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}
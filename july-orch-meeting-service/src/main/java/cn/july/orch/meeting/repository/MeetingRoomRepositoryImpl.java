package cn.july.orch.meeting.repository;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.domain.po.MeetingRoomPO;
import cn.july.orch.meeting.domain.query.MeetingRoomQuery;
import cn.july.orch.meeting.mapper.MeetingRoomMapper;
import cn.july.orch.meeting.assembler.MeetingRoomAssembler;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会议室Repository实现类
 */
@Repository
@RequiredArgsConstructor
public class MeetingRoomRepositoryImpl implements MeetingRoomRepository {

    private final MeetingRoomMapper meetingRoomMapper;
    private final MeetingRoomAssembler meetingRoomAssembler;

    @Override
    public MeetingRoom save(MeetingRoom meetingRoom) {
        MeetingRoomPO po = meetingRoomAssembler.toPO(meetingRoom);
        if (po.getId() == null) {
            meetingRoomMapper.insert(po);
        } else {
            meetingRoomMapper.updateById(po);
        }
        return meetingRoomAssembler.toEntity(po);
    }

    @Override
    public MeetingRoom findById(Long id) {
        MeetingRoomPO po = meetingRoomMapper.selectById(id);
        return po == null ? null : meetingRoomAssembler.toEntity(po);
    }

    @Override
    public MeetingRoom findByFsRoomId(String fsRoomId) {
        LambdaQueryWrapper<MeetingRoomPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingRoomPO::getFsRoomId, fsRoomId)
               .eq(MeetingRoomPO::getDeleted, 0);
        MeetingRoomPO po = meetingRoomMapper.selectOne(wrapper);
        return po == null ? null : meetingRoomAssembler.toEntity(po);
    }

    @Override
    public PageResultDTO<MeetingRoom> pageQuery(MeetingRoomQuery query) {
        LambdaQueryWrapper<MeetingRoomPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingRoomPO::getDeleted, 0);
        
        if (query.getName() != null && !query.getName().isEmpty()) {
            wrapper.like(MeetingRoomPO::getName, query.getName());
        }
        
        if (query.getStatus() != null) {
            wrapper.eq(MeetingRoomPO::getStatus, query.getStatus());
        }
        
        wrapper.orderByAsc(MeetingRoomPO::getName);
        
        IPage<MeetingRoomPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<MeetingRoomPO> result = meetingRoomMapper.selectPage(page, wrapper);
        
        List<MeetingRoom> records = meetingRoomAssembler.toEntityList(result.getRecords());
        return new PageResultDTO<>(query.getPageNum(), query.getPageSize(), result.getTotal(), records);
    }

    @Override
    public List<MeetingRoom> findAll() {
        LambdaQueryWrapper<MeetingRoomPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingRoomPO::getDeleted, 0)
               .orderByAsc(MeetingRoomPO::getName);
        List<MeetingRoomPO> pos = meetingRoomMapper.selectList(wrapper);
        return meetingRoomAssembler.toEntityList(pos);
    }

    @Override
    public void deleteById(Long id) {
        MeetingRoomPO po = new MeetingRoomPO();
        po.setId(id);
        po.setDeleted(1);
        meetingRoomMapper.updateById(po);
    }
}
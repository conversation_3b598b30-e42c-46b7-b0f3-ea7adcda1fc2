package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划详情DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanDetailDTO {
    @ApiModelProperty("规划ID")
    private Long planId;

    @ApiModelProperty("规划名称")
    private String planName;

    @ApiModelProperty("会议规划描述/备注")
    private String planDescription;

    @ApiModelProperty("会议标准ID")
    private Long meetingStandardId;

    @ApiModelProperty("会议标准信息")
    private MeetingStandardDTO meetingStandard;

    @ApiModelProperty("部门ID")
    private String departmentId;

    @ApiModelProperty("部门详情")
    private DepartmentDetailDTO departmentDetail;

    @ApiModelProperty("计划开始时间")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty("计划结束时间")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty("计划持续时长（分钟）")
    private Integer plannedDuration;

    @ApiModelProperty("重复会议cron")
    private String cron;

    @ApiModelProperty("重复会议UUID")
    private String uuid;

    @ApiModelProperty("重复结束日期")
    private LocalDateTime recurrenceEndDate;

    @ApiModelProperty("规划状态")
    private MeetingPlanStatusEnum status;

    @ApiModelProperty("关联标签id")
    private List<Long> tagIds;

    @ApiModelProperty("规划标签信息")
    private List<MeetingTagDTO> tags;

    @ApiModelProperty("提醒人员列表")
    private List<String> attendees;

    @ApiModelProperty("参会人员详细信息")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty("提前通知发送标记(0-未发送,1-已发送)")
    private Integer advanceNoticeSent;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人ID")
    private String updateUserId;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("关联的新会议列表")
    private List<NewMeetingDTO> newMeetings;
}
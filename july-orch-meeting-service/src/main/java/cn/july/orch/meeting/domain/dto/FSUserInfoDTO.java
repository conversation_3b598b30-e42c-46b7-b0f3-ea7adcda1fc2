package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 飞书用户信息DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FSUserInfoDTO {

    @ApiModelProperty(value = "用户openId")
    private String openId;

    @ApiModelProperty(value = "用户姓名")
    private String name;

    @ApiModelProperty(value = "用户头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "签到状态(0:未签到,1:已签到)")
    private Integer checkinStatus;

    @ApiModelProperty(value = "签到时间")
    private java.time.LocalDateTime checkinTime;
}

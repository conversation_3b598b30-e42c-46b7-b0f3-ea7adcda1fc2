package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.dto.FmsTaskDTO;
import cn.july.orch.meeting.domain.po.FmsTaskConfigPO;
import cn.july.orch.meeting.domain.po.FmsTaskPO;
import cn.july.orch.meeting.domain.query.FmsTaskQuery;
import cn.july.orch.meeting.enums.FmsTaskStatusEnum;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> Assistant
 * @description FMS任务Mapper
 */
@Mapper
public interface FmsTaskMapper extends BaseMapper<FmsTaskPO> {
    
    /**
     * 分页查询任务列表
     */
    Page<FmsTaskDTO> selectTaskPage(Page<FmsTaskDTO> page, @Param("query") FmsTaskQuery query);
    
    /**
     * 根据任务代码查询配置
     */
    FmsTaskConfigPO selectConfigByTaskCode(@Param("taskCode") String taskCode);
    
    /**
     * 更新任务状态
     */
    void updateTaskStatus(@Param("taskId") Long taskId, @Param("status") FmsTaskStatusEnum status, @Param("errorMessage") String errorMessage);
    
    /**
     * 更新任务文件信息
     */
    void updateTaskFileInfo(@Param("taskId") Long taskId, @Param("fileId") String fileId, 
                           @Param("fileName") String fileName, @Param("fileUrl") String fileUrl);
}

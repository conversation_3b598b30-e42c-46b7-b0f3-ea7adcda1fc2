package cn.july.orch.meeting.service;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.TaskListAssembler;
import cn.july.orch.meeting.domain.dto.TaskListManagementDTO;
import cn.july.orch.meeting.domain.entity.TaskListAgg;
import cn.july.orch.meeting.domain.entity.TaskListInfo;
import cn.july.orch.meeting.domain.po.TaskListPO;
import cn.july.orch.meeting.domain.query.TaskListQuery;
import cn.july.orch.meeting.mapper.TaskListMapper;
import cn.july.orch.meeting.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务清单查询服务
 */
@Slf4j
@Service
public class TaskListQueryService {

    @Resource
    private TaskListDomainService taskListDomainService;
    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskListMapper taskListMapper;
    @Resource
    private TaskListAssembler taskListAssembler;

    /**
     * 查询任务清单详情
     *
     * @param id 任务清单ID
     * @return 任务清单详情
     */
    public TaskListManagementDTO detail(Long id) {
        log.info("查询任务清单详情，ID：{}", id);
        
        TaskListAgg taskListAgg = taskListDomainService.findById(id);
        if (taskListAgg == null || taskListAgg.getInfo() == null) {
            log.warn("任务清单不存在，ID：{}", id);
            return new TaskListManagementDTO();
        }
        
        TaskListInfo taskListInfo = taskListAgg.getInfo();
        TaskListManagementDTO result = new TaskListManagementDTO();
        
        // 基础信息映射
        result.setId(taskListInfo.getId());
        result.setParentId(taskListInfo.getParentId());
        result.setName(taskListInfo.getName());
        result.setDescription(taskListInfo.getDescription());
        result.setCreateTime(taskListInfo.getCreateTime());
        result.setCreateUserId(taskListInfo.getCreateUserId());
        result.setCreateUserName(taskListInfo.getCreateUserName());
        result.setUpdateTime(taskListInfo.getUpdateTime());
        result.setUpdateUserId(taskListInfo.getUpdateUserId());
        result.setUpdateUserName(taskListInfo.getUpdateUserName());
        
        // 统计任务数量
        Long totalTasks = taskMapper.countByTaskListId(id);
        Long completedTasks = taskMapper.countCompletedByTaskListId(id);
        result.setTotalTasks(totalTasks != null ? totalTasks.intValue() : 0);
        result.setCompletedTasks(completedTasks != null ? completedTasks.intValue() : 0);
        
        return result;
    }

    /**
     * 分页查询任务清单
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListManagementDTO> page(TaskListQuery query) {
        log.info("分页查询任务清单，查询条件：{}", query);
        
        try {
            // 构建分页对象
            Page<TaskListPO> page = new Page<>(query.getPageNo(), query.getPageSize());
            
            // 构建查询条件
            LambdaQueryWrapper<TaskListPO> queryWrapper = new LambdaQueryWrapper<>();
            
            // 逻辑删除条件
            queryWrapper.eq(TaskListPO::getDeleted, DeletedEnum.NOT_DELETED);
            
            // 清单名称模糊查询
            if (StringUtils.hasText(query.getName())) {
                queryWrapper.like(TaskListPO::getName, query.getName());
            }
            
            // 按创建时间倒序
            queryWrapper.orderByDesc(TaskListPO::getCreateTime);
            
            // 执行分页查询
            IPage<TaskListPO> pageResult = taskListMapper.selectPage(page, queryWrapper);
            
            // 转换为DTO并添加统计信息
            List<TaskListManagementDTO> resultList = pageResult.getRecords().stream()
                .map(this::convertToTaskListManagementDTO)
                .collect(Collectors.toList());
            
            log.info("任务清单分页查询成功，总数：{}，当前页数量：{}", pageResult.getTotal(), resultList.size());
            
            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), pageResult.getTotal(), resultList);
            
        } catch (Exception e) {
            log.error("任务清单分页查询失败", e);
            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), 0L, new ArrayList<>());
        }
    }
    
    /**
     * 转换TaskListPO为TaskListManagementDTO并添加统计信息
     *
     * @param taskListPO 任务清单PO
     * @return 任务清单管理DTO
     */
    private TaskListManagementDTO convertToTaskListManagementDTO(TaskListPO taskListPO) {
        TaskListInfo taskListInfo = taskListAssembler.toTaskListInfo(taskListPO);
        
        TaskListManagementDTO result = new TaskListManagementDTO();
        
        // 基础信息映射
        result.setId(taskListInfo.getId());
        result.setParentId(taskListInfo.getParentId());
        result.setName(taskListInfo.getName());
        result.setDescription(taskListInfo.getDescription());
        result.setCreateTime(taskListInfo.getCreateTime());
        result.setCreateUserId(taskListInfo.getCreateUserId());
        result.setCreateUserName(taskListInfo.getCreateUserName());
        result.setUpdateTime(taskListInfo.getUpdateTime());
        result.setUpdateUserId(taskListInfo.getUpdateUserId());
        result.setUpdateUserName(taskListInfo.getUpdateUserName());
        
        // 统计任务数量
        try {
            Long totalTasks = taskMapper.countByTaskListId(taskListInfo.getId());
            Long completedTasks = taskMapper.countCompletedByTaskListId(taskListInfo.getId());
            result.setTotalTasks(totalTasks != null ? totalTasks.intValue() : 0);
            result.setCompletedTasks(completedTasks != null ? completedTasks.intValue() : 0);
        } catch (Exception e) {
            log.warn("统计任务数量失败，任务清单ID：{}", taskListInfo.getId(), e);
            result.setTotalTasks(0);
            result.setCompletedTasks(0);
        }
        
        return result;
    }
}
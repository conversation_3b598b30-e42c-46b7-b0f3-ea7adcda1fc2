package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.FmsTaskProcessTypeEnum;
import cn.july.orch.meeting.enums.FmsTaskTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description FMS任务配置PO
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName("fms_task_config")
public class FmsTaskConfigPO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
    
    /**
     * 任务代码
     */
    @TableField("task_code")
    private String taskCode;
    
    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;
    
    /**
     * 任务类型：1-上传，2-下载
     */
    @TableField("task_type")
    private FmsTaskTypeEnum taskType;
    
    /**
     * 处理方式：1-Excel导出，2-PDF导出，3-CSV导出，9-自定义处理
     */
    @TableField("process_type")
    private FmsTaskProcessTypeEnum processType;
    
    /**
     * 目标类全限定名
     */
    @TableField("target_class")
    private String targetClass;
    
    /**
     * 目标方法名
     */
    @TableField("target_method")
    private String targetMethod;
    
    /**
     * 任务描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("is_enabled")
    private Integer isEnabled;
    
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    
    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    
    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 删除标记
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;
}
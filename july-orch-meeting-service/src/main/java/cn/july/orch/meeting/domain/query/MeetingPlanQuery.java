package cn.july.orch.meeting.domain.query;

import cn.july.core.model.page.PageSortQuery;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议规划查询对象
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingPlanQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "会议规划名称")
    private String planName;

    @ApiModelProperty(value = "会议规划状态")
    private MeetingPlanStatusEnum status;

    @ApiModelProperty(value = "会议标准ID")
    private Long meetingStandardId;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "业务会议ID")
    private Long businessMeetingId;

    @ApiModelProperty(value = "开始时间-起")
    private LocalDateTime startTimeBegin;

    @ApiModelProperty(value = "开始时间-止")
    private LocalDateTime startTimeEnd;

    @ApiModelProperty(value = "创建人ID")
    private String createUserId;
}

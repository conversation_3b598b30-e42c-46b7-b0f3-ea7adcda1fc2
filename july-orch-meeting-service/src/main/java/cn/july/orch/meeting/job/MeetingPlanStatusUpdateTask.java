package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.service.MeetingPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议规划状态更新定时任务
 * @date 2025-01-24
 */
@Slf4j
@Component
public class MeetingPlanStatusUpdateTask {

    @Resource
    private MeetingPlanService meetingPlanService;

    /**
     * 每分钟执行一次，更新逾期状态
     */
    @Scheduled(cron = "0 * * * * ?")
    @IgnoreTenant
    public void updateOverdueStatus() {
        log.info("开始执行会议规划逾期状态更新任务");
        try {
            meetingPlanService.updateOverdueStatus();
            log.info("会议规划逾期状态更新任务执行完成");
        } catch (Exception e) {
            log.error("会议规划逾期状态更新任务执行失败", e);
        }
    }
}

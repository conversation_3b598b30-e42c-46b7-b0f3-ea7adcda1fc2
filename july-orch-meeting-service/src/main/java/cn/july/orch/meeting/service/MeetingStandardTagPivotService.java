package cn.july.orch.meeting.service;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.domain.po.MeetingStandardTagPivotPO;
import cn.july.orch.meeting.mapper.MeetingStandardTagPivotMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标准标签关联服务
 * @date 2025-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingStandardTagPivotService {

    private final MeetingStandardTagPivotMapper pivotMapper;

    /**
     * 根据会议标准ID查询关联的标签ID列表
     */
    public List<Long> getTagIdsByStandardId(Long standardId) {
        return pivotMapper.selectTagIdsByStandardId(standardId);
    }

    /**
     * 更新会议标准的标签关联
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStandardTags(Long standardId, List<Long> tagIds) {
        // 1. 删除原有关联关系
        pivotMapper.deleteByStandardId(standardId);

        // 2. 如果有新的标签ID，则批量插入
        if (tagIds != null && !tagIds.isEmpty()) {
            List<MeetingStandardTagPivotPO> pivots = tagIds.stream()
                .map(tagId -> {
                    MeetingStandardTagPivotPO pivot = new MeetingStandardTagPivotPO();
                    pivot.setStandardId(standardId);
                    pivot.setTagId(tagId);
                    pivot.setCreateTime(LocalDateTime.now());
                    pivot.setUpdateTime(LocalDateTime.now());
                    pivot.setDeleted(DeletedEnum.NOT_DELETED);
                    return pivot;
                })
                .collect(Collectors.toList());

            pivotMapper.batchInsert(pivots);
        }
    }

    /**
     * 删除会议标准的所有标签关联
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByStandardId(Long standardId) {
        pivotMapper.deleteByStandardId(standardId);
    }

    /**
     * 检查标签是否被会议标准使用
     */
    public boolean isTagUsedByStandard(Long tagId) {
        LambdaQueryWrapper<MeetingStandardTagPivotPO> wrapper = Wrappers.lambdaQuery(MeetingStandardTagPivotPO.class);
        wrapper.eq(MeetingStandardTagPivotPO::getTagId, tagId);
        return pivotMapper.selectCount(wrapper) > 0;
    }

    /**
     * 获取使用指定标签的会议标准数量
     */
    public long countStandardsByTagId(Long tagId) {
        LambdaQueryWrapper<MeetingStandardTagPivotPO> wrapper = Wrappers.lambdaQuery(MeetingStandardTagPivotPO.class);
        wrapper.eq(MeetingStandardTagPivotPO::getTagId, tagId);
        return pivotMapper.selectCount(wrapper);
    }
}
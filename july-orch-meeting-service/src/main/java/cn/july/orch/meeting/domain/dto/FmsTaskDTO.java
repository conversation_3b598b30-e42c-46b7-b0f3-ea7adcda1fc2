package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description FMS任务DTO
 */
@Data
@ApiModel("FMS任务DTO")
public class FmsTaskDTO {
    
    @ApiModelProperty("任务ID")
    private Long id;
    
    @ApiModelProperty("任务代码")
    private String taskCode;
    
    @ApiModelProperty("任务名称")
    private String taskName;
    
    @ApiModelProperty("任务类型：1-上传，2-下载")
    private Integer taskType;
    
    @ApiModelProperty("处理方式：1-Excel导出，2-PDF导出，3-CSV导出，9-自定义处理")
    private Integer processType;
    
    @ApiModelProperty("任务状态：0-未开始，1-执行中，2-成功，3-失败")
    private Integer taskStatus;
    
    @ApiModelProperty("任务参数")
    private String taskParams;
    
    @ApiModelProperty("文件ID")
    private String fileId;
    
    @ApiModelProperty("文件名称")
    private String fileName;
    
    @ApiModelProperty("文件URL")
    private String fileUrl;
    
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;
    
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
    
    @ApiModelProperty("创建人ID")
    private String createUserId;
    
    @ApiModelProperty("创建人姓名")
    private String createUserName;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}

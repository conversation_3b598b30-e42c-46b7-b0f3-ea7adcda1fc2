package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.command.TaskListCreateCommand;
import cn.july.orch.meeting.domain.command.TaskListUpdateCommand;
import cn.july.orch.meeting.domain.entity.TaskListAgg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 任务清单操作服务
 */
@Slf4j
@Service
public class TaskListActionService {

    @Resource
    private TaskListDomainService taskListDomainService;

    /**
     * 创建任务清单
     *
     * @param command 创建命令
     * @return 任务清单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TaskListCreateCommand command) {
        log.info("创建任务清单，名称：{}", command.getName());
        
        return taskListDomainService.create(command.getName(), command.getDescription(), command.getParentId());
    }

    /**
     * 更新任务清单
     *
     * @param command 更新命令
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskListUpdateCommand command) {
        log.info("更新任务清单，ID：{}，名称：{}", command.getId(), command.getName());
        
        taskListDomainService.update(command.getId(), command.getName(), command.getDescription(), command.getParentId());
    }

    /**
     * 删除任务清单
     *
     * @param id 任务清单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.info("删除任务清单，ID：{}", id);
        
        taskListDomainService.delete(id);
    }
}
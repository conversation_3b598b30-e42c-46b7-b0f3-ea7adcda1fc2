package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingCheckInPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 参会人签到信息Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingCheckInMapper extends BaseMapper<MeetingCheckInPO> {

    /**
     * 根据会议ID查询签到信息
     */
    List<MeetingCheckInPO> findByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据签到码查询签到信息
     */
    MeetingCheckInPO findByCheckinCode(@Param("checkinCode") String checkinCode);

    /**
     * 根据会议ID和参会人open_id查询签到信息
     */
    MeetingCheckInPO findByMeetingIdAndAttendeeOpenId(@Param("meetingId") Long meetingId, 
                                                     @Param("attendeeOpenId") String attendeeOpenId);

    /**
     * 批量插入签到信息
     */
    int batchInsert(@Param("list") List<MeetingCheckInPO> list);
}

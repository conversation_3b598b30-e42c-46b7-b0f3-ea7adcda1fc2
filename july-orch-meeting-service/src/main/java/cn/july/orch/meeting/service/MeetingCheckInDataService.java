package cn.july.orch.meeting.service;

import cn.hutool.core.util.StrUtil;
import cn.july.orch.meeting.assembler.MeetingCheckInConverter;
import cn.july.orch.meeting.domain.dto.CheckInConfigDTO;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.entity.MeetingCheckIn;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.MeetingCheckInPO;
import cn.july.orch.meeting.mapper.MeetingCheckInMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议签到数据服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingCheckInDataService {

    private final MeetingCheckInMapper meetingCheckInMapper;
    private final MeetingCheckInConverter meetingCheckInConverter;
    private final CheckInCodeService checkInCodeService;
    private final UserInfoQueryService userInfoQueryService;
    private final TenantFeishuAppClient tenantFeishuAppClient;
    private final NewMeetingQueryService newMeetingQueryService;
    private final MeetingStandardService meetingStandardService;

    /**
     * 为会议创建参会人签到数据（支持重复发送和参会人员增量调整）
     *
     * @param meeting     会议信息
     * @param checkinCode 签到码
     */
    public void createCheckInData(NewMeeting meeting, String checkinCode) {
        if (CollectionUtils.isEmpty(meeting.getAttendees())) {
            log.warn("会议没有参会人员，跳过创建签到数据: meetingId={}", meeting.getId());
            return;
        }

        // 获取参会人员信息
        Map<String, cn.july.orch.meeting.domain.dto.FSUserInfoDTO> userInfoMap =
                userInfoQueryService.getUserInfos(meeting.getAttendees());

        // 查询当前会议的所有签到记录
        List<MeetingCheckInPO> existingCheckIns = meetingCheckInMapper.selectList(
                Wrappers.lambdaQuery(MeetingCheckInPO.class)
                        .eq(MeetingCheckInPO::getMeetingId, meeting.getId())
        );

        // 创建现有参会人的OpenID集合
        Set<String> existingAttendeeOpenIds = existingCheckIns.stream()
                .map(MeetingCheckInPO::getAttendeeOpenId)
                .collect(Collectors.toSet());

        List<MeetingCheckIn> newCheckInList = new ArrayList<>();
        List<MeetingCheckInPO> updateCheckInList = new ArrayList<>();

        for (String attendeeOpenId : meeting.getAttendees()) {
            cn.july.orch.meeting.domain.dto.FSUserInfoDTO userInfo = userInfoMap.get(attendeeOpenId);
            String attendeeName = userInfo != null ? userInfo.getName() : "未知用户";

            if (existingAttendeeOpenIds.contains(attendeeOpenId)) {
                // 如果已存在，更新签到码和用户信息
                MeetingCheckInPO existingCheckIn = existingCheckIns.stream()
                        .filter(ci -> attendeeOpenId.equals(ci.getAttendeeOpenId()))
                        .findFirst()
                        .orElse(null);

                if (existingCheckIn != null) {
                    existingCheckIn.setCheckinCode(checkinCode);
                    existingCheckIn.setAttendeeName(attendeeName);
                    existingCheckIn.setUpdateTime(LocalDateTime.now());
                    updateCheckInList.add(existingCheckIn);
                    log.debug("更新已存在的签到记录: meetingId={}, attendeeOpenId={}, attendeeName={}",
                            meeting.getId(), attendeeOpenId, attendeeName);
                }
            } else {
                // 创建新的签到记录
                MeetingCheckIn checkIn = MeetingCheckIn.builder()
                        .meetingId(meeting.getId())
                        .attendeeOpenId(attendeeOpenId)
                        .attendeeName(attendeeName)
                        .checkinCode(checkinCode)
                        .checkinStatus(0) // 未签到
                        .createTime(LocalDateTime.now())
                        .updateTime(LocalDateTime.now())
                        .build();

                newCheckInList.add(checkIn);
                log.debug("创建新的签到记录: meetingId={}, attendeeOpenId={}, attendeeName={}",
                        meeting.getId(), attendeeOpenId, attendeeName);
            }
        }

        // 批量更新已存在的签到记录
        if (!updateCheckInList.isEmpty()) {
            for (MeetingCheckInPO updateCheckIn : updateCheckInList) {
                meetingCheckInMapper.updateById(updateCheckIn);
            }
            log.info("更新已存在的签到记录: meetingId={}, updateCount={}", meeting.getId(), updateCheckInList.size());
        }

        // 批量插入新的签到数据
        if (!newCheckInList.isEmpty()) {
            List<MeetingCheckInPO> poList = newCheckInList.stream()
                    .map(meetingCheckInConverter::toPO)
                    .collect(Collectors.toList());

            meetingCheckInMapper.batchInsert(poList);
            log.info("为会议创建新签到数据: meetingId={}, newCount={}", meeting.getId(), newCheckInList.size());
        }

        // 检查是否有参会人员被移除（不在当前参会人员列表中）
        Set<String> currentAttendeeOpenIds = new HashSet<>(meeting.getAttendees());
        List<MeetingCheckInPO> removedCheckIns = existingCheckIns.stream()
                .filter(ci -> !currentAttendeeOpenIds.contains(ci.getAttendeeOpenId()))
                .collect(Collectors.toList());

        if (!removedCheckIns.isEmpty()) {
            // 删除被移除参会人员的签到记录
            List<Long> removedIds = removedCheckIns.stream()
                    .map(MeetingCheckInPO::getId)
                    .collect(Collectors.toList());
            meetingCheckInMapper.deleteBatchIds(removedIds);
            log.info("删除被移除参会人员的签到记录: meetingId={}, removedCount={}", meeting.getId(), removedCheckIns.size());
        }

        log.info("为会议创建签到数据完成: meetingId={}, totalAttendeeCount={}, newCount={}, updateCount={}, removedCount={}",
                meeting.getId(), meeting.getAttendees().size(), newCheckInList.size(), updateCheckInList.size(), removedCheckIns.size());
    }

    /**
     * 根据签到码签到（旧方法，保留兼容性）
     *
     * @param checkinCode    签到码
     * @param attendeeOpenId 参会人open_id
     * @return 签到结果信息
     */
    public String checkIn(String checkinCode, String attendeeOpenId) {
        // 这个方法现在不再使用，因为我们已经改为通过会议ID验证
        return "此方法已废弃，请使用checkInByMeetingId方法";
    }

    /**
     * 根据会议ID和签到码签到
     *
     * @param meetingId      会议ID
     * @param checkinCode    签到码
     * @param attendeeOpenId 参会人open_id
     * @return 签到结果信息
     */
    public String checkInByMeetingId(Long meetingId, String checkinCode, String attendeeOpenId) {
        String message;

        // 通过会议ID获取正确的签到码
        String correctCheckinCode = checkInCodeService.getCheckInCodeByMeetingId(meetingId);
        if (StrUtil.isBlank(correctCheckinCode)) {
            log.warn("会议签到码不存在或已过期: meetingId={}", meetingId);
            message = "❌ 会议签到码不存在或已过期，请联系会议组织者";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }

        // 验证用户输入的签到码是否正确
        if (!correctCheckinCode.equals(checkinCode)) {
            log.warn("签到码不正确: meetingId={}, inputCode={}, correctCode={}", meetingId, checkinCode, correctCheckinCode);
            message = "❌ 签到码不正确，请检查后重试";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }

        // 查询签到记录
        MeetingCheckInPO checkInPO = meetingCheckInMapper.findByMeetingIdAndAttendeeOpenId(meetingId, attendeeOpenId);
        if (checkInPO == null) {
            log.warn("未找到对应的签到记录: meetingId={}, attendeeOpenId={}", meetingId, attendeeOpenId);
            message = "⚠️ 您不在该会议的参会人员列表中，无法签到";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }

        // 检查是否已签到
        if (checkInPO.getCheckinStatus() == 1) {
            log.warn("用户已签到: meetingId={}, attendeeOpenId={}", meetingId, attendeeOpenId);
            message = "ℹ️ 您已经签到过了，无需重复签到";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }

        // 检查签到截止时间
        String timeCheckResult = checkCheckInTimeLimit(meetingId);
        if (timeCheckResult != null) {
            log.warn("签到时间校验失败: meetingId={}, attendeeOpenId={}, reason={}", meetingId, attendeeOpenId, timeCheckResult);
            message = timeCheckResult;
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }

        // 更新签到状态
        checkInPO.setCheckinStatus(1);
        checkInPO.setCheckinTime(LocalDateTime.now());
        checkInPO.setUpdateTime(LocalDateTime.now());

        int updateCount = meetingCheckInMapper.updateById(checkInPO);
        if (updateCount > 0) {
            log.info("用户签到成功: meetingId={}, attendeeOpenId={}, checkinCode={}",
                    meetingId, attendeeOpenId, checkinCode);
            message = "✅ 签到成功！欢迎参加本次会议 🎉";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        } else {
            log.error("更新签到状态失败: meetingId={}, attendeeOpenId={}", meetingId, attendeeOpenId);
            message = "❌ 签到失败，请稍后重试";
            sendCheckInResultMessage(attendeeOpenId, message);
            return message;
        }
    }

    /**
     * 检查签到截止时间
     *
     * @param meetingId 会议ID
     * @return 如果签到时间已过，返回错误消息；否则返回null
     */
    private String checkCheckInTimeLimit(Long meetingId) {
        try {
            // 1. 查询会议信息
            NewMeetingDTO meetingDTO = newMeetingQueryService.getById(meetingId);
            if (meetingDTO == null) {
                log.warn("会议不存在: meetingId={}", meetingId);
                return "❌ 会议不存在，无法签到";
            }

            // 2. 获取会议开始时间（优先取实际开始时间，否则取计划开始时间）
            LocalDateTime meetingStartTime = meetingDTO.getActualStartTime();
            if (meetingStartTime == null) {
                meetingStartTime = meetingDTO.getStartTime();
            }

            if (meetingStartTime == null) {
                log.warn("会议开始时间不存在: meetingId={}", meetingId);
                return "❌ 会议开始时间不存在，无法签到";
            }

            // 3. 查询会议标准的签到配置
            if (meetingDTO.getMeetingStandardId() == null) {
                log.info("会议没有关联会议标准，跳过签到时间校验: meetingId={}", meetingId);
                return null; // 没有会议标准，不进行时间校验
            }

            MeetingStandardDTO standardDTO = meetingStandardService.getById(meetingDTO.getMeetingStandardId());
            if (standardDTO == null) {
                log.warn("会议标准不存在: meetingId={}, standardId={}", meetingId, meetingDTO.getMeetingStandardId());
                return null; // 会议标准不存在，不进行时间校验
            }

            CheckInConfigDTO checkInConfig = standardDTO.getCheckInConfig();
            if (checkInConfig == null || !Boolean.TRUE.equals(checkInConfig.getEnabled())) {
                log.info("会议标准未启用签到功能，跳过签到时间校验: meetingId={}", meetingId);
                return null; // 未启用签到，不进行时间校验
            }

            // 4. 检查签到截止时间
            Integer endMinutesAfter = checkInConfig.getEndMinutesAfter();
            if (endMinutesAfter == null || endMinutesAfter <= 0) {
                log.info("会议标准未配置签到截止时间，跳过时间校验: meetingId={}", meetingId);
                return null; // 未配置截止时间，不进行校验
            }

            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime checkInDeadline = meetingStartTime.plusMinutes(endMinutesAfter);

            if (currentTime.isAfter(checkInDeadline)) {
                log.warn("签到时间已过: meetingId={}, meetingStartTime={}, checkInDeadline={}, currentTime={}",
                        meetingId, meetingStartTime, checkInDeadline, currentTime);
                return "⏰ 签到时间已过，会议开始后" + endMinutesAfter + "分钟内可签到，请下次准时参加";
            }

            log.info("签到时间校验通过: meetingId={}, meetingStartTime={}, checkInDeadline={}, currentTime={}",
                    meetingId, meetingStartTime, checkInDeadline, currentTime);
            return null; // 时间校验通过

        } catch (Exception e) {
            log.error("检查签到截止时间失败: meetingId={}", meetingId, e);
            return null; // 发生异常时，不阻止签到
        }
    }

    /**
     * 发送签到结果消息给用户
     *
     * @param attendeeOpenId 参会人open_id
     * @param message        消息内容
     */
    private void sendCheckInResultMessage(String attendeeOpenId, String message) {
        try {
            tenantFeishuAppClient.getRobotService().sendText(attendeeOpenId, message);
            log.info("签到结果消息发送成功: attendeeOpenId={}, message={}", attendeeOpenId, message);
        } catch (Exception e) {
            log.error("发送签到结果消息失败: attendeeOpenId={}, message={}", attendeeOpenId, message, e);
        }
    }

}

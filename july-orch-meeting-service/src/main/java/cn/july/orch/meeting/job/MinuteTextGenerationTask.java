package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.domain.po.MeetingMinutePO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.MeetingMinuteMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.service.NewMeetingDomainService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 妙计视频转文字定时任务
 * @date 2025-01-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MinuteTextGenerationTask {

    private final NewMeetingMapper newMeetingMapper;
    private final MeetingMinuteMapper meetingMinuteMapper;
    private final NewMeetingDomainService newMeetingDomainService;

    /**
     * 妙计视频转文字定时任务 - 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000) // 10分钟 = 600000毫秒
    @IgnoreTenant
    public void generateMinuteText() {
        log.info("开始执行妙计视频转文字定时任务");
        
        try {
            // 查询已结束且有妙计链接但未生成文本的会议
            List<NewMeetingPO> meetingsToProcess = findMeetingsForTextGeneration();
            
            log.info("找到{}个需要生成文本的会议", meetingsToProcess.size());
            
            for (NewMeetingPO meeting : meetingsToProcess) {
                try {
                    log.info("开始处理会议妙计视频转文字，会议ID：{}，妙计链接：{}", 
                        meeting.getId(), meeting.getMinuteUrl());
                    
                    // 执行妙计视频转文字
                    newMeetingDomainService.getMinuteText(
                        meeting.getId(), 
                        meeting.getHostUserId(), 
                        meeting.getMinuteUrl()
                    );
                    
                    log.info("会议妙计视频转文字处理成功，会议ID：{}", meeting.getId());
                    
                } catch (Exception e) {
                    // 忽略报错内容，只记录日志
                    log.warn("会议妙计视频转文字处理失败，会议ID：{}，错误：{}", 
                        meeting.getId(), e.getMessage());
                }
            }
            
            log.info("妙计视频转文字定时任务执行完成，处理了{}个会议", meetingsToProcess.size());
            
        } catch (Exception e) {
            log.error("妙计视频转文字定时任务执行异常", e);
        }
    }

    /**
     * 查询已结束且有妙计链接但未生成文本的会议
     */
    private List<NewMeetingPO> findMeetingsForTextGeneration() {
        // 查询已结束且有妙计链接的会议
        LambdaQueryWrapper<NewMeetingPO> meetingWrapper = new LambdaQueryWrapper<>();
        meetingWrapper.eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.ENDED.getCode())
                     .isNotNull(NewMeetingPO::getMinuteUrl)
                     .ne(NewMeetingPO::getMinuteUrl, "")
                     .isNotNull(NewMeetingPO::getHostUserId) // 确保有主持人ID
                     .ne(NewMeetingPO::getHostUserId, "") // 主持人ID不为空
                     .orderByDesc(NewMeetingPO::getEndTime);
        
        List<NewMeetingPO> endedMeetingsWithMinute = newMeetingMapper.selectList(meetingWrapper);
        
        // 批量查询已生成文本的会议ID
        List<Long> meetingIds = endedMeetingsWithMinute.stream()
            .map(NewMeetingPO::getId)
            .collect(Collectors.toList());
        
        if (meetingIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 批量查询已生成文本的会议ID
        List<Long> existingMinuteMeetingIds = findExistingMinuteMeetingIds(meetingIds);
        
        // 过滤掉已经生成文本的会议
        return endedMeetingsWithMinute.stream()
            .filter(meeting -> !existingMinuteMeetingIds.contains(meeting.getId()))
            .collect(Collectors.toList());
    }

    /**
     * 批量查询已生成文本的会议ID
     */
    private List<Long> findExistingMinuteMeetingIds(List<Long> meetingIds) {
        if (meetingIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<MeetingMinutePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MeetingMinutePO::getMeetingId, meetingIds)
               .isNotNull(MeetingMinutePO::getMinuteText)
               .ne(MeetingMinutePO::getMinuteText, "");
        
        return meetingMinuteMapper.selectList(wrapper).stream()
            .map(MeetingMinutePO::getMeetingId)
            .collect(Collectors.toList());
    }

    /**
     * 手动触发妙计视频转文字（用于测试和调试）
     */
    public void manualGenerateMinuteText() {
        log.info("手动触发妙计视频转文字任务");
        generateMinuteText();
    }
}

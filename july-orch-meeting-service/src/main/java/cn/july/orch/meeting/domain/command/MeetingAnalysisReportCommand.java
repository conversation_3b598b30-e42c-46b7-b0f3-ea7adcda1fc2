package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.enums.ReportStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告命令
 */
@Data
@ApiModel("会议分析报告命令")
public class MeetingAnalysisReportCommand {

    @NotNull(message = "会议ID不能为空")
    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    @Min(value = 0, message = "综合评分必须在0-100之间")
    @Max(value = 100, message = "综合评分必须在0-100之间")
    @ApiModelProperty("综合评分 (0-100)")
    private Integer overallScore;

    @ApiModelProperty("综合评分的描述文字")
    private String overallSummary;

    @ApiModelProperty("包含内容质量各项评分和AI分析摘要的JSON对象")
    private String contentAnalysisJson;

    @ApiModelProperty("包含所有AI优化建议的JSON数组")
    private String aiSuggestionsJson;

    @ApiModelProperty("报告状态")
    private ReportStatusEnum status;

    @ApiModelProperty("AI纪要状态")
    private ReportStatusEnum aiTranscriptStatus;

    @ApiModelProperty("AI生成的会议纪要全文 (Markdown格式)")
    private String aiTranscriptMd;
}
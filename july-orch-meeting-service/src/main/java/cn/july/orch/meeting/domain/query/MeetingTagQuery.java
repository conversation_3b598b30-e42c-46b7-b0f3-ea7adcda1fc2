package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会议标签查询Query
 * @date 2025-08-26
 */
@Data
@ApiModel("会议标签查询请求")
public class MeetingTagQuery {

    @ApiModelProperty("标签名称(支持模糊查询)")
    private String name;

    @ApiModelProperty("标签颜色")
    private String color;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;
}
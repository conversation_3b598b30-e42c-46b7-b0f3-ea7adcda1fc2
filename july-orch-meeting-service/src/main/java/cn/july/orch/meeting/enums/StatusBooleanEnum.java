package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum StatusBooleanEnum {
    FALSE(false, "停用"),
    TRUE(true, "启用");


    @EnumValue
    @JsonValue
    private final boolean code;

    private final String description;

    StatusBooleanEnum(boolean code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Boolean, StatusBooleanEnum> VALUES = new HashMap<>();

    static {
        for (final StatusBooleanEnum item : StatusBooleanEnum.values()) {
            VALUES.put(item.code, item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static StatusBooleanEnum of(boolean code) {
        return VALUES.get(code);
    }
}

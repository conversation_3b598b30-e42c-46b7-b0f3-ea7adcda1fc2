package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 会议类型枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum MeetingTypeEnum {

    TEMPORARY(0, "临时会议"),
    PLANNED(1, "规划性会议");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, MeetingTypeEnum> VALUES = new HashMap<>();

    static {
        for (final MeetingTypeEnum item : MeetingTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MeetingTypeEnum of(int code) {
        return VALUES.get(code);
    }
}

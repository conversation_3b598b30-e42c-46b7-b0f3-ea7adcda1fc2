package cn.july.orch.meeting.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 支持JSR310的Map类型处理器，用于替换MyBatis-Plus默认的JacksonTypeHandler
 */
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(Map.class)
public class CustomMapTypeHandler extends BaseTypeHandler<Map<String, Object>> {

    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
                log.debug("序列化Map成功，内容：{}", json);
            } catch (JsonProcessingException e) {
                log.error("Map序列化失败", e);
                ps.setString(i, null);
            }
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseMap(value);
    }

    @Override
    public Map<String, Object> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseMap(value);
    }

    @Override
    public Map<String, Object> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseMap(value);
    }

    private Map<String, Object> parseMap(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            Map<String, Object> result = objectMapper.readValue(value, new TypeReference<Map<String, Object>>() {});
            log.debug("反序列化Map成功，内容：{}", result);
            return result;
        } catch (JsonProcessingException e) {
            log.error("Map反序列化失败，原始值：{}", value, e);
            return new HashMap<>();
        }
    }
}
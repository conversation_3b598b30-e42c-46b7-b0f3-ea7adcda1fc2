package cn.july.orch.meeting.service;

import cn.july.feishu.config.FeishuAppContext;
import cn.july.feishu.model.*;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.domain.CreateCalendarEventResult;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import com.lark.oapi.service.calendar.v4.model.CreateCalendarEventRespBody;
import com.lark.oapi.service.calendar.v4.model.UserCalendar;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 飞书日历操作服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuCalendarActionService {

    private final TenantFeishuAppClient tenantFeishuAppClient;
    private final NewMeetingDomainService newMeetingDomainService;

    /**
     * 创建飞书日历事件
     */
    public CreateCalendarEventResult createCalendarEvent(NewMeeting meeting) {
        log.info("创建飞书日历事件，会议名称：{}", meeting.getMeetingName());

        // 获取主日历ID
        String primaryCalendarId = getPrimaryCalendarId();

        // 从登录用户上下文中获取用户信息
        String currentUserOpenId = CurrentUserHolder.getOpenId();
        String currentUserName = CurrentUserHolder.getCurrentUser().getName();

        // 构建创建日历事件请求
        CreateCalendarEventModel createCalendarEventModel = CreateCalendarEventModel.builder()
                .summary(meeting.getMeetingName())
                .description(meeting.getMeetingDescription())
                .calendarId(primaryCalendarId)
                .startTime(meeting.getStartTime())
                .endTime(meeting.getEndTime())
                .build();

        // 设置组织者信息（从登录用户上下文获取）
        createCalendarEventModel.setOrganizerId(currentUserOpenId);
        createCalendarEventModel.setOrganizerName(currentUserName);

        // 调用飞书API创建日历事件
        CreateCalendarEventRespBody createCalendarEventRespBody = tenantFeishuAppClient.getCalendarEventService().create(createCalendarEventModel);

        // 从返回值中获取eventId和meetingUrl
        String eventId = createCalendarEventRespBody.getEvent().getEventId();
        String meetingUrl = createCalendarEventRespBody.getEvent().getVchat().getMeetingUrl();

        log.info("飞书日历事件创建成功，事件ID：{}，会议链接：{}，组织者：{}", eventId, meetingUrl, currentUserName);

        return CreateCalendarEventResult.builder()
                .eventId(eventId)
                .meetingUrl(meetingUrl)
                .build();
    }

    /**
     * 更新飞书日历事件
     */
    public void updateCalendarEvent(NewMeeting meeting) {
        log.info("更新飞书日历事件，会议ID：{}", meeting.getId());

        // 获取主日历ID
        String primaryCalendarId = getPrimaryCalendarId();

        // 构建更新日历事件请求
        UpdateCalendarEventModel updateCalendarEventModel = UpdateCalendarEventModel.builder()
                .calendarId(primaryCalendarId)
                .eventId(meeting.getFsCalendarEventId())
                .summary(meeting.getMeetingName())
                .description(meeting.getMeetingDescription())
                .startTime(meeting.getStartTime())
                .endTime(meeting.getEndTime())
                .build();

        // 调用飞书API更新日历事件
        tenantFeishuAppClient.getCalendarEventService().update(updateCalendarEventModel);

        log.info("飞书日历事件更新成功，会议ID：{}", meeting.getId());
    }

    /**
     * 更新飞书日历事件的描述信息，同时更新系统内的会议描述
     */
    public void updateCalendarEventDescription(String fsCalendarEventId, String description, String userAccessToken) {
        log.info("更新飞书日历事件描述，事件ID：{}", fsCalendarEventId);

        // 临时设置 userAccessToken 到 FeishuAppContext 中
        FeishuAppContext originalContext = FeishuAppContext.get();
        FeishuAppContext tempContext = new FeishuAppContext();
        tempContext.setUserAccessToken(userAccessToken);
        FeishuAppContext.set(tempContext);

        try {
            // 获取主日历ID
            String primaryCalendarId = getPrimaryCalendarId();

            // 构建更新日历事件请求（只更新描述）
            UpdateCalendarEventModel updateCalendarEventModel = UpdateCalendarEventModel.builder()
                    .calendarId(primaryCalendarId)
                    .eventId(fsCalendarEventId)
                    .description(description)
                    .build();

            // 调用飞书API更新日历事件描述
            tenantFeishuAppClient.getCalendarEventService().updateDescription(updateCalendarEventModel);

            // 同时更新系统内的会议描述
            NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
            if (meeting != null) {
                newMeetingDomainService.updateMeetingDescription(meeting.getId(), description);
                log.info("系统内会议描述更新成功，会议ID：{}", meeting.getId());
            } else {
                log.warn("未找到对应的系统内会议，无法更新会议描述，飞书事件ID：{}", fsCalendarEventId);
            }

            log.info("飞书日历事件描述更新成功，事件ID：{}", fsCalendarEventId);
        } finally {
            // 恢复原始的 FeishuAppContext
            if (originalContext != null) {
                FeishuAppContext.set(originalContext);
            } else {
                FeishuAppContext.remove();
            }
        }
    }

    /**
     * 删除飞书日历事件
     */
    public void deleteCalendarEvent(String fsCalendarEventId) {
        log.info("删除飞书日历事件，事件ID：{}", fsCalendarEventId);

        // 获取主日历ID
        String primaryCalendarId = getPrimaryCalendarId();

        // 构建删除日历事件请求
        CalendarEventModel calendarEventModel = CalendarEventModel.builder()
                .calendarId(primaryCalendarId)
                .eventId(fsCalendarEventId)
                .build();

        // 调用飞书API删除日历事件
        tenantFeishuAppClient.getCalendarEventService().delete(calendarEventModel);

        log.info("飞书日历事件删除成功，事件ID：{}", fsCalendarEventId);
    }

    /**
     * 为日历事件添加参会人员
     */
    public void addCalendarEventAttendees(String fsCalendarEventId, List<String> attendeeIds, String organizerId) {
        log.info("为日历事件添加参会人员，事件ID：{}，参会人员数量：{}", fsCalendarEventId, attendeeIds.size());

        if (attendeeIds.isEmpty()) {
            return;
        }

        // 获取主日历ID
        String primaryCalendarId = getPrimaryCalendarId();

        // 构建参会人员模型
        List<AttendUserModel> attendUserModels = attendeeIds.stream()
                .map(userId -> AttendUserModel.builder()
                        .userId(userId)
                        .isOrganizer(organizerId != null && organizerId.equals(userId))
                        .build())
                .collect(Collectors.toList());

        // 构建添加参会人员请求
        CreateCalendarEventAttendeesModel createCalendarEventAttendeesModel = CreateCalendarEventAttendeesModel.builder()
                .calendarId(primaryCalendarId)
                .eventId(fsCalendarEventId)
                .attendUsers(attendUserModels)
                .build();

        // 调用飞书API添加参会人员
        tenantFeishuAppClient.getCalendarEventService().createAttendees(createCalendarEventAttendeesModel);

        log.info("飞书日历事件参会人员添加成功，事件ID：{}", fsCalendarEventId);
    }

    /**
     * 从日历事件删除参会人员
     */
    public void deleteCalendarEventAttendees(String fsCalendarEventId, List<String> attendeeIds) {
        log.info("从日历事件删除参会人员，事件ID：{}，参会人员数量：{}", fsCalendarEventId, attendeeIds.size());

        if (attendeeIds.isEmpty()) {
            return;
        }

        // 获取主日历ID
        String primaryCalendarId = getPrimaryCalendarId();

        // 构建删除参会人员请求
        DeleteCalendarEventAttendeesModel deleteCalendarEventAttendeesModel = DeleteCalendarEventAttendeesModel.builder()
                .calendarId(primaryCalendarId)
                .eventId(fsCalendarEventId)
                .userIds(attendeeIds)
                .build();

        // 调用飞书API删除参会人员
        tenantFeishuAppClient.getCalendarEventService().deleteAttendees(deleteCalendarEventAttendeesModel);

        log.info("飞书日历事件参会人员删除成功，事件ID：{}", fsCalendarEventId);
    }

//    /**
//     * 获取会议ID
//     * 注意：这个方法在创建日程时可能无法获取到会议ID，因为会议还未开始
//     * 会议ID应该通过飞书回调事件获取
//     */
//    public String getMeetingId(String fsCalendarEventId) {
//        log.info("获取飞书会议ID，日程事件ID：{}", fsCalendarEventId);
//
//        try {
//            // 获取主日历ID
//            String primaryCalendarId = getPrimaryCalendarId();
//
//            // 构建获取日历事件请求
//            CalendarEventModel calendarEventModel = CalendarEventModel.builder()
//                .calendarId(primaryCalendarId)
//                .eventId(fsCalendarEventId)
//                .build();
//
//            // 调用飞书API获取日历事件详情
//            GetCalendarEventRespBody calendarEventRespBody = feishuAppClient.getCalendarEvent(calendarEventModel);
//
//            // 检查是否有vchat信息
//            if (calendarEventRespBody.getEvent().getVchat() != null) {
//                String fsMeetingId = calendarEventRespBody.getEvent().getVchat().getMeetingId();
//                log.info("获取飞书会议ID成功，会议ID：{}", fsMeetingId);
//                return fsMeetingId;
//            } else {
//                log.warn("日历事件中未找到vchat信息，可能会议还未开始，日程事件ID：{}", fsCalendarEventId);
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("获取飞书会议ID失败，日程事件ID：{}", fsCalendarEventId, e);
//            return null;
//        }
//    }

    /**
     * 获取会议链接
     */
    public String getMeetingUrl(String fsMeetingId) {
        log.info("获取飞书会议链接，会议ID：{}", fsMeetingId);

        // 根据会议ID生成会议链接
        String meetingUrl = "https://meetings.feishu.cn/" + fsMeetingId;

        log.info("获取飞书会议链接成功，链接：{}", meetingUrl);
        return meetingUrl;
    }

    /**
     * 获取主日历ID
     */
    public String getPrimaryCalendarId() {
        UserCalendar userCalendar = tenantFeishuAppClient.getCalendarService().getUserMainInfo();
        if (userCalendar == null) {
            throw new RuntimeException("获取用户主日历信息失败");
        }
        return userCalendar.getCalendar().getCalendarId();
    }

}

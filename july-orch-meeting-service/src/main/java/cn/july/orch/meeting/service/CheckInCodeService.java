package cn.july.orch.meeting.service;

import cn.july.orch.meeting.common.CacheConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;

/**
 * <AUTHOR>
 * @description 签到码服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CheckInCodeService {

    private final StringRedisTemplate stringRedisTemplate;
    private final SecureRandom secureRandom = new SecureRandom();

    private static final Duration CODE_EXPIRE_TIME = Duration.ofDays(1);

    /**
     * 生成6位数字签到码
     */
    public String generateCheckInCode() {
        int code = secureRandom.nextInt(900000) + 100000; // 生成100000-999999之间的数字
        return String.valueOf(code);
    }

    /**
     * 存储签到码到缓存
     * @param meetingId 会议ID
     * @param checkinCode 签到码
     */
    public void storeCheckInCode(Long meetingId, String checkinCode) {
        String key = CacheConstants.getMeetingCheckinCodeByMeetingId(meetingId);
        stringRedisTemplate.opsForValue().set(key, checkinCode, CODE_EXPIRE_TIME);
        log.info("签到码已存储到缓存: meetingId={}, checkinCode={}", meetingId, checkinCode);
    }

    /**
     * 从缓存获取会议对应的签到码
     * @param meetingId 会议ID
     * @return 签到码，如果不存在返回null
     */
    public String getCheckInCodeByMeetingId(Long meetingId) {
        String key = CacheConstants.getMeetingCheckinCodeByMeetingId(meetingId);
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 删除签到码缓存
     * @param meetingId 会议ID
     */
    public void removeCheckInCode(Long meetingId) {
        String key = CacheConstants.getMeetingCheckinCodeByMeetingId(meetingId);
        stringRedisTemplate.delete(key);
        log.info("签到码已从缓存删除: meetingId={}", meetingId);
    }

    /**
     * 检查会议签到码是否存在
     * @param meetingId 会议ID
     * @return 是否存在
     */
    public boolean existsCheckInCode(Long meetingId) {
        String key = CacheConstants.getMeetingCheckinCodeByMeetingId(meetingId);
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(key));
    }
}

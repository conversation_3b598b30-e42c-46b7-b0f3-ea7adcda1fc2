package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划数据洞察查询对象
 * @date 2025-01-24
 */
@Data
public class MeetingPlanInsightQuery implements Serializable {

    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否使用模拟数据", example = "false")
    private Boolean useMockData = false;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty("部门ID列表（用于查询部门及其子部门）")
    private List<String> departmentIds;
}

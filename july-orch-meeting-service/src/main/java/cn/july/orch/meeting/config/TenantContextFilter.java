package cn.july.orch.meeting.config;

import cn.hutool.core.util.StrUtil;
import cn.july.core.context.BaseRequestContext;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.config.JulyRequestContext;
import cn.july.orch.meeting.config.TenantContext;
import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.common.Constants;
import cn.july.orch.meeting.exception.AuthException;
import cn.july.orch.meeting.service.SsoActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 多租户上下文过滤器
 * 专门用于设置多租户上下文，与登录认证过滤器分离
 * 即使接口在SsoAuthFilter的排除列表中，也能正确设置租户上下文
 */
@Component
@Slf4j
public class TenantContextFilter implements HandlerInterceptor, Ordered {
    
    public static final String TOKEN = "token";
    
    @Resource
    private SsoActionService ssoActionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 确保BaseRequestContext已初始化
        BaseRequestContext baseContext = BaseRequestContext.get();
        if (baseContext == null) {
            baseContext = new JulyRequestContext();
            BaseRequestContext.set(baseContext);
        }
        
        // 从BaseRequestContext中获取租户ID并设置到TenantContext中
        Long tenantId = baseContext.getTenantId();
        
        // 如果还是没有租户ID，则尝试从请求头中的token获取用户信息
        if (tenantId == null) {
            String token = request.getHeader(TOKEN);
            if (StrUtil.isNotBlank(token)) {
                try {
                    UserInfoDTO userInfoDTO = ssoActionService.getCacheUserInfo(token);
                    if (userInfoDTO != null && userInfoDTO.getTenantId() != null) {
                        tenantId = userInfoDTO.getTenantId();
                        baseContext.setTenantId(tenantId);
                        
                        // 同时设置当前用户到CurrentUserHolder
                        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(userInfoDTO);
                        
                        // 设置BaseRequestContext中的附件信息
                        BaseRequestContext.putAttachment(Constants.OPEN_ID, userInfoDTO.getOpenId());
                        BaseRequestContext.putAttachment(Constants.USER_NAME, userInfoDTO.getName());
                    }
                } catch (AuthException ex) {
                    log.debug("Token invalid, cannot get user info: {}", ex.getMessage());
                } catch (Exception ex) {
                    log.warn("Error getting user info from token: {}", ex.getMessage());
                }
            }
        }
        
        // 设置TenantContext
        if (tenantId != null) {
            TenantContext.setTenantId(tenantId);
            log.debug("TenantContextFilter set tenantId: {}", tenantId);
        } else {
            log.debug("TenantContextFilter no tenantId found");
        }
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除租户上下文
        TenantContext.clear();
        // 清除当前用户上下文（如果是在这个过滤器中设置的）
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.remove();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    @Override
    public int getOrder() {
        // 设置为较高的优先级，确保在任何需要租户上下文的地方都能正确设置
        return -100;
    }
}
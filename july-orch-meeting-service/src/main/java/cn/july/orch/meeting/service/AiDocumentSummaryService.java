package cn.july.orch.meeting.service;

import cn.july.feishu.model.drive.BatchCreatePermissionMemberRequest;
import cn.july.feishu.model.drive.CreateImportTaskRequest;
import cn.july.feishu.model.drive.UploadMediaRequest;
import cn.july.orch.meeting.domain.dto.ImportTaskResult;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import com.lark.oapi.service.drive.v1.model.CreateImportTaskRespBody;
import com.lark.oapi.service.drive.v1.model.GetImportTaskRespBody;
import com.lark.oapi.service.drive.v1.model.ImportTask;
import com.lark.oapi.service.drive.v1.model.UploadAllMediaRespBody;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Assistant
 * @description AI文档汇总服务
 */
@Slf4j
@Service
public class AiDocumentSummaryService {

    @Resource
    private AiEmpowermentService aiEmpowermentService;

    @Resource
    private FileStorageService fileStorageService;

    @Resource
    private FileDetailService fileDetailService;

    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;

    @Resource
    private FeishuCalendarActionService feishuCalendarActionService;

    @Resource
    private NewMeetingDomainService newMeetingDomainService;

    /**
     * 对会前文档进行AI汇总并生成Markdown格式的汇总内容
     * 用于创建云文档
     *
     * @param preMeetingDocuments 会前文档列表
     * @param enableDocAiSummary  是否启用文档AI汇总
     * @param meetingTitle        会议标题
     * @return Markdown格式的汇总内容
     */
    public String generateMarkdownSummary(List<PreMeetingDocumentDTO> preMeetingDocuments, Boolean enableDocAiSummary, String meetingTitle) {
        // 检查是否启用AI汇总
        if (enableDocAiSummary == null || !enableDocAiSummary) {
            log.debug("文档AI汇总功能未启用");
            return "";
        }

        // 检查是否有文档需要汇总
        if (CollectionUtils.isEmpty(preMeetingDocuments)) {
            log.debug("无会前文档需要汇总");
            return "";
        }

        log.info("开始生成{}个会前文档的Markdown汇总，会议：{}", preMeetingDocuments.size(), meetingTitle);

        StringBuilder markdownBuilder = new StringBuilder();

        // 添加文档标题
        markdownBuilder.append("# ").append(meetingTitle).append(" - 会前文档汇总\n\n");
        markdownBuilder.append("> 本文档由AI自动生成，汇总了会议相关的会前文档内容\n\n");

        // 添加文档列表
        markdownBuilder.append("## 文档列表\n\n");
        for (int i = 0; i < preMeetingDocuments.size(); i++) {
            PreMeetingDocumentDTO document = preMeetingDocuments.get(i);
            markdownBuilder.append(String.format("%d. **%s**\n", i + 1, document.getFileName()));
        }
        markdownBuilder.append("\n");

        // 添加汇总内容
        markdownBuilder.append("## 文档汇总\n\n");

        for (int i = 0; i < preMeetingDocuments.size(); i++) {
            PreMeetingDocumentDTO document = preMeetingDocuments.get(i);
            String documentSummary = processDocumentSummary(document, i + 1);

            // 只有当文档汇总成功且不为空时才添加到结果中
            if (document.getAiSummaryStatus() != null && "SUCCESS".equals(document.getAiSummaryStatus())
                    && documentSummary != null && !documentSummary.isEmpty()) {

                // 将文档汇总转换为Markdown格式
                String markdownSummary = convertToMarkdown(documentSummary, i + 1);
                markdownBuilder.append(markdownSummary).append("\n\n");
            }
        }

        String finalMarkdown = markdownBuilder.toString();
        log.info("Markdown汇总生成完成，总长度：{}", finalMarkdown.length());
        return finalMarkdown;
    }

    /**
     * 将文档汇总内容转换为Markdown格式
     *
     * @param documentSummary 文档汇总内容
     * @param index           文档序号
     * @return Markdown格式的内容
     */
    private String convertToMarkdown(String documentSummary, int index) {
        StringBuilder markdownBuilder = new StringBuilder();

        // 按行分割内容
        String[] lines = documentSummary.split("\n");

        for (String line : lines) {
            line = line.trim();
            if (line.isEmpty()) {
                continue;
            }

            // 处理标题行（以【文档X】开头）
            if (line.startsWith("【文档" + index + "】")) {
                String title = line.substring(line.indexOf("】") + 1);
                markdownBuilder.append("### ").append(title).append("\n\n");
            }
            // 处理"汇总内容："行
            else if (line.equals("汇总内容：")) {
                // 跳过这行，因为标题已经处理了
                continue;
            }
            // 处理其他内容
            else {
                markdownBuilder.append(line).append("\n\n");
            }
        }

        return markdownBuilder.toString();
    }

    /**
     * 异步创建会议文档汇总
     *
     * @param meeting             会议
     * @param preMeetingDocuments 会前文档列表
     * @param enableDocAiSummary  是否启用AI汇总
     * @param meetingTitle        会议标题
     * @param attendees           参会人员列表
     * @param userAccessToken     用户访问token
     * @return 异步结果，包含文档链接
     */
    @Async
    public CompletableFuture<String> createMeetingDocumentSummaryAsync(
            NewMeeting meeting,
            List<PreMeetingDocumentDTO> preMeetingDocuments,
            Boolean enableDocAiSummary,
            String meetingTitle,
            List<String> attendees,
            String userAccessToken) {

        return CompletableFuture.supplyAsync(() -> {
            try {
                return createMeetingDocumentSummary(meeting, preMeetingDocuments, enableDocAiSummary, meetingTitle, attendees, userAccessToken);
            } catch (Exception e) {
                log.error("异步创建会议文档汇总失败", e);
                return null;
            }
        });
    }

    /**
     * 创建会议文档汇总
     *
     * @param meeting             会议
     * @param preMeetingDocuments 会前文档列表
     * @param enableDocAiSummary  是否启用AI汇总
     * @param meetingTitle        会议标题
     * @param attendees           参会人员列表
     * @param userAccessToken     用户访问token
     * @return 文档链接
     */
    public String createMeetingDocumentSummary(
            NewMeeting meeting,
            List<PreMeetingDocumentDTO> preMeetingDocuments,
            Boolean enableDocAiSummary,
            String meetingTitle,
            List<String> attendees,
            String userAccessToken) {

        if (meeting == null) {
            return null;
        }

        // 检查是否启用AI汇总
        if (enableDocAiSummary == null || !enableDocAiSummary) {
            log.debug("文档AI汇总功能未启用");
            return null;
        }

        // 检查是否有文档需要汇总
        if (CollectionUtils.isEmpty(preMeetingDocuments)) {
            log.debug("无会前文档需要汇总");
            return null;
        }

        log.info("开始创建会议文档汇总，会议：{}，文档数量：{}", meetingTitle, preMeetingDocuments.size());

        try {
            // 1. 生成Markdown汇总内容（同时更新PreMeetingDocumentDTO对象）
            String markdownContent = generateMarkdownSummary(
                    preMeetingDocuments, enableDocAiSummary, meetingTitle);

            if (markdownContent == null || markdownContent.trim().isEmpty()) {
                log.warn("生成的Markdown汇总内容为空");
                return null;
            }

            // 2. 更新数据库中的会前文档信息（AI汇总状态和内容）
            updateMeetingPreMeetingDocuments(meeting.getId(), preMeetingDocuments);

            // 3. 创建临时文件
            String fileName = meetingTitle + "_会前资料智能汇总.md";
            File tempFile = createTempMarkdownFile(fileName, markdownContent);

            try {
                String trimFileName = fileName.replace(".md", "");
                // 4. 上传文件到飞书云盘
                String fileToken = uploadMarkdownFile(tempFile, fileName, userAccessToken);
                if (fileToken == null) {
                    log.error("上传Markdown文件失败");
                    return null;
                }

                // 5. 创建导入任务
                String ticket = createImportTask(fileToken, trimFileName, userAccessToken);
                if (ticket == null) {
                    log.error("创建导入任务失败");
                    return null;
                }

                // 6. 查询导入任务结果
                ImportTaskResult importResult = waitForImportTaskCompletion(ticket, userAccessToken);
                if (importResult == null) {
                    log.error("导入任务失败");
                    return null;
                }

                // 7. 设置文档权限
                // 移除创建人ID，避免重复设置权限
                String createUserId = meeting.getCreateUserId();
                if (createUserId != null && attendees != null) {
                    attendees.removeIf(attendee -> createUserId.equals(attendee));
                }
                if (!CollectionUtils.isEmpty(attendees)) {
                    setDocumentPermissions(importResult.getDocumentToken(), attendees, userAccessToken);
                }

                // 8. 使用导入任务返回的文档链接
                String documentUrl = importResult.getDocumentUrl();

                log.info("会议文档汇总创建成功，文档链接：{}", documentUrl);
                return documentUrl;

            } finally {
                // 清理临时文件
                cleanupTempFile(tempFile);
            }

        } catch (Exception e) {
            log.error("创建会议文档汇总失败，会议：{}", meetingTitle, e);
            return null;
        }
    }

    /**
     * 创建临时Markdown文件
     */
    private File createTempMarkdownFile(String fileName, String content) throws IOException {
        // 在项目根目录创建临时文件夹
        Path projectRoot = Paths.get("").toAbsolutePath();
        Path tempDir = projectRoot.resolve("temp");

        // 确保临时目录存在
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        Path tempFile = tempDir.resolve(UUID.randomUUID().toString() + "_" + fileName);

        try (FileWriter writer = new FileWriter(tempFile.toFile())) {
            writer.write(content);
        }

        return tempFile.toFile();
    }

    /**
     * 上传Markdown文件到飞书云盘
     */
    private String uploadMarkdownFile(File file, String fileName, String userAccessToken) {
        try {
            UploadMediaRequest request = UploadMediaRequest.builder()
                    .fileName(fileName)
                    .parentType("ccm_import_open")
                    .size(file.length())
                    .extra("{\"obj_type\":\"docx\",\"file_extension\":\"md\"}")
                    .file(file)
                    .build();

            UploadAllMediaRespBody response = tenantFeishuAppClient.getDriveService().uploadMedia(request, userAccessToken);

            if (response != null && response.getFileToken() != null) {
                log.info("Markdown文件上传成功，fileToken：{}", response.getFileToken());
                return response.getFileToken();
            } else {
                log.error("Markdown文件上传失败，响应为空");
                return null;
            }

        } catch (Exception e) {
            log.error("上传Markdown文件失败，文件名：{}", fileName, e);
            return null;
        }
    }

    /**
     * 创建导入任务
     */
    private String createImportTask(String fileToken, String fileName, String userAccessToken) {
        try {
            CreateImportTaskRequest request = CreateImportTaskRequest.builder()
                    .fileExtension("md")
                    .fileToken(fileToken)
                    .type("docx")
                    .fileName(fileName)
                    .mountType(1)
                    .mountKey("") // 挂载到根目录
                    .build();

            CreateImportTaskRespBody response = tenantFeishuAppClient.getDriveService().createImportTask(request, userAccessToken);

            if (response != null && response.getTicket() != null) {
                log.info("导入任务创建成功，ticket：{}", response.getTicket());
                return response.getTicket();
            } else {
                log.error("导入任务创建失败，响应为空");
                return null;
            }

        } catch (Exception e) {
            log.error("创建导入任务失败，fileToken：{}", fileToken, e);
            return null;
        }
    }

    /**
     * 等待导入任务完成
     *
     * @param ticket          任务票据
     * @param userAccessToken 用户访问token
     * @return 包含文档token和文件链接的结果
     */
    private ImportTaskResult waitForImportTaskCompletion(String ticket, String userAccessToken) {
        int maxRetries = 30; // 最多重试30次
        int retryInterval = 2000; // 每次重试间隔2秒

        for (int i = 0; i < maxRetries; i++) {
            try {
                GetImportTaskRespBody response = tenantFeishuAppClient.getDriveService().getImportTask(ticket, userAccessToken);

                if (response != null && response.getResult() != null) {
                    ImportTask result = response.getResult();

                    // 使用反射获取状态
                    Integer status = null;
                    String fileToken = null;
                    String failReason = null;

                    try {
                        status = result.getJobStatus();
                        if (0 == status) {
                            fileToken = result.getToken();
                            String documentUrl = result.getUrl();
                            log.info("导入任务完成，文档token：{}，文档链接：{}", fileToken, documentUrl);
                            return ImportTaskResult.builder()
                                    .documentToken(fileToken)
                                    .documentUrl(documentUrl)
                                    .build();
                        } else if (3 == status) {
                            failReason = result.getJobErrorMsg();
                            log.error("导入任务失败，错误信息：{}", failReason);
                            return null;
                        } else {
                            // 任务还在进行中，继续等待
                            log.debug("导入任务进行中，状态：{}，重试次数：{}", status, i + 1);
                            Thread.sleep(retryInterval);
                        }
                    } catch (Exception e) {
                        log.error("获取导入任务结果信息失败", e);
                        Thread.sleep(retryInterval);
                        continue;
                    }
                } else {
                    log.warn("查询导入任务结果失败，响应为空，重试次数：{}", i + 1);
                    Thread.sleep(retryInterval);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待导入任务完成时被中断", e);
                return null;
            } catch (Exception e) {
                log.error("查询导入任务结果失败，重试次数：{}", i + 1, e);
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return null;
                }
            }
        }

        log.error("导入任务超时，ticket：{}", ticket);
        return null;
    }

    /**
     * 设置文档权限
     */
    private void setDocumentPermissions(String documentToken, List<String> attendees, String userAccessToken) {
        try {
            List<BatchCreatePermissionMemberRequest.PermissionMember> members = attendees.stream()
                    .map(attendee -> BatchCreatePermissionMemberRequest.PermissionMember.builder()
                            .memberType("openid")
                            .memberId(attendee)
                            .perm("view")
                            .permType("container")
                            .type("user")
                            .build())
                    .collect(java.util.stream.Collectors.toList());

            BatchCreatePermissionMemberRequest request = BatchCreatePermissionMemberRequest.builder()
                    .token(documentToken)
                    .type("docx")
                    .needNotification(false)
                    .members(members)
                    .build();

            tenantFeishuAppClient.getDriveService().batchCreatePermissionMember(request, userAccessToken);
            log.info("文档权限设置成功，文档token：{}，权限用户数量：{}", documentToken, attendees.size());

        } catch (Exception e) {
            log.error("设置文档权限失败，文档token：{}", documentToken, e);
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    log.debug("临时文件删除成功：{}", tempFile.getAbsolutePath());
                } else {
                    log.warn("临时文件删除失败：{}", tempFile.getAbsolutePath());
                }
            } catch (Exception e) {
                log.error("删除临时文件时发生异常：{}", tempFile.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 清理项目临时目录中的所有临时文件
     * 可以定期调用此方法清理过期的临时文件
     */
    public void cleanupTempDirectory() {
        try {
            Path projectRoot = Paths.get("").toAbsolutePath();
            Path tempDir = projectRoot.resolve("temp");

            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                        .filter(Files::isRegularFile)
                        .forEach(path -> {
                            try {
                                // 删除超过1小时的临时文件
                                long fileAge = System.currentTimeMillis() - Files.getLastModifiedTime(path).toMillis();
                                if (fileAge > 3600000) { // 1小时 = 3600000毫秒
                                    Files.delete(path);
                                    log.debug("清理过期临时文件：{}", path);
                                }
                            } catch (Exception e) {
                                log.warn("清理临时文件失败：{}", path, e);
                            }
                        });
            }
        } catch (Exception e) {
            log.error("清理临时目录时发生异常", e);
        }
    }

    /**
     * 处理单个文档的AI汇总
     *
     * @param document 文档信息
     * @param index    文档序号
     * @return 单个文档的汇总内容
     */
    private String processDocumentSummary(PreMeetingDocumentDTO document, int index) {
        StringJoiner documentSummary = new StringJoiner("\n");
        documentSummary.add(String.format("【文档%d】%s", index, document.getFileName()));

        try {
            // 从 filekey 获取文件并调用AI汇总
            String aiSummary = callAiSummaryWithFileKey(document.getFileKey(), document.getFileName());

            // 更新文档的AI汇总状态和内容
            document.setAiSummaryStatus("SUCCESS");
            document.setAiSummaryContent(aiSummary);

            documentSummary.add("汇总内容：");
            documentSummary.add(aiSummary);

            log.info("文档 {} AI汇总成功", document.getFileName());

        } catch (Exception e) {
            log.error("文档 {} AI汇总失败", document.getFileName(), e);

            // 更新文档的AI汇总状态为失败
            document.setAiSummaryStatus("FAILED");
            document.setAiSummaryContent("AI汇总失败：" + e.getMessage());

            // 处理失败时不返回任何内容，直接返回空字符串
            log.info("文档 {} AI汇总失败，不展示该文件的AI汇总", document.getFileName());
            return ""; // 直接返回空字符串，不展示失败的文档
        }

        return documentSummary.toString();
    }

    /**
     * 通过filekey调用AI智能体进行文档汇总
     *
     * @param fileKey  文件关键字
     * @param fileName 文件名称
     * @return AI汇总结果
     */
    private String callAiSummaryWithFileKey(String fileKey, String fileName) {
        try {
            // 从 filekey 获取文件信息
            FileDetailPO fileDetailPO = fileDetailService.getById(fileKey);
            if (fileDetailPO == null) {
                throw new RuntimeException("文件不存在：" + fileKey);
            }

            // 获取文件URL
            FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
            if (fileInfo == null) {
                throw new RuntimeException("无法获取文件信息：" + fileKey);
            }

            // 下载文件到内存
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            fileStorageService.download(fileInfo).outputStream(outputStream);
            byte[] fileBytes = outputStream.toByteArray();

            // 创建 MultipartFile 对象
            MultipartFile multipartFile = createMultipartFileFromBytes(fileBytes, fileName, fileDetailPO.getContentType());

            // 调用现有的AiEmpowermentService进行文档汇总
            log.info("开始调用AI智能体汇总文档：{}", fileName);

            // 使用默认的汇总提示词
            String customPrompt = "请对这个文档进行详细的汇总，重点关注关键信息和要点。";

            // 调用现有的智能体服务（非流式）
            AgentCompleteRespDTO response = aiEmpowermentService.callAiSummaryNonStream(multipartFile, customPrompt);

            if (response != null && response.getAnswer() != null) {
                return response.getAnswer();
            } else {
                throw new RuntimeException("智能体返回结果为空");
            }

        } catch (Exception e) {
            log.error("调用AI智能体汇总文档失败，文件：{}", fileName, e);
            throw new RuntimeException("智能体汇总失败：" + e.getMessage(), e);
        }
    }

    /**
     * 从字节数组创建 MultipartFile 对象
     */
    private MultipartFile createMultipartFileFromBytes(byte[] fileBytes, String fileName, String contentType) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return fileBytes.length == 0;
            }

            @Override
            public long getSize() {
                return fileBytes.length;
            }

            @Override
            public byte[] getBytes() {
                return fileBytes;
            }

            @Override
            public java.io.InputStream getInputStream() {
                return new ByteArrayInputStream(fileBytes);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException {
                throw new UnsupportedOperationException("不支持此操作");
            }
        };
    }

    /**
     * 检查并初始化文档的AI汇总状态
     *
     * @param preMeetingDocuments 会前文档列表
     */
    public void initializeDocumentSummaryStatus(List<PreMeetingDocumentDTO> preMeetingDocuments) {
        if (CollectionUtils.isEmpty(preMeetingDocuments)) {
            return;
        }

        for (PreMeetingDocumentDTO document : preMeetingDocuments) {
            if (document.getAiSummaryStatus() == null) {
                document.setAiSummaryStatus("PENDING");
            }
            if (document.getAiSummaryContent() == null) {
                document.setAiSummaryContent("");
            }
        }
    }

    /**
     * 异步处理文档AI汇总并更新会议描述
     */
    public void processDocumentSummaryAsync(NewMeeting meeting, List<PreMeetingDocumentDTO> preMeetingDocuments, String fsCalendarEventId, String userAccessToken) {
        Long meetingId = meeting.getId();
        log.info("开始异步处理会议{}的文档AI汇总", meetingId);

        try {
            // 1. 异步创建云文档汇总
            createMeetingDocumentSummaryAsync(
                    meeting,
                    preMeetingDocuments,
                    true,
                    meeting.getMeetingName(),
                    meeting.getAttendees(),
                    userAccessToken
            ).thenAccept(documentUrl -> {
                if (documentUrl != null) {
                    // 更新会议描述，添加云文档链接
                    String originalDescription = meeting.getMeetingDescription() != null ? meeting.getMeetingDescription() : "";
                    String documentLinkText = "\n\n📄 会前文档汇总：" + documentUrl;
                    String finalDescription = originalDescription + documentLinkText;

                    // 更新飞书会议描述
                    if (fsCalendarEventId != null) {
                        try {
                            feishuCalendarActionService.updateCalendarEventDescription(fsCalendarEventId, finalDescription, userAccessToken);
                            log.info("已成功更新飞书会议描述并添加云文档链接，会议ID：{}，文档链接：{}", meetingId, documentUrl);
                        } catch (Exception e) {
                            log.error("更新飞书会议描述失败，会议ID：{}", meetingId, e);
                        }
                    }

                    log.info("云文档汇总创建成功，会议ID：{}，文档链接：{}", meetingId, documentUrl);
                } else {
                    log.warn("云文档汇总创建失败，会议ID：{}", meetingId);
                }
            }).exceptionally(throwable -> {
                log.error("异步创建云文档汇总失败，会议ID：{}", meetingId, throwable);
                return null;
            });

        } catch (Exception e) {
            log.error("异步处理文档AI汇总失败，会议ID：{}", meetingId, e);
        }
    }

    /**
     * 更新会议中的会前文档信息（AI汇总状态和内容）
     *
     * @param meetingId           会议ID
     * @param preMeetingDocuments 更新后的会前文档列表
     */
    private void updateMeetingPreMeetingDocuments(Long meetingId, List<PreMeetingDocumentDTO> preMeetingDocuments) {
        try {
            // 获取会议信息
            NewMeeting meeting = newMeetingDomainService.findById(meetingId);
            if (meeting == null) {
                log.warn("会议不存在，无法更新会前文档信息，会议ID：{}", meetingId);
                return;
            }

            // 更新会前文档列表
            meeting.setPreMeetingDocuments(preMeetingDocuments);

            // 保存到数据库
            newMeetingDomainService.updateMeeting(meeting);

            log.info("成功更新会议会前文档信息，会议ID：{}，文档数量：{}", meetingId, preMeetingDocuments.size());

        } catch (Exception e) {
            log.error("更新会议会前文档信息失败，会议ID：{}", meetingId, e);
        }
    }
}
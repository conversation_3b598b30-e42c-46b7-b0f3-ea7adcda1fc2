package cn.july.orch.meeting.assembler;

import cn.july.core.model.assembler.QueryAssembler;
import cn.july.orch.meeting.domain.dto.SysResourceDTO;
import cn.july.orch.meeting.domain.dto.SysResourceTreeDTO;
import cn.july.orch.meeting.domain.po.SysResourcePO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ResourceAssembler extends QueryAssembler<Object, SysResourcePO, SysResourceDTO> {

    default List<SysResourceTreeDTO> buildTree(List<SysResourcePO> resourceList) {
        // 获取顶层资源
        List<SysResourcePO> topLevelResources = resourceList.stream()
                .filter(resource -> resource.getPid() == null || resource.getPid() == 0)
                .sorted(Comparator.comparing(SysResourcePO::getResourceSort))
                .collect(Collectors.toList());

        // 递归生成树结构
        return topLevelResources.stream()
                .map(resource -> buildTree(resource, resourceList))
                .collect(Collectors.toList());
    }

    default SysResourceTreeDTO buildTree(SysResourcePO resource, List<SysResourcePO> resourceList) {
        SysResourceTreeDTO dto = new SysResourceTreeDTO(resource);

        // 获取子资源
        List<SysResourcePO> children = resourceList.stream()
                .filter(child -> resource.getId().equals(child.getPid()))
                .sorted(Comparator.comparing(SysResourcePO::getResourceSort))
                .collect(Collectors.toList());

        // 递归生成子资源树
        dto.setChildren(children.stream()
                .map(child -> buildTree(child, resourceList))
                .collect(Collectors.toList()));

        return dto;
    }
}

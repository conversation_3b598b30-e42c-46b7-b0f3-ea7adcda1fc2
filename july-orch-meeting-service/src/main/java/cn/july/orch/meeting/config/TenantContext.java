package cn.july.orch.meeting.config;

import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.exception.MessageCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户上下文管理器
 * 用于在请求线程中传递租户ID
 */
@Slf4j
public class TenantContext {
    
    private static final ThreadLocal<Long> TENANT_HOLDER = new ThreadLocal<>();
    
    /**
     * 设置当前租户ID
     */
    public static void setTenantId(Long tenantId) {
        TENANT_HOLDER.set(tenantId);
    }
    
    /**
     * 获取当前租户ID
     */
    public static Long getTenantId() {
        return TENANT_HOLDER.get();
    }
    
    /**
     * 获取当前租户ID，如果为空则抛出异常
     */
    public static Long getRequiredTenantId() {
        Long tenantId = getTenantId();
        if (tenantId == null) {
            throw new BusinessException(MessageCode.TENANT_ID_REQUIRED);
        }
        return tenantId;
    }
    
    /**
     * 清除租户上下文
     */
    public static void clear() {
        TENANT_HOLDER.remove();
    }
    
    /**
     * 检查是否有租户上下文
     */
    public static boolean hasTenant() {
        return getTenantId() != null;
    }
}

package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.service.MeetingCheckInService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 会议签到
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MeetingCheckInTask {

    private final MeetingCheckInService meetingCheckInService;

    /**
     * 会议提前通知定时任务
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    @IgnoreTenant
    public void sendAdvanceNotifications() {
        log.info("开始执行会议提前通知定时任务");
        meetingCheckInService.sendCheckIn();
    }
}

package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 角色类型枚举
 * @date 2025-01-30
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum {

    SYSTEM("system", "系统角色"),
    CUSTOMIZE("customize", "自定义角色");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, RoleTypeEnum> VALUES = new HashMap<>();

    static {
        for (final RoleTypeEnum item : RoleTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RoleTypeEnum of(String code) {
        return VALUES.get(code);
    }

    /**
     * 根据code获取枚举
     */
    public static RoleTypeEnum getByCode(String code) {
        return VALUES.get(code);
    }
}

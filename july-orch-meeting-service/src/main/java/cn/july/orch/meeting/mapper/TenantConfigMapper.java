package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TenantConfigPO;
import cn.july.orch.meeting.enums.TenantStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 租户配置Mapper
 */
@Mapper
public interface TenantConfigMapper extends BaseMapper<TenantConfigPO> {

    /**
     * 根据租户ID查询配置
     */
    default TenantConfigPO selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantConfigPO> wrapper = Wrappers.lambdaQuery(TenantConfigPO.class)
                .eq(TenantConfigPO::getTenantId, tenantId)
                .eq(TenantConfigPO::getDeleted, 0);
        return selectOne(wrapper);
    }

    /**
     * 根据飞书应用ID查询配置
     */
    default TenantConfigPO selectByFeishuAppId(String feishuAppId) {
        LambdaQueryWrapper<TenantConfigPO> wrapper = Wrappers.lambdaQuery(TenantConfigPO.class)
                .eq(TenantConfigPO::getFeishuAppId, feishuAppId)
                .eq(TenantConfigPO::getDeleted, 0);
        return selectOne(wrapper);
    }

    /**
     * 查询所有启用的租户配置
     */
    default List<TenantConfigPO> selectEnabledConfigs() {
        LambdaQueryWrapper<TenantConfigPO> wrapper = Wrappers.lambdaQuery(TenantConfigPO.class)
                .eq(TenantConfigPO::getStatus, TenantStatusEnum.ENABLED)
                .eq(TenantConfigPO::getDeleted, 0)
                .orderByAsc(TenantConfigPO::getCreateTime);
        return selectList(wrapper);
    }

    /**
     * 检查租户ID是否存在
     */
    default int checkTenantIdExists(Long tenantId) {
        LambdaQueryWrapper<TenantConfigPO> wrapper = Wrappers.lambdaQuery(TenantConfigPO.class)
                .eq(TenantConfigPO::getTenantId, tenantId)
                .eq(TenantConfigPO::getDeleted, 0);
        return Math.toIntExact(selectCount(wrapper));
    }

    /**
     * 检查飞书应用ID是否存在
     */
    default int checkFeishuAppIdExists(String feishuAppId) {
        LambdaQueryWrapper<TenantConfigPO> wrapper = Wrappers.lambdaQuery(TenantConfigPO.class)
                .eq(TenantConfigPO::getFeishuAppId, feishuAppId)
                .eq(TenantConfigPO::getDeleted, 0);
        return Math.toIntExact(selectCount(wrapper));
    }
}

package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.MeetingCheckIn;
import cn.july.orch.meeting.domain.po.MeetingCheckInPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 参会人签到信息转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingCheckInConverter {

    /**
     * PO转Entity
     */
    MeetingCheckIn toEntity(MeetingCheckInPO po);

    /**
     * Entity转PO
     */
    MeetingCheckInPO toPO(MeetingCheckIn entity);

    /**
     * PO列表转Entity列表
     */
    List<MeetingCheckIn> toEntityList(List<MeetingCheckInPO> poList);
}

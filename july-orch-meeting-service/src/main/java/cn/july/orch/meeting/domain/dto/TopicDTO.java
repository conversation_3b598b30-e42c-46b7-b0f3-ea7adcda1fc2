package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议题DTO
 * @date 2025-11-06
 */
@Data
@ApiModel("议题信息")
public class TopicDTO {

    @ApiModelProperty("议题ID")
    private Long id;

    @ApiModelProperty("议题名称")
    private String name;

    @ApiModelProperty("议题描述")
    private String description;

    @ApiModelProperty("附件列表")
    private List<FileInfoDTO> attachments;

    @ApiModelProperty("是否启用(0-否,1-是)")
    private Integer isEnabled;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人ID")
    private String updateUserId;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}

package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum ResourceTypeEnum {
    MENU(1, "菜单"),
    BUTTON(2, "按钮"),
    LINK(3, "链接"),
    COL(4,"列数据");


    @EnumValue
    @JsonValue
    private final int type;

    private final String description;

    ResourceTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    private static final Map<Integer, ResourceTypeEnum> VALUES = new HashMap<>();
    static {
        for (final ResourceTypeEnum item : ResourceTypeEnum.values()) {
            VALUES.put(item.getType(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ResourceTypeEnum of(int code) {
        return VALUES.get(code);
    }
}

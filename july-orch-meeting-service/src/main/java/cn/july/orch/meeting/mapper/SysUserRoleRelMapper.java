package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.SysUserRoleRelPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 用户角色关联映射器
 * @date 2025-01-24
 */
public interface SysUserRoleRelMapper extends BaseMapper<SysUserRoleRelPO> {

    /**
     * 根据用户ID查询角色关联列表
     */
    default List<SysUserRoleRelPO> findByUserId(String userId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID查询用户关联列表
     */
    default List<SysUserRoleRelPO> findByRoleId(Long roleId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getRoleId, roleId);
        return selectList(wrapper);
    }

    /**
     * 根据用户ID和租户ID查询角色关联列表
     */
    default List<SysUserRoleRelPO> findByUserIdAndTenantId(String userId, Long tenantId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId)
                .eq(SysUserRoleRelPO::getTenantId, tenantId);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID和租户ID查询用户关联列表
     */
    default List<SysUserRoleRelPO> findByRoleIdAndTenantId(Long roleId, Long tenantId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getRoleId, roleId)
                .eq(SysUserRoleRelPO::getTenantId, tenantId);
        return selectList(wrapper);
    }

    /**
     * 根据用户ID列表查询角色关联列表
     */
    default List<SysUserRoleRelPO> findByUserIds(List<String> userIds) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .in(SysUserRoleRelPO::getUserId, userIds);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID列表查询用户关联列表
     */
    default List<SysUserRoleRelPO> findByRoleIds(List<Long> roleIds) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .in(SysUserRoleRelPO::getRoleId, roleIds);
        return selectList(wrapper);
    }

    /**
     * 根据角色类型查询用户关联列表
     */
    default List<SysUserRoleRelPO> findByRoleType(String roleType) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class);
        if (StringUtils.hasText(roleType)) {
            wrapper.eq(SysUserRoleRelPO::getRoleType, roleType);
        }
        return selectList(wrapper);
    }

    /**
     * 根据用户ID和角色类型查询角色关联列表
     */
    default List<SysUserRoleRelPO> findByUserIdAndRoleType(String userId, String roleType) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId);
        if (StringUtils.hasText(roleType)) {
            wrapper.eq(SysUserRoleRelPO::getRoleType, roleType);
        }
        return selectList(wrapper);
    }

    /**
     * 根据用户ID和角色ID查询关联关系
     */
    default SysUserRoleRelPO findByUserIdAndRoleId(String userId, Long roleId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId)
                .eq(SysUserRoleRelPO::getRoleId, roleId);
        return selectOne(wrapper);
    }

    /**
     * 根据用户ID删除所有关联关系
     */
    default void deleteByUserId(String userId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId);
        delete(wrapper);
    }

    /**
     * 根据角色ID删除所有关联关系
     */
    default void deleteByRoleId(Long roleId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getRoleId, roleId);
        delete(wrapper);
    }

    /**
     * 根据用户ID和租户ID删除所有关联关系
     */
    default void deleteByUserIdAndTenantId(String userId, Long tenantId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId)
                .eq(SysUserRoleRelPO::getTenantId, tenantId);
        delete(wrapper);
    }

    /**
     * 根据用户ID和角色ID删除关联关系
     */
    default void deleteByUserIdAndRoleId(String userId, Long roleId) {
        LambdaQueryWrapper<SysUserRoleRelPO> wrapper = Wrappers.lambdaQuery(SysUserRoleRelPO.class)
                .eq(SysUserRoleRelPO::getUserId, userId)
                .eq(SysUserRoleRelPO::getRoleId, roleId);
        delete(wrapper);
    }
}

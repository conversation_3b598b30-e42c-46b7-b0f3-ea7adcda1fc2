package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingPlanDTO;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划组装器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingPlanAssembler {

    MeetingPlanPO toEntity(MeetingPlanCreateCommand command);

    MeetingPlanPO toEntity(MeetingPlanUpdateCommand command);

    MeetingPlanDTO PO2DTO(MeetingPlanPO po);

    List<MeetingPlanDTO> PO2DTO(List<MeetingPlanPO> po);
}

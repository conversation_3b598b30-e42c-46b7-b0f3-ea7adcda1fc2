package cn.july.orch.meeting.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 参会人签到信息持久化对象
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_checkin", autoResultMap = true)
public class MeetingCheckInPO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 会议ID
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 参会人员open_id
     */
    @TableField("attendee_open_id")
    private String attendeeOpenId;

    /**
     * 参会人员姓名
     */
    @TableField("attendee_name")
    private String attendeeName;

    /**
     * 签到码（6位数字）
     */
    @TableField("checkin_code")
    private String checkinCode;

    /**
     * 签到状态(0:未签到,1:已签到)
     */
    @TableField("checkin_status")
    private Integer checkinStatus;

    /**
     * 签到时间
     */
    @TableField("checkin_time")
    private LocalDateTime checkinTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}

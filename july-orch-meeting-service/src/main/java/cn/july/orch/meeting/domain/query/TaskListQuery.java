package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> Assistant
 * @description 任务清单查询条件
 */
@Data
@ApiModel("任务清单查询条件")
public class TaskListQuery {

    @ApiModelProperty("页码")
    private Integer pageNo = 1;

    @ApiModelProperty("每页大小")
    private Integer pageSize = 10;

    @ApiModelProperty("清单名称")
    private String name;

    @ApiModelProperty("任务状态")
    private TaskStatusEnum status;
}
package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TaskActivityPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务动态Mapper
 */
public interface TaskActivityMapper extends BaseMapper<TaskActivityPO> {

    /**
     * 根据任务ID查询动态列表
     *
     * @param taskId 任务ID
     * @return 动态列表
     */
    List<TaskActivityPO> findByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务ID查询最新的动态
     *
     * @param taskId 任务ID
     * @param limit 限制条数
     * @return 动态列表
     */
    List<TaskActivityPO> findLatestByTaskId(@Param("taskId") Long taskId, @Param("limit") Integer limit);
}
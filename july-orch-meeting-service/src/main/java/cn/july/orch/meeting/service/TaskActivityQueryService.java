package cn.july.orch.meeting.service;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.TaskActivityAssembler;
import cn.july.orch.meeting.domain.dto.TaskActivityDTO;
import cn.july.orch.meeting.domain.entity.TaskActivityInfo;
import cn.july.orch.meeting.domain.po.TaskActivityPO;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import cn.july.orch.meeting.mapper.TaskActivityMapper;
import cn.july.orch.meeting.repository.ITaskActivityRepository;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务动态查询服务
 */
@Slf4j
@Service
public class TaskActivityQueryService {

    @Resource
    private ITaskActivityRepository taskActivityRepository;
    @Resource
    private TaskActivityMapper taskActivityMapper;
    @Resource
    private TaskActivityAssembler taskActivityAssembler;

    /**
     * 根据任务ID查询动态列表
     *
     * @param taskId 任务ID
     * @return 动态DTO列表
     */
    public List<TaskActivityDTO> getByTaskId(Long taskId) {
        log.info("查询任务动态列表，任务ID：{}", taskId);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findByTaskId(taskId);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 根据任务ID查询最新动态
     *
     * @param taskId 任务ID
     * @param limit 限制条数
     * @return 动态DTO列表
     */
    public List<TaskActivityDTO> getLatestByTaskId(Long taskId, Integer limit) {
        log.info("查询任务最新动态，任务ID：{}，限制条数：{}", taskId, limit);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findLatestByTaskId(taskId, limit);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 分页查询任务动态
     *
     * @param taskId 任务ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 分页结果
     */
    public PageResultDTO<TaskActivityDTO> pageByTaskId(Long taskId, Integer pageNo, Integer pageSize) {
        log.info("分页查询任务动态，任务ID：{}，页码：{}，页大小：{}", taskId, pageNo, pageSize);
        
        Page<TaskActivityPO> page = new Page<>(pageNo, pageSize);
        
        LambdaQueryWrapper<TaskActivityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskActivityPO::getTaskId, taskId)
                   .orderByDesc(TaskActivityPO::getCreateTime);
        
        IPage<TaskActivityPO> pageResult = taskActivityMapper.selectPage(page, queryWrapper);
        
        List<TaskActivityInfo> activityInfoList = taskActivityAssembler.toTaskActivityInfoList(pageResult.getRecords());
        List<TaskActivityDTO> activityDTOList = convertToTaskActivityDTOList(activityInfoList);
        
        return new PageResultDTO<>(pageNo, pageSize, pageResult.getTotal(), activityDTOList);
    }

    /**
     * 根据动态类型查询动态列表
     *
     * @param activityType 动态类型
     * @return 动态DTO列表
     */
    public List<TaskActivityDTO> getByActivityType(TaskActivityTypeEnum activityType) {
        log.info("查询指定类型的任务动态，类型：{}", activityType);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findByActivityType(activityType);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 根据时间范围查询动态列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 动态DTO列表
     */
    public List<TaskActivityDTO> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("查询时间范围内的任务动态，开始时间：{}，结束时间：{}", startTime, endTime);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findByTimeRange(startTime, endTime);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 根据用户ID查询动态列表
     *
     * @param userId 用户ID
     * @return 动态DTO列表
     */
    public List<TaskActivityDTO> getByUserId(String userId) {
        log.info("查询用户的任务动态，用户ID：{}", userId);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findByUserId(userId);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 统计任务动态数量
     *
     * @param taskId 任务ID
     * @return 动态数量
     */
    public Long countByTaskId(Long taskId) {
        log.info("统计任务动态数量，任务ID：{}", taskId);
        
        return taskActivityRepository.countByTaskId(taskId);
    }

    /**
     * 根据动态ID查询详情
     *
     * @param id 动态ID
     * @return 动态DTO
     */
    public TaskActivityDTO getById(Long id) {
        log.info("查询任务动态详情，ID：{}", id);
        
        TaskActivityInfo activityInfo = taskActivityRepository.findById(id);
        if (activityInfo == null) {
            log.warn("任务动态不存在，ID：{}", id);
            return null;
        }
        
        return convertToTaskActivityDTO(activityInfo);
    }

    /**
     * 获取任务最近一条动态
     *
     * @param taskId 任务ID
     * @return 动态DTO
     */
    public TaskActivityDTO getLatestByTaskId(Long taskId) {
        log.info("查询任务最新一条动态，任务ID：{}", taskId);
        
        List<TaskActivityInfo> activityInfoList = taskActivityRepository.findLatestByTaskId(taskId, 1);
        
        if (activityInfoList != null && !activityInfoList.isEmpty()) {
            return convertToTaskActivityDTO(activityInfoList.get(0));
        }
        
        return null;
    }

    /**
     * 获取督办相关的动态列表
     *
     * @param taskId 任务ID
     * @return 督办动态列表
     */
    public List<TaskActivityDTO> getSupervisionActivities(Long taskId) {
        log.info("查询任务督办相关动态，任务ID：{}", taskId);
        
        List<TaskActivityPO> supervisionActivities = taskActivityMapper.selectList(
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getTaskId, taskId)
                .in(TaskActivityPO::getActivityType, 
                    TaskActivityTypeEnum.SUPERVISE.getCode(),
                    TaskActivityTypeEnum.REMIND.getCode(),
                    TaskActivityTypeEnum.ESCALATE.getCode(),
                    TaskActivityTypeEnum.OVERDUE.getCode())
                .orderByDesc(TaskActivityPO::getCreateTime)
        );
        
        List<TaskActivityInfo> activityInfoList = taskActivityAssembler.toTaskActivityInfoList(supervisionActivities);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 获取任务状态变更动态
     *
     * @param taskId 任务ID
     * @return 状态变更动态列表
     */
    public List<TaskActivityDTO> getStatusChangeActivities(Long taskId) {
        log.info("查询任务状态变更动态，任务ID：{}", taskId);
        
        List<TaskActivityPO> statusChangeActivities = taskActivityMapper.selectList(
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getTaskId, taskId)
                .in(TaskActivityPO::getActivityType, 
                    TaskActivityTypeEnum.UPDATE_STATUS.getCode(),
                    TaskActivityTypeEnum.COMPLETE.getCode(),
                    TaskActivityTypeEnum.START.getCode())
                .orderByDesc(TaskActivityPO::getCreateTime)
        );
        
        List<TaskActivityInfo> activityInfoList = taskActivityAssembler.toTaskActivityInfoList(statusChangeActivities);
        
        return convertToTaskActivityDTOList(activityInfoList);
    }

    /**
     * 转换TaskActivityInfo为TaskActivityDTO
     *
     * @param activityInfo 动态信息
     * @return 动态DTO
     */
    private TaskActivityDTO convertToTaskActivityDTO(TaskActivityInfo activityInfo) {
        if (activityInfo == null) {
            return null;
        }
        
        TaskActivityDTO activityDTO = new TaskActivityDTO();
        activityDTO.setId(activityInfo.getId());
        activityDTO.setTaskId(activityInfo.getTaskId());
        activityDTO.setActivityType(activityInfo.getActivityType());
        activityDTO.setActivityTypeDesc(activityInfo.getActivityType() != null ? 
            activityInfo.getActivityType().getDescription() : null);
        activityDTO.setActivityDescription(activityInfo.getActivityDescription());
        activityDTO.setContentJson(activityInfo.getContentJson());
        activityDTO.setCreateTime(activityInfo.getCreateTime());
        activityDTO.setCreateUserId(activityInfo.getCreateUserId());
        activityDTO.setCreateUserName(activityInfo.getCreateUserName());
        
        return activityDTO;
    }

    /**
     * 转换TaskActivityInfo列表为TaskActivityDTO列表
     *
     * @param activityInfoList 动态信息列表
     * @return 动态DTO列表
     */
    private List<TaskActivityDTO> convertToTaskActivityDTOList(List<TaskActivityInfo> activityInfoList) {
        if (activityInfoList == null || activityInfoList.isEmpty()) {
            return null;
        }
        
        return activityInfoList.stream()
                .map(this::convertToTaskActivityDTO)
                .collect(Collectors.toList());
    }
}
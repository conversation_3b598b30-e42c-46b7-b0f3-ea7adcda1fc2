package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议题更新Command
 * @date 2025-11-06
 */
@Data
@ApiModel("议题更新请求")
public class TopicUpdateCommand {

    @ApiModelProperty(value = "议题ID", required = true)
    @NotNull(message = "议题ID不能为空")
    private Long id;

    @ApiModelProperty(value = "议题名称", required = true)
    @NotBlank(message = "议题名称不能为空")
    private String name;

    @ApiModelProperty("议题描述")
    private String description;

    @ApiModelProperty("附件对象存储key列表")
    private List<String> attachmentKeys;

    @ApiModelProperty(value = "是否启用(0-否,1-是)", required = true)
    @NotNull(message = "是否启用不能为空")
    private Integer isEnabled;
}

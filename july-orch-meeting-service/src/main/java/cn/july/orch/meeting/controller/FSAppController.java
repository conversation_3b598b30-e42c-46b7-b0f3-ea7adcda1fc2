package cn.july.orch.meeting.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.exception.BusinessException;
import cn.july.feishu.model.DepartmentWithChildrenDTO;
import cn.july.feishu.model.SignatureModel;
import cn.july.feishu.model.command.DepartmentCommand;
import cn.july.orch.meeting.domain.dto.TenantConfigDTO;
import cn.july.orch.meeting.domain.query.JsTicketQuery;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.service.TenantFeishuAppClient;
import cn.july.orch.meeting.service.TenantFeishuClientManager;
import com.lark.oapi.service.contact.v3.model.Department;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 飞书应用信息
 *
 * <AUTHOR>
 */
@Api(tags = "飞书应用信息")
@RestController
@RequestMapping("/app")
@RequiredArgsConstructor
public class FSAppController {

    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;
    @Resource
    private TenantFeishuClientManager tenantFeishuClientManager;

    @PostMapping("/id")
    @ApiOperation(value = "获取飞书appId")
    public String id(@RequestParam(value = "tenantId") Long tenantId) {
        TenantConfigDTO tenantConfig = tenantFeishuClientManager.getTenantConfig(tenantId);
        if (ObjUtil.isNotNull(tenantConfig)) {
            return tenantConfig.getFeishuAppId();
        }
        throw new BusinessException(MessageCode.TENANT_NOT_FOUND);
    }

    @PostMapping("/getJsTicket")
    @ApiOperation("获取js_sdk_ticket")
    public SignatureModel getJsTicket(@Validated @RequestBody JsTicketQuery query) {
        return tenantFeishuAppClient.getAuthService().generateSignature(query.getUrl());
    }

    @PostMapping("/getDepartmentByIds")
    @ApiOperation(value = "批量查询飞书部门信息")
    public List<Department> getDepartmentByIds(@RequestBody DepartmentCommand command) {
        return tenantFeishuAppClient.getContactService().getDepartmentBatch(command.getDepartmentIds());
    }

    @PostMapping("/getChildDepartments")
    @ApiOperation(value = "获取子部门信息")
    public List<DepartmentWithChildrenDTO> getChildDepartments(@RequestParam(value = "departmentId", required = false) String departmentId) {
        List<Department> departments;
        if (StrUtil.isBlank(departmentId)) {
            List<String> contactScope = tenantFeishuAppClient.getContactService().getContactScope();
            departments = tenantFeishuAppClient.getContactService().getDepartmentBatch(contactScope);
        } else {
            departments = tenantFeishuAppClient.getContactService().getDepartmentChildren(departmentId);
        }

        // 为每个部门检查是否有子部门
        List<DepartmentWithChildrenDTO> result = new ArrayList<>();
        for (Department department : departments) {
            // 创建DTO并复制部门属性
            DepartmentWithChildrenDTO dto = new DepartmentWithChildrenDTO();
            BeanUtils.copyProperties(department, dto);

            // 获取该部门的子部门信息
            List<Department> children = tenantFeishuAppClient.getContactService().getDepartmentChildren(department.getOpenDepartmentId());
            // 判断是否有子部门
            dto.setHasChildren(CollUtil.isNotEmpty(children));

            result.add(dto);
        }

        return result;
    }

    @PostMapping("/getParentDepartments")
    @ApiOperation(value = "获取父部门信息,子部门在前")
    public Map<String, List<Department>> getParentDepartments(@RequestBody List<String> departmentIds) {
        Map<String, List<Department>> result = new HashMap<>();
        for (String departmentId : departmentIds) {
            List<Department> departmentBatch = tenantFeishuAppClient.getContactService().getDepartmentBatch(Collections.singletonList(departmentId));
            if (CollUtil.isEmpty(departmentBatch)) {
                result.put(departmentId, new ArrayList<>());
                continue;
            }
            List<Department> parentDepartment = new ArrayList<>(tenantFeishuAppClient.getContactService().getParentDepartment(departmentId));
            parentDepartment.add(departmentBatch.get(0));
            result.put(departmentId, parentDepartment);
        }
        return result;
    }
}

package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.dto.FmsTaskDTO;
import cn.july.orch.meeting.domain.po.FmsTaskPO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Assistant
 * @description FMS任务装配器
 */
@Component
public class FmsTaskAssembler {
    
    /**
     * PO转DTO
     */
    public FmsTaskDTO PO2DTO(FmsTaskPO po) {
        if (po == null) {
            return null;
        }
        FmsTaskDTO dto = new FmsTaskDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
}

package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.domain.dto.card.SendMeetingNotificationDTO;
import com.lark.oapi.service.im.v1.model.CreateMessageRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CardSendActionService {

    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;

    /**
     * 会议提前通知
     */
    public CreateMessageRespBody sendMeetingNotification(SendMeetingNotificationDTO dto) {
        String cardContent = buildMeetingNotificationCardContent(dto);
        return tenantFeishuAppClient.getRobotService().sendCard(dto.getOpenId(), cardContent);
    }

    /**
     * 构建会议提前通知飞书卡片内容
     */
    private String buildMeetingNotificationCardContent(SendMeetingNotificationDTO dto) {
        // 根据优先级选择卡片颜色主题
//        String themeColor = getMeetingCardThemeColor(dto.getPriorityLevel());
//        String priorityIcon = getMeetingPriorityIcon(dto.getPriorityLevel());

        // 处理参会人员列表
        String attendeesText = dto.getAttendees() != null && !dto.getAttendees().isEmpty()
                ? String.join("、", dto.getAttendees())
                : "暂无";

        String cardJson = String.format(
                "{\n" +
                        "  \"config\": {\n" +
                        "    \"wide_screen_mode\": true\n" +
                        "  },\n" +
                        "  \"header\": {\n" +
                        "    \"template\": \"%s\",\n" +
                        "    \"title\": {\n" +
                        "      \"tag\": \"plain_text\",\n" +
                        "      \"content\": \"%s 会议提前通知\"\n" +
                        "    }\n" +
                        "  },\n" +
                        "  \"elements\": [\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"fields\": [\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**会议名称**\\n%s\"\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**会议类型**\\n%s\"\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"fields\": [\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**会议时间**\\n%s\"\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**会议时长**\\n%s\"\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"fields\": [\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**会议地点**\\n%s\"\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**优先级**\\n%s\"\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"text\": {\n" +
                        "        \"tag\": \"lark_md\",\n" +
                        "        \"content\": \"**参会人员**\\n%s\"\n" +
                        "      }\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"text\": {\n" +
                        "        \"tag\": \"lark_md\",\n" +
                        "        \"content\": \"**会议描述**\\n%s\"\n" +
                        "      }\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"hr\"\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"div\",\n" +
                        "      \"fields\": [\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**通知类型**\\n%s\"\n" +
                        "          }\n" +
                        "        },\n" +
                        "        {\n" +
                        "          \"is_short\": true,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**提前时间**\\n%s\"\n" +
                        "          }\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    },\n" +
                        "    {\n" +
                        "      \"tag\": \"action\",\n" +
                        "      \"actions\": [\n" +
                        "        {\n" +
                        "          \"tag\": \"button\",\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"plain_text\",\n" +
                        "            \"content\": \"查看会议详情\"\n" +
                        "          },\n" +
                        "          \"type\": \"primary\",\n" +
                        "          \"value\": \"{\\\"action\\\":\\\"view_meeting_detail\\\",\\\"meeting_url\\\":\\\"%s\\\"}\",\n" +
                        "          \"url\": \"%s\"\n" +
                        "        }\n" +
                        "      ]\n" +
                        "    }\n" +
                        "  ]\n" +
                        "}",
                "blue",
                "📅",
                escapeJsonString(dto.getMeetingName()),
                escapeJsonString(dto.getMeetingType()),
                escapeJsonString(dto.getMeetingTime()),
                escapeJsonString(dto.getMeetingDuration()),
                escapeJsonString(dto.getMeetingLocation() != null ? dto.getMeetingLocation() : "待定"),
                escapeJsonString("标签名称"),
                escapeJsonString(attendeesText),
                escapeJsonString(dto.getMeetingDescription() != null ? dto.getMeetingDescription() : "暂无描述"),
                escapeJsonString(dto.getNoticeType()),
                escapeJsonString(dto.getNoticeTime()),
                dto.getMeetingUrl() != null ? dto.getMeetingUrl() : "#",
                dto.getMeetingUrl() != null ? dto.getMeetingUrl() : "#"
        );

        log.debug("会议通知卡片JSON: {}", cardJson);
        return cardJson;
    }

    /**
     * 发送会议评价调查卡片
     */
    public CreateMessageRespBody sendMeetingEvaluationSurvey(SendMeetingEvaluationSurveyDTO dto) {
        String cardContent = buildMeetingEvaluationSurveyCardContent(dto);
        return tenantFeishuAppClient.getRobotService().sendCard(dto.getOpenId(), cardContent);
    }

    /**
     * 构建会议评价调查飞书卡片内容
     */
    private String buildMeetingEvaluationSurveyCardContent(SendMeetingEvaluationSurveyDTO dto) {
        String cardJson = String.format(
                "{\n" +
                        "  \"schema\": \"2.0\",\n" +
                        "  \"config\": {\n" +
                        "    \"update_multi\": true\n" +
                        "  },\n" +
                        "  \"header\": {\n" +
                        "    \"title\": {\n" +
                        "      \"tag\": \"plain_text\",\n" +
                        "      \"content\": \"📝 会议评价调查\"\n" +
                        "    },\n" +
                        "    \"template\": \"blue\"\n" +
                        "  },\n" +
                        "  \"body\": {\n" +
                        "    \"direction\": \"vertical\",\n" +
                        "    \"elements\": [\n" +
                        "      {\n" +
                        "        \"tag\": \"markdown\",\n" +
                        "        \"content\": \"**会议名称：** %s\\n**会议时间：** %s\\n**评价人：** %s\"\n" +
                        "      },\n" +
                        "      {\n" +
                        "        \"tag\": \"markdown\",\n" +
                        "        \"content\": \"请对本次会议进行评价：\"\n" +
                        "      },\n" +
                        "      {\n" +
                        "        \"tag\": \"form\",\n" +
                        "        \"name\": \"meeting_evaluation_form\",\n" +
                        "        \"elements\": [\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"markdown\",\n" +
                        "                    \"content\": \"会议整体评分\"\n" +
                        "                  },\n" +
                        "                  {\n" +
                        "                    \"tag\": \"select_static\",\n" +
                        "                    \"name\": \"meeting_score\",\n" +
                        "                    \"placeholder\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"请选择\"\n" +
                        "                    },\n" +
                        "                    \"options\": [\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"非常满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"5\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"4\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"一般\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"3\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"不满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"2\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"非常不满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"1\"\n" +
                        "                      }\n" +
                        "                    ]\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"markdown\",\n" +
                        "                    \"content\": \"会议内容评分\"\n" +
                        "                  },\n" +
                        "                  {\n" +
                        "                    \"tag\": \"select_static\",\n" +
                        "                    \"name\": \"content_score\",\n" +
                        "                    \"placeholder\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"请选择\"\n" +
                        "                    },\n" +
                        "                    \"options\": [\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"非常满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"5\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"4\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"一般\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"3\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"不满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"2\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"非常不满意\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"1\"\n" +
                        "                      }\n" +
                        "                    ]\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"markdown\",\n" +
                        "                    \"content\": \"会议时长评分\"\n" +
                        "                  },\n" +
                        "                  {\n" +
                        "                    \"tag\": \"select_static\",\n" +
                        "                    \"name\": \"duration_score\",\n" +
                        "                    \"placeholder\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"请选择\"\n" +
                        "                    },\n" +
                        "                    \"options\": [\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"太长\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"2\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"偏长\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"4\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"合适\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"5\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"偏短\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"3\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"太短\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"1\"\n" +
                        "                      }\n" +
                        "                    ]\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"markdown\",\n" +
                        "                    \"content\": \"会议效果评分\"\n" +
                        "                  },\n" +
                        "                  {\n" +
                        "                    \"tag\": \"select_static\",\n" +
                        "                    \"name\": \"effectiveness_score\",\n" +
                        "                    \"placeholder\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"请选择\"\n" +
                        "                    },\n" +
                        "                    \"options\": [\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"远超预期\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"5\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"超出预期\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"4\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"达到预期\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"3\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"部分达到预期\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"2\"\n" +
                        "                      },\n" +
                        "                      {\n" +
                        "                        \"text\": {\n" +
                        "                          \"tag\": \"plain_text\",\n" +
                        "                          \"content\": \"未达到预期\"\n" +
                        "                        },\n" +
                        "                        \"value\": \"1\"\n" +
                        "                      }\n" +
                        "                    ]\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"markdown\",\n" +
                        "                    \"content\": \"改进建议\"\n" +
                        "                  },\n" +
                        "                  {\n" +
                        "                    \"tag\": \"input\",\n" +
                        "                    \"name\": \"suggestions\",\n" +
                        "                    \"placeholder\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"请输入您的改进建议（最多500字）\"\n" +
                        "                    },\n" +
                        "                    \"max_length\": 500\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"tag\": \"column_set\",\n" +
                        "            \"columns\": [\n" +
                        "              {\n" +
                        "                \"tag\": \"column\",\n" +
                        "                \"width\": \"weighted\",\n" +
                        "                \"weight\": 1,\n" +
                        "                \"elements\": [\n" +
                        "                  {\n" +
                        "                    \"tag\": \"button\",\n" +
                        "                    \"text\": {\n" +
                        "                      \"tag\": \"plain_text\",\n" +
                        "                      \"content\": \"提交评价\"\n" +
                        "                    },\n" +
                        "                    \"type\": \"primary_filled\",\n" +
                        "                    \"behaviors\": [\n" +
                        "                      {\n" +
                        "                        \"type\": \"callback\",\n" +
                        "                        \"value\": \"{\\\"action\\\":\\\"submit_meeting_evaluation\\\",\\\"meeting_id\\\":\\\"%d\\\",\\\"evaluator_open_id\\\":\\\"%s\\\"}\"\n" +
                        "                      }\n" +
                        "                    ],\n" +
                        "                    \"form_action_type\": \"submit\",\n" +
                        "                    \"name\": \"submit_button\"\n" +
                        "                  }\n" +
                        "                ]\n" +
                        "              }\n" +
                        "            ]\n" +
                        "          }\n" +
                        "        ]\n" +
                        "      }\n" +
                        "    ]\n" +
                        "  }\n" +
                        "}",
                escapeJsonString(dto.getMeetingName()),
                escapeJsonString(dto.getMeetingTime()),
                escapeJsonString(dto.getEvaluatorName()),
                dto.getMeetingId(),
                dto.getOpenId()
        );

        log.debug("会议评价调查卡片JSON: {}", cardJson);
        return cardJson;
    }

    /**
     * JSON字符串转义
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    /**
     * 发送测试交互卡片
     */
    public CreateMessageRespBody sendTestInteractiveCard(String openId) {
        String cardContent = buildTestInteractiveCardContent();
        return tenantFeishuAppClient.getRobotService().sendCard(openId, cardContent);
    }

    /**
     * 构建测试交互卡片内容
     */
    private String buildTestInteractiveCardContent() {
        String currentTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        String cardJson = "{\n" +
                "  \"config\": {\n" +
                "    \"wide_screen_mode\": true\n" +
                "  },\n" +
                "  \"header\": {\n" +
                "    \"template\": \"blue\",\n" +
                "    \"title\": {\n" +
                "      \"tag\": \"plain_text\",\n" +
                "      \"content\": \"🧪 卡片交互测试\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"elements\": [\n" +
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"text\": {\n" +
                "        \"tag\": \"lark_md\",\n" +
                "        \"content\": \"**测试说明**\\n这是一个测试卡片，用于验证卡片交互功能。点击下方按钮后，卡片将在5秒后自动禁用。\"\n" +
                "      }\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"hr\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"fields\": [\n" +
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**测试时间**\\n" + currentTime + "\"\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**功能**\\n卡片交互回调测试\"\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"action\",\n" +
                "      \"actions\": [\n" +
                "        {\n" +
                "          \"tag\": \"button\",\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"plain_text\",\n" +
                "            \"content\": \"测试按钮1\"\n" +
                "          },\n" +
                "          \"type\": \"primary\",\n" +
                "          \"value\": \"{\\\"action\\\":\\\"test_action_1\\\",\\\"data\\\":\\\"test_data_1\\\"}\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"tag\": \"button\",\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"plain_text\",\n" +
                "            \"content\": \"测试按钮2\"\n" +
                "          },\n" +
                "          \"type\": \"default\",\n" +
                "          \"value\": \"{\\\"action\\\":\\\"test_action_2\\\",\\\"data\\\":\\\"test_data_2\\\"}\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        log.debug("测试交互卡片JSON: {}", cardJson);
        return cardJson;
    }

}

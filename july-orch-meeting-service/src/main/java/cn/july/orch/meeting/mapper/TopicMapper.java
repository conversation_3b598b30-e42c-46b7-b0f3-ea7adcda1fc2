package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TopicPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 议题Mapper
 * @date 2025-11-06
 */
@Mapper
public interface TopicMapper extends BaseMapper<TopicPO> {

    /**
     * 根据议题名称统计数量（排除指定ID）
     */
    int countByName(@Param("name") String name, @Param("excludeId") Long excludeId);
}

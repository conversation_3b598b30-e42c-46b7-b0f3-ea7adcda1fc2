package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议数据统计DTO
 * @date 2025-01-24
 */
@Data
public class MeetingDataStatisticsDTO {

    @ApiModelProperty("基础统计：会议总数、总时长、平均参会人数、临时会议占比")
    private MeetingBasicStatisticsDTO basicStatistics;

    @ApiModelProperty("会议标签分布（饼状图）")
    private List<MeetingTagDistributionDTO> tagDistribution;

    @ApiModelProperty("会议标签下临时会议和规划会议的占比")
    private List<TagMeetingTypeRatioDTO> tagMeetingTypeRatio;

    @ApiModelProperty("周一到周日每天的会议总数")
    private WeeklyMeetingStatisticsDTO weeklyStatistics;

    @ApiModelProperty("不同会议标签类型的会议总时长")
    private List<TagMeetingDurationDTO> tagDurationStatistics;

    @ApiModelProperty("人时成本最高的会议统计")
    private MeetingCostStatisticsDTO costStatistics;

    @ApiModelProperty("会议规划统计")
    private MeetingPlanStatisticsDTO planStatistics;

    @ApiModelProperty("部门会议数量对比")
    private DepartmentMeetingComparisonDTO departmentComparison;
}

package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.service.MeetingNotificationActionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 会议通知定时任务
 * @date 2025-01-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MeetingNotificationTask {

    private final MeetingNotificationActionService meetingNotificationActionService;

    /**
     * 会议提前通知定时任务
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    @IgnoreTenant
    public void sendAdvanceNotifications() {
        log.info("开始执行会议提前通知定时任务");
        meetingNotificationActionService.sendAdvanceNotifications();
    }
}

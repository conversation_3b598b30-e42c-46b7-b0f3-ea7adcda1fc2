package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 议程项DTO
 * @date 2025-08-26
 */
@Data
@ApiModel("议程项信息")
public class AgendaItemDTO {

    @ApiModelProperty("议程序号")
    private Integer sequence;

    @ApiModelProperty("议程主题")
    private String topic;

    @ApiModelProperty("建议持续时长(分钟)")
    private Integer suggestedDurationMinutes;

    @ApiModelProperty("议程描述")
    private String description;
}
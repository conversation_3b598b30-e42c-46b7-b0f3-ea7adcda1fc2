package cn.july.orch.meeting.domain.dto;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务列表DTO
 */
@Data
@ApiModel("任务列表信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskListDTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("关联的任务清单ID")
    private Long taskListId;

    @ApiModelProperty("父任务ID")
    private Long parentId;

    @ApiModelProperty("飞书任务ID")
    private String feishuTaskId;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("负责人OpenID")
    private String ownerOpenId;

    @ApiModelProperty("负责人名称")
    private String ownerName;

    @ApiModelProperty("负责人头像")
    private String ownerAvatarUrl;

    @ApiModelProperty("优先级")
    private TaskPriorityEnum priority;

    @ApiModelProperty("任务状态")
    private TaskStatusEnum status;

    @ApiModelProperty("截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dueDate;

    @ApiModelProperty("实际完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completedAt;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    @ApiModelProperty("关联的会议名称")
    private String meetingName;

    @ApiModelProperty("关联的会议标签列表")
    private List<SimpleMeetingTagDTO> meetingTags;

    @ApiModelProperty("附件列表")
    private List<String> attachmentsJson;

    @ApiModelProperty("附件详情列表")
    private List<FileInfoDTO> attachmentDetails;

    @ApiModelProperty("子任务列表")
    private List<TaskListDTO> subTasks;

    @ApiModelProperty("子任务统计 (格式: 已完成数/总数)")
    private String subTaskSummary;

    @ApiModelProperty("已完成子任务数")
    private Integer completedSubTasks;

    @ApiModelProperty("总子任务数")
    private Integer totalSubTasks;

    @ApiModelProperty("逻辑删除")
    private DeletedEnum deleted;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建用户名")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新用户ID")
    private String updateUserId;

    @ApiModelProperty("更新用户名")
    private String updateUserName;

    @ApiModelProperty("是否超期")
    private Boolean isOverdue;
}
package cn.july.orch.meeting.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.exception.BusinessException;
import cn.july.core.utils.CronUtils;
import cn.july.feishu.config.FeishuAppContext;
import cn.july.feishu.model.AttendUserModel;
import cn.july.feishu.model.CreateCalendarEventAttendeesModel;
import cn.july.feishu.model.GetMeetingModel;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.common.Constants;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.domain.CreateCalendarEventResult;
import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import com.lark.oapi.service.vc.v1.model.Meeting;
import com.lark.oapi.service.vc.v1.model.MeetingParticipant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新会议操作服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingActionService {

    private final NewMeetingDomainService newMeetingDomainService;
    private final NewMeetingAssembler newMeetingAssembler;
    private final FeishuCalendarActionService feishuCalendarActionService;
    private final TenantFeishuAppClient tenantFeishuAppClient;
    private final NewMeetingMapper newMeetingMapper;
    private final AiDocumentSummaryService aiDocumentSummaryService;
    private final FileDetailService fileDetailService;
    private final MeetingTagService meetingTagService;
    private final MeetingRoomStatusService meetingRoomStatusService;
    private final MeetingPlanMapper meetingPlanMapper;
    private final DepartmentUserQueryService departmentUserQueryService;


    /**
     * 创建会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMeeting(NewMeetingCreateCommand command) {
        log.info("开始创建新会议，命令：{}", command);

        // 处理部门ID，获取部门下所有用户并添加到参会人员列表
        List<String> attendeesFromDepartments = getAttendeesFromDepartments(command.getDepartmentIds());
        if (!attendeesFromDepartments.isEmpty()) {
            command.getAttendees().addAll(attendeesFromDepartments);
            log.info("从部门获取到 {} 个用户添加到参会人员列表", attendeesFromDepartments.size());
        }

        if (StrUtil.isNotBlank(command.getHostUserId())) {
            command.getAttendees().add(command.getHostUserId());
        }
        if (StrUtil.isNotBlank(command.getRecorderUserId())) {
            command.getAttendees().add(command.getRecorderUserId());
        }
        command.setAttendees(command.getAttendees().stream().distinct().collect(Collectors.toList()));

        // 校验会议室是否空闲
        if (command.getMeetingRoomId() != null && !meetingRoomStatusService.isMeetingRoomFree(command.getMeetingRoomId())) {
            throw new BusinessException("会议室已被占用，请选择其他会议室");
        }

        // 根据id获取完整的会议标签对象
        List<SimpleMeetingTagDTO> meetingTags = buildMeetingTagsFromIds(command.getMeetingTagIds());

        // 根据fileKey获取完整的文档对象
        List<PreMeetingDocumentDTO> preMeetingDocuments = buildPreMeetingDocumentsFromKeys(command.getPreMeetingDocumentKeys());

        // 转换为领域实体
        NewMeeting meeting = newMeetingAssembler.toEntity(command);

        // 设置获取到的完整对象
        meeting.setMeetingTags(meetingTags);
        meeting.setPreMeetingDocuments(preMeetingDocuments);

        // 先初始化文档汇总状态（不进行实际汇总）
        if (preMeetingDocuments != null && !preMeetingDocuments.isEmpty()) {
            aiDocumentSummaryService.initializeDocumentSummaryStatus(preMeetingDocuments);
        }

        // 创建飞书日程
        CreateCalendarEventResult calendarEventResult;
        try {
            calendarEventResult = feishuCalendarActionService.createCalendarEvent(meeting);

            // 设置飞书相关信息
            meeting.setFsCalendarEventId(calendarEventResult.getEventId());
            meeting.setMeetingUrl(calendarEventResult.getMeetingUrl());

            log.info("飞书日程创建成功，事件ID：{}，会议链接：{}",
                    calendarEventResult.getEventId(), calendarEventResult.getMeetingUrl());
        } catch (Exception e) {
            log.error("创建飞书日程失败，会议名称：{}", meeting.getMeetingName(), e);
            throw new BusinessException("创建飞书日程失败：" + e.getMessage());
        }

        // 创建会议（落库）
        newMeetingDomainService.createMeeting(meeting);
        meeting.setCreateUserId(CurrentUserHolder.getOpenId());

        // 更新会议室状态为已预定
        if (meeting.getMeetingRoomId() != null) {
            meetingRoomStatusService.updateMeetingRoomStatus(meeting.getMeetingRoomId(), MeetingRoomStatusEnum.RESERVED);
        }

        // 异步添加参会人员到飞书日程
        if (calendarEventResult != null) {
            addFSCalendarEventAttendeesAsync(meeting, calendarEventResult.getEventId(), FeishuAppContext.get());
        }

        log.info("新会议创建成功，ID：{}", meeting.getId());

        // 异步处理文档AI汇总并更新会议描述
        if (preMeetingDocuments != null && !preMeetingDocuments.isEmpty()
                && command.getEnableDocAiSummary() != null && command.getEnableDocAiSummary()) {
            String userAccessToken = FeishuAppContext.get().getUserAccessToken();
            processDocumentSummaryAsync(meeting, preMeetingDocuments, calendarEventResult.getEventId(), userAccessToken);
        }

        FeishuAppContext.remove();
        // 发送新会议创建事件
        // CreateNewMeetingEvent.CreateNewMeetingData createNewMeetingData = CreateNewMeetingEvent.CreateNewMeetingData.builder()
        //     .newMeeting(meeting)
        //     .build();
        // springEventPublish.publish(new CreateNewMeetingEvent(createNewMeetingData));
    }

    /**
     * 更新会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeeting(NewMeetingUpdateCommand command) {
        log.info("开始更新新会议，命令：{}", command);

        // 获取原有会议信息
        NewMeeting existingMeeting = newMeetingDomainService.findById(command.getId());
        if (existingMeeting == null) {
            throw new BusinessException("会议不存在");
        }

        // 状态校验：只有未开始的会议可以更新
        if (existingMeeting.getStatus().getCode() > NewMeetingStatusEnum.NOT_STARTED.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_UPDATE);
        }

        // 处理部门ID，获取部门下所有用户并添加到参会人员列表
        List<String> attendeesFromDepartments = getAttendeesFromDepartments(command.getDepartmentIds());
        if (!attendeesFromDepartments.isEmpty()) {
            command.getAttendees().addAll(attendeesFromDepartments);
            log.info("从部门获取到 {} 个用户添加到参会人员列表", attendeesFromDepartments.size());
        }

        // 参会人员去重处理
        command.setAttendees(command.getAttendees().stream().distinct().collect(Collectors.toList()));

        // 参会人员重复校验
        HashSet<String> userIds = new HashSet<>();
        command.getAttendees().forEach(userId -> {
            if (!userIds.add(userId)) {
                throw new BusinessException(MessageCode.EXIST_PERSONNEL_DUPLICATION, userId);
            }
        });

        // 校验会议室是否空闲（如果会议室ID发生了变化）
        if (command.getMeetingRoomId() != null && !command.getMeetingRoomId().equals(existingMeeting.getMeetingRoomId())) {
            if (!meetingRoomStatusService.isMeetingRoomFree(command.getMeetingRoomId())) {
                throw new BusinessException("会议室已被占用，请选择其他会议室");
            }
        }

        // 转换为领域实体
        NewMeeting meeting = newMeetingAssembler.toEntity(command);

        // 保留原有会议的飞书相关信息
        meeting.setFsCalendarEventId(existingMeeting.getFsCalendarEventId());
        meeting.setFsMeetingId(existingMeeting.getFsMeetingId());
        meeting.setMeetingUrl(existingMeeting.getMeetingUrl());
        meeting.setMinuteUrl(existingMeeting.getMinuteUrl());
        meeting.setCreateUserId(existingMeeting.getCreateUserId());
        meeting.setCreateUserName(existingMeeting.getCreateUserName());
        meeting.setCreateTime(existingMeeting.getCreateTime());

        // 更新会议
        newMeetingDomainService.updateMeeting(meeting);

        // 更新会议室状态
        // 如果会议室ID发生了变化，需要更新旧会议室状态为FREE，新会议室状态为RESERVED
        if (existingMeeting.getMeetingRoomId() != null && !existingMeeting.getMeetingRoomId().equals(meeting.getMeetingRoomId())) {
            // 将旧会议室状态更新为FREE
            meetingRoomStatusService.updateMeetingRoomStatus(existingMeeting.getMeetingRoomId(), MeetingRoomStatusEnum.FREE);
        }
        if (meeting.getMeetingRoomId() != null) {
            // 将新会议室状态更新为RESERVED
            meetingRoomStatusService.updateMeetingRoomStatus(meeting.getMeetingRoomId(), MeetingRoomStatusEnum.RESERVED);
        }

        // 更新飞书日程
        if (existingMeeting.getFsCalendarEventId() != null) {
            try {
                feishuCalendarActionService.updateCalendarEvent(meeting);

                // 处理参会人员变更
                handleAttendeeChanges(existingMeeting, meeting);

                log.info("飞书日程更新成功，会议ID：{}", meeting.getId());
            } catch (Exception e) {
                log.error("更新飞书日程失败，会议ID：{}", meeting.getId(), e);
                throw new BusinessException("更新飞书日程失败：" + e.getMessage());
            }
        }

        // 处理附件变更和AI汇总
        if (command.getPreMeetingDocuments() != null && command.getEnableDocAiSummary() != null && command.getEnableDocAiSummary()) {
            // 检查附件是否发生变化
            boolean attachmentsChanged = !Objects.equals(existingMeeting.getPreMeetingDocuments(), command.getPreMeetingDocuments());

            if (attachmentsChanged) {
                log.info("检测到会议附件变更，开始处理AI汇总，会议ID：{}", meeting.getId());

                // 根据fileKey获取完整的文档对象
                List<PreMeetingDocumentDTO> preMeetingDocuments = buildPreMeetingDocumentsFromKeys(command.getPreMeetingDocuments());

                // 异步处理文档AI汇总并创建云文档
                String userAccessToken = FeishuAppContext.get().getUserAccessToken();
                processDocumentSummaryAsync(meeting, preMeetingDocuments, existingMeeting.getFsCalendarEventId(), userAccessToken);
            }
        }
    }

    /**
     * 删除会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeeting(Long id) {
        log.info("开始删除新会议，ID：{}", id);

        // 获取会议信息
        NewMeeting meeting = newMeetingDomainService.findById(id);
        if (meeting == null) {
            return;
        }

        // 状态校验：只有未开始的会议可以删除
        if (meeting.getStatus().getCode() > NewMeetingStatusEnum.NOT_STARTED.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_DELETE);
        }

        // 删除飞书日程
        if (meeting.getFsCalendarEventId() != null) {
            try {
                feishuCalendarActionService.deleteCalendarEvent(meeting.getFsCalendarEventId());
                log.info("飞书日程删除成功，会议ID：{}", id);
            } catch (Exception e) {
                log.error("删除飞书日程失败，会议ID：{}", id, e);
                throw new BusinessException("删除飞书日程失败：" + e.getMessage());
            }
        }

        // 删除会议
        newMeetingDomainService.deleteMeeting(id);
        log.info("新会议删除成功，ID：{}", id);
    }

    /**
     * 更新会议状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingStatus(Long id, NewMeetingStatusEnum status) {
        log.info("开始更新会议状态，ID：{}，状态：{}", id, status);
        newMeetingDomainService.updateMeetingStatus(id, status);
        log.info("会议状态更新成功，ID：{}，状态：{}", id, status);
    }

    /**
     * 处理飞书回调 - 会议开始事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuMeetingStartCallback(String fsCalendarEventId, String meetingNo, String fsMeetingId, String startTime) {
        log.info("处理飞书会议开始回调，日程事件ID：{}，会议编号：{}", fsCalendarEventId, meetingNo);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            // 生成会议链接
//            String meetingUrl = feishuCalendarActionService.getMeetingUrl(fsMeetingId);
            String meetingUrl = null;

            // 更新会议编号、飞书会议ID和会议链接
            newMeetingDomainService.updateMeetingInfo(meeting.getId(), meetingNo, fsMeetingId, meetingUrl);

            // 更新会议状态为进行中
            newMeetingDomainService.updateMeetingStatus(meeting.getId(), NewMeetingStatusEnum.IN_PROCESS);
            // 更新会议规划为进行中
            meetingPlanMapper.updateStatusByIds(Collections.singletonList(meeting.getMeetingPlanId()), MeetingPlanStatusEnum.IN_PROGRESS);

            // 更新会议室状态为使用中
            if (meeting.getMeetingRoomId() != null) {
                meetingRoomStatusService.updateMeetingRoomStatus(meeting.getMeetingRoomId(), MeetingRoomStatusEnum.IN_USE);
            }

            log.info("飞书会议开始回调处理成功，会议ID：{}，会议编号：{}", meeting.getId(), meetingNo);
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 处理飞书回调 - 会议结束事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuMeetingEndCallback(String fsCalendarEventId, String endTime) {
        log.info("处理飞书会议结束回调，日程事件ID：{}", fsCalendarEventId);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            // 更新会议状态为已结束
            newMeetingDomainService.updateMeetingStatus(meeting.getId(), NewMeetingStatusEnum.ENDED);

            // 更新会议室状态为空闲
            if (meeting.getMeetingRoomId() != null) {
                meetingRoomStatusService.updateMeetingRoomStatus(meeting.getMeetingRoomId(), MeetingRoomStatusEnum.FREE);
            }

            // 获取飞书会议ID，如果存在则获取实际会议信息
            if (meeting.getFsMeetingId() != null && !meeting.getFsMeetingId().isEmpty()) {
                try {
                    updateMeetingActualInfoFromFeishu(meeting.getId(), meeting.getFsMeetingId());
                } catch (Exception e) {
                    log.error("获取飞书会议实际信息失败，会议ID：{}", meeting.getId(), e);
                }
            }

            // 会议规划状态处理
            Long meetingPlanId = meeting.getMeetingPlanId();
            if (meetingPlanId != null) {
                meetingPlanMapper.updateStatusByIds(Collections.singletonList(meetingPlanId), MeetingPlanStatusEnum.COMPLETED);
                MeetingPlanPO meetingPlanPO = meetingPlanMapper.selectById(meetingPlanId);
                if (StrUtil.isNotBlank(meetingPlanPO.getCron())) {
                    MeetingPlanPO newPlanPO = meetingPlanMapper.selectByUuidAndNotStart(meetingPlanPO.getUuid());
                    if (ObjUtil.isNull(newPlanPO)) {
                        newPlanPO = new MeetingPlanPO();
                        BeanUtils.copyProperties(meetingPlanPO, newPlanPO);
                        LocalDateTime nextTime = CronUtils.nextExecutionTime(meetingPlanPO.getCron(), LocalDateTime.now());
                        if (nextTime.isBefore(meetingPlanPO.getRecurrenceEndDate())) {
                            newPlanPO.setPlannedStartTime(nextTime);
                            newPlanPO.setPlannedEndTime(nextTime.plusMinutes(meetingPlanPO.getPlannedDuration()));
                            newPlanPO.setStatus(MeetingPlanStatusEnum.NOT_STARTED);
                            newPlanPO.setAdvanceNoticeSent(0);
                            newPlanPO.setId(null);
                            meetingPlanMapper.insert(newPlanPO);
                        }
                    }
                }
            }

            log.info("飞书会议结束回调处理成功，会议ID：{}", meeting.getId());
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 处理飞书回调 - 会议录制完成事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuRecordReadyCallback(String fsMeetingId, String minuteUrl) {
        log.info("处理飞书会议录制完成回调，飞书会议ID：{}", fsMeetingId);

        NewMeeting meeting = newMeetingDomainService.findByFsMeetingId(fsMeetingId);
        if (meeting != null) {
            // 更新妙计链接
            newMeetingDomainService.updateMinuteUrl(meeting.getId(), minuteUrl);

            log.info("飞书会议录制完成回调处理成功，会议ID：{}，妙计链接：{}", meeting.getId(), minuteUrl);
        } else {
            log.warn("未找到对应的会议，飞书会议ID：{}", fsMeetingId);
        }
    }

    /**
     * 异步添加飞书日程参会人员
     */
    @Async
    public void addFSCalendarEventAttendeesAsync(NewMeeting meeting, String fsCalendarEventId, FeishuAppContext feishuAppContext) {
        try {
            // 异步上下文传递
            FeishuAppContext.set(feishuAppContext);

            if (meeting.getAttendees() != null && !meeting.getAttendees().isEmpty()) {
                // 构建参会人员模型
                List<AttendUserModel> attendUsers = meeting.getAttendees().stream()
                        .map(userId -> AttendUserModel.builder()
                                .userId(userId)
                                .isOrganizer(meeting.getCreateUserId().equals(userId))
                                .build())
                        .collect(Collectors.toList());

                // 调用飞书API添加参会人员
                tenantFeishuAppClient.getCalendarEventService().createAttendees(CreateCalendarEventAttendeesModel.builder()
                        .calendarId(feishuCalendarActionService.getPrimaryCalendarId())
                        .eventId(fsCalendarEventId)
                        .attendUsers(attendUsers)
                        .build());

                log.info("异步添加飞书日程参会人员成功，会议ID：{}", meeting.getId());
            }
        } catch (Exception e) {
            log.error("异步添加飞书日程参会人员失败，会议ID：{}", meeting.getId(), e);
        }
    }

    /**
     * 处理参会人员变更
     */
    private void handleAttendeeChanges(NewMeeting oldMeeting, NewMeeting newMeeting) {
        // 这里可以实现参会人员的增删改逻辑
        // 由于简化设计，这里只做基本处理
        // 实际项目中可能需要更复杂的逻辑来处理参会人员的变更

        if (oldMeeting.getAttendees() != null && newMeeting.getAttendees() != null) {
            // 找出新增的参会人员
            List<String> addedAttendees = newMeeting.getAttendees().stream()
                    .filter(attendee -> !oldMeeting.getAttendees().contains(attendee))
                    .collect(Collectors.toList());

            // 找出删除的参会人员
            List<String> removedAttendees = oldMeeting.getAttendees().stream()
                    .filter(attendee -> !newMeeting.getAttendees().contains(attendee))
                    .collect(Collectors.toList());

            // 添加新参会人员
            if (!addedAttendees.isEmpty()) {
                feishuCalendarActionService.addCalendarEventAttendees(
                        newMeeting.getFsCalendarEventId(),
                        addedAttendees,
                        newMeeting.getCreateUserId()
                );
            }

            // 删除参会人员
            if (!removedAttendees.isEmpty()) {
                feishuCalendarActionService.deleteCalendarEventAttendees(
                        newMeeting.getFsCalendarEventId(),
                        removedAttendees
                );
            }
        }
    }

    /**
     * 从飞书获取会议实际执行信息并更新
     */
    private void updateMeetingActualInfoFromFeishu(Long meetingId, String fsMeetingId) {
        try {
            // 调用飞书API获取会议详情
            GetMeetingModel fsMeetingReq = GetMeetingModel.builder()
                    .meetingId(fsMeetingId)
                    .withParticipants(true)
                    .userIdType(Constants.OPEN_ID)
                    .build();
            GetMeetingRespBody response = tenantFeishuAppClient.getMeetService().getMeeting(fsMeetingReq);

            if (response != null && response.getMeeting() != null) {
                Meeting fsMeeting = response.getMeeting();

                // 获取实际开始时间和结束时间
                LocalDateTime actualStartTime = null;
                LocalDateTime actualEndTime = null;

                if (fsMeeting.getStartTime() != null) {
                    actualStartTime = LocalDateTime.ofEpochSecond(Long.parseLong(fsMeeting.getStartTime()), 0, ZoneOffset.ofHours(8));
                }
                if (fsMeeting.getEndTime() != null) {
                    actualEndTime = LocalDateTime.ofEpochSecond(Long.parseLong(fsMeeting.getEndTime()), 0, ZoneOffset.ofHours(8));
                }

                // 获取实际参会人员open_id列表
                List<String> actualAttendees = null;
                if (fsMeeting.getParticipants() != null && fsMeeting.getParticipants().length > 0) {
                    actualAttendees = Arrays.stream(fsMeeting.getParticipants())
                            .map(MeetingParticipant::getId)
                            .collect(Collectors.toList());
                }

                // 更新会议实际执行信息
                NewMeetingPO meetingPO = newMeetingMapper.selectById(meetingId);
                if (meetingPO != null) {
                    meetingPO.setActualStartTime(actualStartTime);
                    meetingPO.setActualEndTime(actualEndTime);
                    meetingPO.setActualAttendees(actualAttendees);

                    newMeetingMapper.updateById(meetingPO);

                    log.info("会议实际执行信息更新成功，会议ID：{}，实际开始时间：{}，实际结束时间：{}，实际参会人数：{}",
                            meetingId, actualStartTime, actualEndTime,
                            actualAttendees != null ? actualAttendees.size() : 0);
                }
            }
        } catch (Exception e) {
            log.error("从飞书获取会议实际信息失败，会议ID：{}，飞书会议ID：{}，错误：{}",
                    meetingId, fsMeetingId, e.getMessage(), e);
            throw new RuntimeException("获取飞书会议实际信息失败", e);
        }
    }

    /**
     * 异步处理文档AI汇总并更新会议描述
     */
    @Async
    public void processDocumentSummaryAsync(NewMeeting meeting, List<PreMeetingDocumentDTO> preMeetingDocuments, String fsCalendarEventId, String userAccessToken) {
        Long meetingId = meeting.getId();
        log.info("开始异步处理会议{}的文档AI汇总", meetingId);

        try {
            if (userAccessToken == null) {
                log.error("无法获取用户访问token，会议ID：{}", meetingId);
                return;
            }

            // 调用AiDocumentSummaryService处理文档汇总
            aiDocumentSummaryService.processDocumentSummaryAsync(meeting, preMeetingDocuments, fsCalendarEventId, userAccessToken);
        } catch (Exception e) {
            log.error("异步处理文档AI汇总失败，会议ID：{}", meetingId, e);
        }
    }

    /**
     * 根据会议标签ID列表构建完整的会议标签对象
     */
    private List<SimpleMeetingTagDTO> buildMeetingTagsFromIds(List<Long> meetingTagIds) {
        if (meetingTagIds == null || meetingTagIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<SimpleMeetingTagDTO> meetingTags = new ArrayList<>();
        for (Long tagId : meetingTagIds) {
            try {
                // 使用MeetingTagService获取标签信息
                MeetingTagDTO meetingTagDTO = meetingTagService.getById(tagId);
                if (meetingTagDTO != null) {
                    // 转换为SimpleMeetingTagDTO（轻量级）
                    SimpleMeetingTagDTO tag = SimpleMeetingTagDTO.builder()
                            .id(meetingTagDTO.getId())
                            .name(meetingTagDTO.getName())
                            .color(meetingTagDTO.getColor())
                            .build();
                    meetingTags.add(tag);
                    log.debug("成功获取会议标签，ID：{}，名称：{}", tagId, meetingTagDTO.getName());
                } else {
                    log.warn("未找到会议标签，ID：{}", tagId);
                }
            } catch (Exception e) {
                log.error("获取会议标签失败，ID：{}", tagId, e);
            }
        }
        return meetingTags;
    }

    /**
     * 根据fileKey列表构建完整的会前文档对象
     */
    private List<PreMeetingDocumentDTO> buildPreMeetingDocumentsFromKeys(List<String> fileKeys) {
        if (fileKeys == null || fileKeys.isEmpty()) {
            return new ArrayList<>();
        }

        List<PreMeetingDocumentDTO> documents = new ArrayList<>();
        for (String fileKey : fileKeys) {
            try {
                // 通过FileDetailService获取文件信息
                FileDetailPO fileDetail = fileDetailService.getById(fileKey);
                if (fileDetail != null) {
                    PreMeetingDocumentDTO document = PreMeetingDocumentDTO.builder()
                            .fileKey(fileKey)
                            .fileName(fileDetail.getOriginalFilename())
                            .fileSize(fileDetail.getSize())
                            .fileType(fileDetail.getContentType())
                            .aiSummaryStatus("PENDING")
                            .aiSummaryContent("")
                            .build();
                    documents.add(document);
                    log.debug("成功获取会前文档信息，fileKey：{}，文件名：{}", fileKey, fileDetail.getOriginalFilename());
                } else {
                    log.warn("未找到文件信息，fileKey：{}", fileKey);
                }
            } catch (Exception e) {
                log.error("获取会前文档信息失败，fileKey：{}", fileKey, e);
            }
        }

        log.info("成功构建会前文档列表，总数：{}", documents.size());
        return documents;
    }

    /**
     * 从部门ID列表获取所有用户（包括子部门）
     * 
     * @param departmentIds 部门ID列表
     * @return 所有部门下的用户OpenID列表（已去重）
     */
    private List<String> getAttendeesFromDepartments(List<String> departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            log.info("开始从部门获取用户，部门数量：{}", departmentIds.size());
            
            // 调用DepartmentUserQueryService获取所有部门的用户汇总列表
            List<String> userOpenIds = departmentUserQueryService.getAllUsersFromDepartments(departmentIds);
            
            log.info("从部门获取用户成功，总用户数：{}", userOpenIds.size());
            
            return userOpenIds;
        } catch (Exception e) {
            log.error("从部门获取用户失败", e);
            // 失败时返回空列表，不影响主流程
            return new ArrayList<>();
        }
    }
}

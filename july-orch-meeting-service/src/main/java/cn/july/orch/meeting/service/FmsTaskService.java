package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.enums.DeletedEnum;
import cn.july.core.model.page.PageResultDTO;
import cn.july.core.utils.jackson.JsonUtils;
import cn.july.orch.meeting.domain.command.CreateTaskCommand;
import cn.july.orch.meeting.domain.dto.FmsTaskDTO;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.po.FmsTaskConfigPO;
import cn.july.orch.meeting.domain.po.FmsTaskPO;
import cn.july.orch.meeting.domain.query.FmsTaskQuery;
import cn.july.orch.meeting.enums.FmsTaskStatusEnum;
import cn.july.orch.meeting.enums.FmsTaskTypeEnum;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.mapper.FmsTaskConfigMapper;
import cn.july.orch.meeting.mapper.FmsTaskMapper;
import cn.july.orch.meeting.utils.AsyncTaskExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Assistant
 * @description FMS任务服务
 */
@Slf4j
@Service
public class FmsTaskService {

    @Resource
    private FmsTaskMapper fmsTaskMapper;

    @Resource
    private FmsTaskConfigMapper fmsTaskConfigMapper;

    @Resource
    private FileDetailService fileDetailService;

    @Resource
    private LocalMethodTaskProcessor localMethodTaskProcessor;

    /**
     * 创建任务
     */
    public Long createTask(CreateTaskCommand command) {
        // 1. 验证任务配置
        FmsTaskConfigPO config = validateTaskConfig(command.getTaskCode());

        // 2. 创建任务记录
        FmsTaskPO task = createTaskRecord(command, config);

        // 3. 异步执行任务
        executeTaskAsync(task.getId());

        return task.getId();
    }

    /**
     * 分页查询任务列表
     */
    public PageResultDTO<FmsTaskDTO> page(FmsTaskQuery query) {
        // 使用XML实现分页查询
        Page<FmsTaskDTO> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<FmsTaskDTO> result = fmsTaskMapper.selectTaskPage(page, query);

        return new PageResultDTO<>(
                query.getPageNo(), query.getPageSize(), result.getTotal(), result.getRecords());
    }

    /**
     * 异步执行任务
     */
    @Async("fmsTaskExecutor")
    public void executeTaskAsync(Long taskId) {
        CompletableFuture.runAsync(() -> {
            try {
                // 1. 更新状态为执行中
                fmsTaskMapper.updateTaskStatus(taskId, FmsTaskStatusEnum.RUNNING, null);

                // 2. 获取任务和配置信息
                FmsTaskPO task = fmsTaskMapper.selectById(taskId);
                FmsTaskConfigPO config = fmsTaskMapper.selectConfigByTaskCode(task.getTaskCode());

                // 3. 根据任务类型执行不同逻辑
                if (FmsTaskTypeEnum.DOWNLOAD.equals(task.getTaskType())) {
                    executeDownloadTask(config, task);
                } else {
                    executeUploadTask(config, task);
                }

                // 4. 更新任务状态为成功
                fmsTaskMapper.updateTaskStatus(taskId, FmsTaskStatusEnum.SUCCESS, null);

            } catch (Exception e) {
                fmsTaskMapper.updateTaskStatus(taskId, FmsTaskStatusEnum.FAILED, e.getMessage());
                log.error("任务执行失败: taskId={}", taskId, e);
            }
        }, AsyncTaskExecutor.fmsTaskExecutor);
    }

    /**
     * 执行下载任务
     */
    private void executeDownloadTask(FmsTaskConfigPO config, FmsTaskPO task) {
        try {
            // 解析任务参数为Map
            Map<String, Object> params = JsonUtils.parseToObjectMap(task.getTaskParams());
            
            // 执行目标方法
            FileDetailPO fileDetail = localMethodTaskProcessor.executeMethod(config.getTargetClass(), config.getTargetMethod(), params);
            
            if (fileDetail != null) {
                fmsTaskMapper.updateTaskFileInfo(task.getId(), fileDetail.getId(),
                        fileDetail.getFilename(), fileDetail.getUrl());
            }

        } catch (Exception e) {
            throw new RuntimeException("下载任务执行失败", e);
        }
    }

    /**
     * 执行上传任务
     */
    private void executeUploadTask(FmsTaskConfigPO config, FmsTaskPO task) {
        // 上传逻辑暂不实现，只保留字段和判断
        log.info("上传任务完成: taskId={}", task.getId());
    }

    /**
     * 验证任务配置
     */
    private FmsTaskConfigPO validateTaskConfig(String taskCode) {
        LambdaQueryWrapper<FmsTaskConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FmsTaskConfigPO::getTaskCode, taskCode)
                .eq(FmsTaskConfigPO::getIsEnabled, 1)
                .eq(FmsTaskConfigPO::getDeleted, DeletedEnum.NOT_DELETED);

        FmsTaskConfigPO config = fmsTaskConfigMapper.selectOne(queryWrapper);
        if (config == null) {
            throw new BusinessException(MessageCode.TASK_CONFIG_NOT_FOUND);
        }
        return config;
    }

    /**
     * 创建任务记录
     */
    private FmsTaskPO createTaskRecord(CreateTaskCommand command, FmsTaskConfigPO config) {
        FmsTaskPO task = new FmsTaskPO();
        task.setTaskCode(command.getTaskCode());
        task.setTaskType(config.getTaskType());
        task.setTaskStatus(FmsTaskStatusEnum.PENDING);
        task.setTaskParams(JsonUtils.toJson(command.getParams()));

        fmsTaskMapper.insert(task);
        return task;
    }
}

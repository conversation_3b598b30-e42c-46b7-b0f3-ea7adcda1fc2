package cn.july.orch.meeting.domain.entity;

import cn.july.orch.meeting.enums.ReportStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingAnalysisReport {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联的会议ID
     */
    private Long meetingId;

    /**
     * 状态（0-生成中 1-已生成 2-生成失败）
     */
    private ReportStatusEnum status;

    /**
     * AI纪要状态（0-生成中 1-已生成 2-生成失败）
     */
    private ReportStatusEnum aiTranscriptStatus;

    /**
     * AI生成的会议纪要全文 (Markdown格式)
     */
    private String aiTranscriptMd;

    /**
     * 综合评分 (0-100)
     */
    private Integer overallScore;

    /**
     * 综合评分的描述文字
     */
    private String overallSummary;

    /**
     * 包含内容质量各项评分和AI分析摘要的JSON对象
     */
    private String contentAnalysisJson;

    /**
     * 包含所有AI优化建议的JSON数组
     */
    private String aiSuggestionsJson;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
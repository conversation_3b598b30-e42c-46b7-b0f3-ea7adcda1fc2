package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Assistant
 * @description 任务清单更新命令
 */
@Data
@ApiModel("任务清单更新命令")
public class TaskListUpdateCommand {

    @ApiModelProperty(value = "清单ID", required = true)
    @NotNull(message = "清单ID不能为空")
    private Long id;

    @ApiModelProperty(value = "清单名称", required = true)
    @NotBlank(message = "清单名称不能为空")
    @Size(max = 100, message = "清单名称长度不能超过100个字符")
    private String name;

    @ApiModelProperty(value = "父清单ID")
    private Long parentId;

    @ApiModelProperty(value = "清单描述")
    @Size(max = 500, message = "清单描述长度不能超过500个字符")
    private String description;
}
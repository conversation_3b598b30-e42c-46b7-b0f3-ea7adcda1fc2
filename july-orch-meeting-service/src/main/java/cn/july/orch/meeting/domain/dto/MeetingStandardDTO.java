package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准DTO
 * @date 2025-01-24
 */
@Data
@ApiModel("会议标准信息")
public class MeetingStandardDTO {

    @ApiModelProperty("会议标准ID")
    private Long id;

    @ApiModelProperty("标准名称")
    private String standardName;

    @ApiModelProperty("标准描述")
    private String description;

    @ApiModelProperty("默认持续时长(分钟)")
    private Integer defaultDuration;

    @ApiModelProperty("默认提前通知数值")
    private Integer advanceNoticeValue;

    @ApiModelProperty("默认提前通知时间单位")
    private TimeUnitEnum advanceNoticeUnit;

    @ApiModelProperty("默认优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty("默认会议地点")
    private String defaultLocation;

    @ApiModelProperty("迟到容许时间(分钟)")
    private Integer lateToleranceMinutes;

    @ApiModelProperty("人员必要角色列表")
    private List<MeetingRoleEnum> requiredRoles;

    @ApiModelProperty("最少参会人数")
    private Integer minAttendees;

    @ApiModelProperty("最多参会人数")
    private Integer maxAttendees;

    @ApiModelProperty("会议要点列表")
    private List<String> meetingPoints;

    @ApiModelProperty("议程规划")
    private List<AgendaItemDTO> agendaPlan;

    @ApiModelProperty("签到配置")
    private CheckInConfigDTO checkInConfig;

    @ApiModelProperty("关联的标签列表")
    private List<MeetingTagDTO> tags;

    @ApiModelProperty("是否启用(0-否,1-是)")
    private Integer isEnabled;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人ID")
    private String updateUserId;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}

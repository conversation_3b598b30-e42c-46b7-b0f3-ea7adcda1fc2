package cn.july.orch.meeting.config;

import cn.july.orch.meeting.domain.dto.AgendaItemDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议程计划JSON类型处理器
 * @date 2025-08-26
 */
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class AgendaPlanTypeHandler extends BaseTypeHandler<List<AgendaItemDTO>> {

    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<AgendaItemDTO> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
            } catch (JsonProcessingException e) {
                log.error("议程计划序列化失败", e);
                ps.setString(i, null);
            }
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public List<AgendaItemDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseAgendaPlan(value);
    }

    @Override
    public List<AgendaItemDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseAgendaPlan(value);
    }

    @Override
    public List<AgendaItemDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseAgendaPlan(value);
    }

    private List<AgendaItemDTO> parseAgendaPlan(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(value, new TypeReference<List<AgendaItemDTO>>() {});
        } catch (JsonProcessingException e) {
            log.error("议程计划反序列化失败: {}", value, e);
            return new ArrayList<>();
        }
    }
}
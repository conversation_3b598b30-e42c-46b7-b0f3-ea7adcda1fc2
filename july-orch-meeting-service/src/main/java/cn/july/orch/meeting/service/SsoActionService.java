package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.AuthUserAccessTokenModel;
import cn.july.feishu.model.ContactSearchUserDTO;
import cn.july.feishu.model.ContactSearchUserResponse;
import cn.july.feishu.model.UserDTO;
import cn.july.orch.meeting.assembler.ResourceAssembler;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.common.CacheConstants;
import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.domain.command.LogoutToolCommand;
import cn.july.orch.meeting.domain.dto.AuthConfigDTO;
import cn.july.orch.meeting.domain.dto.UserTokenDTO;
import cn.july.orch.meeting.domain.po.SysResourcePO;
import cn.july.orch.meeting.domain.query.SsoUserLoginQuery;
import cn.july.orch.meeting.domain.query.SsoUserTokenQuery;
import cn.july.orch.meeting.enums.ClientTypeEnum;
import cn.july.orch.meeting.exception.AuthException;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.mapper.SysResourceMapper;
import cn.july.orch.meeting.properties.MeetingSeverProperties;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SsoActionService {

    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;
    @Resource
    private UserInfoAssembler userInfoAssembler;
    @Resource
    private SysResourceMapper sysResourceMapper;
    @Resource
    private ResourceAssembler resourceAssembler;
    @Resource
    private TenantFeishuClientManager tenantFeishuClientManager;

    /**
     * 免登过程-获取token
     */
    public UserTokenDTO getUserToken(SsoUserLoginQuery query) {
//        if(ObjUtil.isNull(query.getTenantId())){
//            query.setTenantId(1L);
//        }
        FeishuAppClient client = tenantFeishuClientManager.getClient(query.getTenantId());
        AuthUserAccessTokenModel authUserAccessTokenModel = client.getAuthService().authUserAccessToken(query.getCode(), query.getRedirectUri());
        String uuid = IdUtil.fastSimpleUUID();
        String token = Base64.getUrlEncoder().withoutPadding().encodeToString(uuid.getBytes());
        long timeOut = meetingSeverProperties.getLogin().getTimeOut();
        generateFsUserInfo(token, authUserAccessTokenModel.getUserAccessToken(), query.getClientType(), timeOut,query.getTenantId());
        return UserTokenDTO.builder()
                .token(token)
                .timeOut(meetingSeverProperties.getLogin().getTimeOut())
                .build();
    }

    public UserInfoDTO getUserInfo(String token) {
        UserInfoDTO userInfoDTO = this.getCacheUserInfo(token);
        String userAccessToken = userInfoDTO.getUserAccessToken();
        FeishuAppClient client = tenantFeishuClientManager.getClient(userInfoDTO.getTenantId());
        AuthUserAccessTokenModel userAccessTokenModel = client.getAuthService().getUserAccessToken(userAccessToken);
        if (!userInfoDTO.getUserAccessToken().equals(userAccessTokenModel.getUserAccessToken())) {
            Long expire = stringRedisTemplate.getExpire(CacheConstants.getAuthToken(token));
            long timeOut = meetingSeverProperties.getLogin().getTimeOut();
            return this.generateFsUserInfo(token, userAccessTokenModel.getUserAccessToken(), userInfoDTO.getClientType(), Optional.ofNullable(expire).orElse(timeOut),userInfoDTO.getTenantId());
        } else {
            log.info("token:{}无须刷新", token);
        }
        return userInfoDTO;
    }

    public UserInfoDTO getCacheUserInfo(String token) {
        String userInfo = stringRedisTemplate.opsForValue().get(CacheConstants.getAuthToken(token));
        if (StrUtil.isBlank(userInfo)) {
            throw new AuthException(MessageCode.AUTH_FAIL);
        }
        return JsonUtils.parse(userInfo, UserInfoDTO.class);
    }

    private UserInfoDTO generateFsUserInfo(String token, String userToken, ClientTypeEnum clientType, long timeOut,Long tenantId) {
        FeishuAppClient client = tenantFeishuClientManager.getClient(tenantId);
        GetUserInfoRespBody respBody = client.getAuthService().getUserInfo(userToken);
        UserInfoDTO userInfoDTO = userInfoAssembler.resp2DTO(respBody);
        userInfoDTO.setToken(token);
        userInfoDTO.setClientType(clientType);
        userInfoDTO.setLastRefreshTime(LocalDateTime.now());
        userInfoDTO.setUserAccessToken(userToken);
        userInfoDTO.setTenantId(tenantId);
        stringRedisTemplate.opsForValue().set(CacheConstants.getAuthToken(token), JsonUtils.toJson(userInfoDTO), timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.opsForValue().set(CacheConstants.getFeishuOpenId(clientType.getCode(), userInfoDTO.getOpenId()), JsonUtils.toJson(userInfoDTO), timeOut, TimeUnit.SECONDS);
        return userInfoDTO;
    }

    public Boolean refreshToken(SsoUserTokenQuery query) {
        UserInfoDTO cacheUserInfo = this.getCacheUserInfo(query.getToken());
        long timeOut = meetingSeverProperties.getLogin().getTimeOut();
        stringRedisTemplate.expire(CacheConstants.getAuthToken(query.getToken()), timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.expire(CacheConstants.getFeishuOpenId(cacheUserInfo.getClientType().getCode(), cacheUserInfo.getOpenId()), timeOut, TimeUnit.SECONDS);
        return true;
    }


    public Boolean logout(String token) {
        stringRedisTemplate.delete(CacheConstants.getAuthToken(token));
        return true;
    }

    public Boolean logoutTool(LogoutToolCommand command) {
        if (CollUtil.isNotEmpty(command.getOpenIdList())) {
            command.getOpenIdList().forEach(this::logoutByOpenId);
        }
        if (StrUtil.isNotBlank(command.getName())) {
            ContactSearchUserDTO dto = ContactSearchUserDTO.builder().query(command.getName()).build();
            ContactSearchUserResponse resp = tenantFeishuAppClient.getContactService().searchUser(dto);
            if (ObjUtil.isNotNull(resp) && !resp.getUsers().isEmpty()) {
                List<String> openIds = resp.getUsers().stream().map(UserDTO::getOpenId).distinct().collect(Collectors.toList());
                log.info("按名称登出用户,name:{},openIds:{}", command.getName(), JsonUtils.toJson(openIds));
                openIds.forEach(this::logoutByOpenId);
            }
        }
        if (StrUtil.isNotBlank(command.getTelephone())) {
            BatchGetIdUserRespBody body = tenantFeishuAppClient.getContactService().getOpenId(command.getTelephone());
            if (ObjUtil.isNotNull(body) && body.getUserList().length > 0) {
                String openId = Arrays.asList(body.getUserList()).get(0).getUserId();
                this.logoutByOpenId(openId);
            }
        }
        return true;
    }

    private void logoutByOpenId(String openId) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            String key = CacheConstants.getFeishuOpenId(value.getCode(), openId);
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            if (StrUtil.isNotBlank(userInfo)) {
                String token = JsonUtils.parse(userInfo, UserInfoDTO.class).getToken();
                this.logout(token);
            }
        }

    }

    public UserInfoDTO getUserToken(String openId) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            String key = CacheConstants.getFeishuOpenId(value.getCode(), openId);
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            if (StrUtil.isNotBlank(userInfo)) {
                UserInfoDTO userInfoDTO = JsonUtils.parse(userInfo, UserInfoDTO.class);
                String authToken = CacheConstants.getAuthToken(userInfoDTO.getToken());
                String userInfoStr = stringRedisTemplate.opsForValue().get(authToken);
                if (ObjUtil.isNotNull(userInfoStr)) {
                    return userInfoDTO;
                }
            }
        }
        return null;
    }

    public AuthConfigDTO getAuthConfig(String openId) {
        return null;
    }

    //todo:现在都是超管
    public void getResource(UserInfoDTO userInfo) {
        List<SysResourcePO> resourcePOS = sysResourceMapper.selectList(null);
        userInfo.setResourceTree(resourceAssembler.buildTree(resourcePOS));
        userInfo.setPermissions(resourcePOS.stream().map(SysResourcePO::getPath).collect(Collectors.toSet()));


//        List<SysResourcePO> resourcePOS = new ArrayList<>();
//        // 超管有所有权限
//        List<String> adminTelephones = meiyeSeverProperties.getLogin().getAdminTelephones();
//        if (adminTelephones.contains(userInfo.getMobile())) {
//            resourcePOS = myResourceMapper.selectList(null);
//        } else {
//            resourcePOS = myResourceMapper.selectByOpenId(userInfo.getOpenId());
//        }
//        if (CollUtil.isNotEmpty(resourcePOS)) {
//            userInfo.setResourceTree(assembler.buildTree(resourcePOS));
//            userInfo.setPermissions(resourcePOS.stream().map(MyResourcePO::getPath).collect(Collectors.toSet()));
//        }
    }
}

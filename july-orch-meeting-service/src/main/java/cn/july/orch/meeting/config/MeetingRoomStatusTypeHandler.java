package cn.july.orch.meeting.config;

import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 会议室状态枚举TypeHandler
 */
public class MeetingRoomStatusTypeHandler extends BaseTypeHandler<MeetingRoomStatusEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, MeetingRoomStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public MeetingRoomStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getObject(columnName, Integer.class);
        return MeetingRoomStatusEnum.fromCode(code);
    }

    @Override
    public MeetingRoomStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getObject(columnIndex, Integer.class);
        return MeetingRoomStatusEnum.fromCode(code);
    }

    @Override
    public MeetingRoomStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getObject(columnIndex, Integer.class);
        return MeetingRoomStatusEnum.fromCode(code);
    }
}
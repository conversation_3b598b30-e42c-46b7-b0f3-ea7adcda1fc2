package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TaskListPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务清单Mapper
 */
public interface TaskListMapper extends BaseMapper<TaskListPO> {

    /**
     * 根据名称查询任务清单
     *
     * @param name 清单名称
     * @return 任务清单列表
     */
    List<TaskListPO> findByName(@Param("name") String name);

    /**
     * 根据创建人ID查询任务清单
     *
     * @param createUserId 创建人ID
     * @return 任务清单列表
     */
    List<TaskListPO> findByCreateUserId(@Param("createUserId") String createUserId);
}
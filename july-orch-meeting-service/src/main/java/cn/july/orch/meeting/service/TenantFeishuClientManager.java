package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.properties.FeishuProperties;
import cn.july.orch.meeting.domain.dto.TenantConfigDTO;
import cn.july.orch.meeting.exception.MessageCode;
import com.lark.oapi.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 多租户飞书客户端管理器
 * 负责管理多个租户的飞书客户端实例，支持动态加载和缓存
 */
@Slf4j
@Service
public class TenantFeishuClientManager {
    
    // 租户客户端缓存 Map<tenantId, FeishuAppClient>
    private final Map<Long, FeishuAppClient> clientCache = new ConcurrentHashMap<>();
    
    // 租户配置缓存 Map<tenantId, TenantConfigDTO>
    private final Map<Long, TenantConfigDTO> configCache = new ConcurrentHashMap<>();
    
    // 客户端锁，防止并发创建
    private final Map<Long, Object> clientLocks = new ConcurrentHashMap<>();
    
    @Resource
    private TenantConfigService tenantConfigService;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * 获取指定租户的飞书客户端
     */
    public FeishuAppClient getClient(Long tenantId) {
        if (tenantId == null) {
            throw new BusinessException(MessageCode.TENANT_ID_REQUIRED);
        }
        
        // 先从缓存获取
        FeishuAppClient client = clientCache.get(tenantId);
        if (client != null) {
            return client;
        }
        
        // 双重检查锁定
        Object lock = clientLocks.computeIfAbsent(tenantId, k -> new Object());
        synchronized (lock) {
            client = clientCache.get(tenantId);
            if (client != null) {
                return client;
            }
            
            // 创建新的客户端
            client = createClient(tenantId);
            clientCache.put(tenantId, client);
            return client;
        }
    }
    
    /**
     * 创建租户飞书客户端
     */
    private FeishuAppClient createClient(Long tenantId) {
        TenantConfigDTO config = getTenantConfig(tenantId);
        if (config == null) {
            throw new BusinessException(MessageCode.TENANT_NOT_FOUND);
        }
        
        if (config.getStatus().getCode() != 1) {
            throw new BusinessException(MessageCode.TENANT_DISABLED);
        }
        
        try {
            // 创建飞书Client
            Client feishuClient = Client.newBuilder(config.getFeishuAppId(), config.getFeishuAppSecret())
                    .requestTimeout(3, TimeUnit.SECONDS)
                    .build();
            
            // 创建FeishuAppClient
            FeishuProperties properties = new FeishuProperties();
            properties.setAppId(config.getFeishuAppId());
            properties.setAppSecret(config.getFeishuAppSecret());
            properties.setEnabled(true);
            
            return new FeishuAppClient.Builder(properties, feishuClient, stringRedisTemplate)
                    .build();
                    
        } catch (Exception e) {
            log.error("创建租户飞书客户端失败, tenantId: {}", tenantId, e);
            throw new BusinessException(MessageCode.TENANT_CONFIG_ERROR);
        }
    }
    
    /**
     * 获取租户配置
     */
    public TenantConfigDTO getTenantConfig(Long tenantId) {
        // 先从缓存获取
        TenantConfigDTO config = configCache.get(tenantId);
        if (config != null) {
            return config;
        }
        
        // 从数据库加载
        config = tenantConfigService.getByTenantId(tenantId);
        configCache.put(tenantId, config);
        
        return config;
    }
    
    /**
     * 刷新租户客户端
     */
    public void refreshClient(Long tenantId) {
        log.info("刷新租户飞书客户端, tenantId: {}", tenantId);
        
        // 清除缓存
        clientCache.remove(tenantId);
        configCache.remove(tenantId);
        
        // 重新创建
        getClient(tenantId);
    }
    
    /**
     * 移除租户客户端
     */
    public void removeClient(Long tenantId) {
        log.info("移除租户飞书客户端, tenantId: {}", tenantId);
        
        clientCache.remove(tenantId);
        configCache.remove(tenantId);
        clientLocks.remove(tenantId);
    }

    /**
     * 获取所有租户客户端
     */
    public Map<Long, FeishuAppClient> getAllClients() {
        return new HashMap<>(clientCache);
    }
    
    /**
     * 获取所有租户客户端信息（用于缓存刷新任务）
     */
    public Map<Long, Object> getAllClientInfo() {
        Map<Long, Object> result = new HashMap<>();
        for (Map.Entry<Long, FeishuAppClient> entry : clientCache.entrySet()) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }
    
    /**
     * 预热所有启用的租户客户端
     */
    @PostConstruct
    public void warmUpClients() {
        log.info("开始预热租户飞书客户端");
        
        try {
            List<TenantConfigDTO> enabledConfigs = tenantConfigService.getEnabledConfigs();
            for (TenantConfigDTO config : enabledConfigs) {
                try {
                    getClient(config.getTenantId());
                    log.info("预热租户客户端成功, tenantId: {}", config.getTenantId());
                } catch (Exception e) {
                    log.error("预热租户客户端失败, tenantId: {}", config.getTenantId(), e);
                }
            }
        } catch (Exception e) {
            log.error("预热租户客户端失败", e);
        }
        
        log.info("租户飞书客户端预热完成");
    }
}

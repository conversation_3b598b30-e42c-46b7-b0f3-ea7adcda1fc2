package cn.july.orch.meeting.domain.entity;

import cn.july.orch.meeting.domain.dto.FileInfoDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议题领域实体
 * @date 2025-11-06
 */
@Data
@Accessors(chain = true)
public class Topic {

    /**
     * 议题ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 议题名称
     */
    private String name;

    /**
     * 议题描述
     */
    private String description;

    /**
     * 附件对象存储key列表
     */
    private List<String> attachmentKeys;

    /**
     * 附件完整信息列表
     */
    private List<FileInfoDTO> attachments;

    /**
     * 是否启用(0-否,1-是)
     */
    private Integer isEnabled;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

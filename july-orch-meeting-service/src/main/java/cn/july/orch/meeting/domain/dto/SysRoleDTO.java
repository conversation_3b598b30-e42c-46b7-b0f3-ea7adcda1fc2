package cn.july.orch.meeting.domain.dto;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.RoleTypeEnum;
import cn.july.orch.meeting.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 系统角色DTO
 * @date 2025-01-30
 */
@Data
public class SysRoleDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "角色名称")
    private String name;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 类型(system（系统角色），customize（自定义）)
     */
    @ApiModelProperty(value = "角色类型")
    private RoleTypeEnum type;

    /**
     * 状态（1：启用；2：停用）
     */
    @ApiModelProperty(value = "状态")
    private StatusEnum status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 逻辑删除（0：未删除；1：删除）
     */
    @ApiModelProperty(value = "逻辑删除")
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    /**
     * 创建用户名
     */
    @ApiModelProperty(value = "创建用户名")
    private String createUserName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @ApiModelProperty(value = "更新用户ID")
    private String updateUserId;

    /**
     * 更新用户名
     */
    @ApiModelProperty(value = "更新用户名")
    private String updateUserName;
}

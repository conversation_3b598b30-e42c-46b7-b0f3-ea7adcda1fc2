package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.SysRoleResourceRelPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description 角色与资源关联映射器
 * @date 2025-01-24
 */
public interface SysRoleResourceRelMapper extends BaseMapper<SysRoleResourceRelPO> {

    /**
     * 根据角色ID查询资源关联列表
     */
    default List<SysRoleResourceRelPO> findByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId);
        return selectList(wrapper);
    }

    /**
     * 根据资源ID查询角色关联列表
     */
    default List<SysRoleResourceRelPO> findByResourceId(Long resourceId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getResourceId, resourceId);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID和租户ID查询资源关联列表
     */
    default List<SysRoleResourceRelPO> findByRoleIdAndTenantId(Long roleId, Long tenantId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId)
                .eq(SysRoleResourceRelPO::getTenantId, tenantId);
        return selectList(wrapper);
    }

    /**
     * 根据资源ID和租户ID查询角色关联列表
     */
    default List<SysRoleResourceRelPO> findByResourceIdAndTenantId(Long resourceId, Long tenantId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getResourceId, resourceId)
                .eq(SysRoleResourceRelPO::getTenantId, tenantId);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID列表查询资源关联列表
     */
    default List<SysRoleResourceRelPO> findByRoleIds(List<Long> roleIds) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .in(SysRoleResourceRelPO::getRoleId, roleIds);
        return selectList(wrapper);
    }

    /**
     * 根据资源ID列表查询角色关联列表
     */
    default List<SysRoleResourceRelPO> findByResourceIds(List<Long> resourceIds) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .in(SysRoleResourceRelPO::getResourceId, resourceIds);
        return selectList(wrapper);
    }

    /**
     * 根据角色ID和资源ID查询关联关系
     */
    default SysRoleResourceRelPO findByRoleIdAndResourceId(Long roleId, Long resourceId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId)
                .eq(SysRoleResourceRelPO::getResourceId, resourceId);
        return selectOne(wrapper);
    }

    /**
     * 根据角色ID删除所有关联关系
     */
    default void deleteByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId);
        delete(wrapper);
    }

    /**
     * 根据资源ID删除所有关联关系
     */
    default void deleteByResourceId(Long resourceId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getResourceId, resourceId);
        delete(wrapper);
    }

    /**
     * 根据角色ID和租户ID删除所有关联关系
     */
    default void deleteByRoleIdAndTenantId(Long roleId, Long tenantId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId)
                .eq(SysRoleResourceRelPO::getTenantId, tenantId);
        delete(wrapper);
    }

    /**
     * 根据角色ID和资源ID删除关联关系
     */
    default void deleteByRoleIdAndResourceId(Long roleId, Long resourceId) {
        LambdaQueryWrapper<SysRoleResourceRelPO> wrapper = Wrappers.lambdaQuery(SysRoleResourceRelPO.class)
                .eq(SysRoleResourceRelPO::getRoleId, roleId)
                .eq(SysRoleResourceRelPO::getResourceId, resourceId);
        delete(wrapper);
    }
}

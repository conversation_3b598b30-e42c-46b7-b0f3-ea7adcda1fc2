package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.TaskListInfo;
import cn.july.orch.meeting.domain.po.TaskListPO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务清单数据转换器
 */
@Component
public class TaskListAssembler {

    /**
     * TaskListPO转TaskListInfo
     *
     * @param taskListPO 任务清单PO
     * @return TaskListInfo
     */
    public TaskListInfo toTaskListInfo(TaskListPO taskListPO) {
        if (taskListPO == null) {
            return null;
        }

        TaskListInfo taskListInfo = new TaskListInfo();
        BeanUtils.copyProperties(taskListPO, taskListInfo);
        return taskListInfo;
    }

    /**
     * TaskListInfo转TaskListPO
     *
     * @param taskListInfo 任务清单Info
     * @return TaskListPO
     */
    public TaskListPO toTaskListPO(TaskListInfo taskListInfo) {
        if (taskListInfo == null) {
            return null;
        }

        TaskListPO taskListPO = new TaskListPO();
        BeanUtils.copyProperties(taskListInfo, taskListPO);
        return taskListPO;
    }

    /**
     * TaskListPO列表转TaskListInfo列表
     *
     * @param taskListPOList 任务清单PO列表
     * @return TaskListInfo列表
     */
    public List<TaskListInfo> toTaskListInfoList(List<TaskListPO> taskListPOList) {
        if (taskListPOList == null || taskListPOList.isEmpty()) {
            return null;
        }

        return taskListPOList.stream()
                .map(this::toTaskListInfo)
                .collect(Collectors.toList());
    }

    /**
     * TaskListInfo列表转TaskListPO列表
     *
     * @param taskListInfoList 任务清单Info列表
     * @return TaskListPO列表
     */
    public List<TaskListPO> toTaskListPOList(List<TaskListInfo> taskListInfoList) {
        if (taskListInfoList == null || taskListInfoList.isEmpty()) {
            return null;
        }

        return taskListInfoList.stream()
                .map(this::toTaskListPO)
                .collect(Collectors.toList());
    }
}
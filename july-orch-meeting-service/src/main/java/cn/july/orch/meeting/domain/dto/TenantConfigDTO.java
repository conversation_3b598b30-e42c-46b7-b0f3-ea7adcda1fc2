package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.TenantStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 租户配置DTO
 */
@Data
public class TenantConfigDTO {

    /**
     * 租户ID（主键）
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 飞书应用ID
     */
    private String feishuAppId;

    /**
     * 飞书应用密钥（不返回给前端）
     */
    private String feishuAppSecret;

    /**
     * 租户状态
     */
    private TenantStatusEnum status;

    /**
     * 租户描述
     */
    private String description;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人姓名
     */
    private String updateUserName;
}

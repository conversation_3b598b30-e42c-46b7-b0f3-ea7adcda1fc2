package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.GenerateAnalysisReportCommand;
import cn.july.orch.meeting.domain.command.MeetingAnalysisReportCommand;
import cn.july.orch.meeting.domain.dto.MeetingAnalysisReportDTO;
import cn.july.orch.meeting.domain.query.MeetingAnalysisReportQuery;
import cn.july.orch.meeting.service.MeetingAnalysisReportActionService;
import cn.july.orch.meeting.service.MeetingAnalysisReportQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告控制器
 */
@Api(tags = "会议分析报告")
@Slf4j
@RestController
@RequestMapping("/analysis-report")
public class MeetingAnalysisReportController {

    @Resource
    private MeetingAnalysisReportActionService meetingAnalysisReportActionService;

    @Resource
    private MeetingAnalysisReportQueryService meetingAnalysisReportQueryService;

    @PostMapping
    @ApiOperation("创建会议分析报告")
    public Long createReport(@Validated @RequestBody MeetingAnalysisReportCommand command) {
        log.info("收到创建会议分析报告请求，会议ID：{}", command.getMeetingId());
        return meetingAnalysisReportActionService.createReport(command);
    }

    @PostMapping("/{id}")
    @ApiOperation("更新会议分析报告")
    public void updateReport(@PathVariable Long id, @Validated @RequestBody MeetingAnalysisReportCommand command) {
        log.info("收到更新会议分析报告请求，报告ID：{}", id);
        meetingAnalysisReportActionService.updateReport(id, command);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除会议分析报告")
    public void deleteReport(@PathVariable Long id) {
        log.info("收到删除会议分析报告请求，报告ID：{}", id);
        meetingAnalysisReportActionService.deleteReport(id);
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询会议分析报告")
    public MeetingAnalysisReportDTO queryById(@PathVariable Long id) {
        log.info("收到查询会议分析报告请求，报告ID：{}", id);
        return meetingAnalysisReportQueryService.queryById(id);
    }

    @GetMapping("/meeting/{meetingId}")
    @ApiOperation("根据会议ID查询会议分析报告")
    public MeetingAnalysisReportDTO queryByMeetingId(@PathVariable Long meetingId) {
        log.info("收到查询会议分析报告请求，会议ID：{}", meetingId);
        return meetingAnalysisReportQueryService.queryByMeetingId(meetingId);
    }

    @GetMapping("/query")
    @ApiOperation("查询会议分析报告")
    public MeetingAnalysisReportDTO query(MeetingAnalysisReportQuery query) {
        log.info("收到查询会议分析报告请求，查询条件：{}", query);
        return meetingAnalysisReportQueryService.query(query);
    }

    @PostMapping("/generate")
    @ApiOperation("生成会议分析报告")
    public ResponseEntity<Long> generateReport(@Validated @RequestBody GenerateAnalysisReportCommand command) {
        log.info("收到生成会议分析报告请求，会议ID：{}", command.getMeetingId());

        // 先创建或更新报告记录
        Long reportId = meetingAnalysisReportActionService.initReport(command.getMeetingId());

        // 异步执行生成报告任务
        meetingAnalysisReportActionService.generateReport(command);

        return ResponseEntity.ok(reportId);
    }

    @PostMapping("/generate-ai-transcript/{meetingId}")
    @ApiOperation("生成AI智能纪要")
    public ResponseEntity<Long> generateAiTranscript(@PathVariable Long meetingId) {
        log.info("收到生成AI智能纪要请求，会议ID：{}", meetingId);

        // 先创建或更新报告记录
        Long reportId = meetingAnalysisReportActionService.initReport(meetingId);

        // 异步执行生成AI纪要任务
        meetingAnalysisReportActionService.generateAiTranscript(meetingId);

        return ResponseEntity.ok(reportId);
    }

    @GetMapping("/ai-transcript/{meetingId}")
    @ApiOperation("查看AI智能纪要")
    public String getAiTranscript(@PathVariable Long meetingId) {
        log.info("收到查看AI智能纪要请求，会议ID：{}", meetingId);
        return meetingAnalysisReportQueryService.getAiTranscriptByMeetingId(meetingId);
    }

    @PostMapping("/ai-transcript/{meetingId}")
    @ApiOperation("更新AI分析报告")
    public void updateAiTranscript(@PathVariable Long meetingId, @RequestBody String aiTranscriptMd) {
        log.info("收到更新AI分析报告请求，会议ID：{}", meetingId);
        meetingAnalysisReportActionService.updateAiTranscript(meetingId, aiTranscriptMd);
    }
}

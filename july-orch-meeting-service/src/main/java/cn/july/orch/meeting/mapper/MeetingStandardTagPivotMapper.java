package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingStandardTagPivotPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准标签关联Mapper
 * @date 2025-08-26
 */
@Mapper
public interface MeetingStandardTagPivotMapper extends BaseMapper<MeetingStandardTagPivotPO> {

    /**
     * 根据会议标准ID查询关联的标签ID列表
     */
    List<Long> selectTagIdsByStandardId(@Param("standardId") Long standardId);

    /**
     * 根据会议标准ID删除关联关系
     */
    int deleteByStandardId(@Param("standardId") Long standardId);

    /**
     * 批量插入关联关系
     */
    int batchInsert(@Param("list") List<MeetingStandardTagPivotPO> list);
}
package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 取消重复系列命令
 * @date 2025-01-24
 */
@Data
public class CancelRecurrenceCommand {

    @ApiModelProperty(value = "会议ID", required = true)
    @NotNull(message = "会议ID不能为空")
    private Long meetingId;

    @ApiModelProperty(value = "取消原因")
    private String reason;
}


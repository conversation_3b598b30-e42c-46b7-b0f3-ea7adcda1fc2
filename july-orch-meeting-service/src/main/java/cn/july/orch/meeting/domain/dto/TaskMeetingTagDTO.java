package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> Assistant
 * @description 任务关联的会议标签DTO（轻量级，仅包含核心字段）
 * @date 2025-08-29
 */
@Data
@Builder
@ApiModel("任务会议标签信息")
public class TaskMeetingTagDTO {

    @ApiModelProperty("标签ID")
    private Long id;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("标签颜色(Hex格式, e.g., #3498DB)")
    private String color;
}
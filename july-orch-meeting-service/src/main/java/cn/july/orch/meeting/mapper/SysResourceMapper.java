package cn.july.orch.meeting.mapper;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.domain.po.SysResourcePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 系统资源映射器
 * @date 2025-01-24
 */
public interface SysResourceMapper extends BaseMapper<SysResourcePO> {

    /**
     * 根据资源类型查询资源列表
     */
    default List<SysResourcePO> findByType(Integer type) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getType, type)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 根据父级ID查询子资源列表
     */
    default List<SysResourcePO> findByPid(Long pid) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getPid, pid)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 根据状态查询资源列表
     */
    default List<SysResourcePO> findByStatus(Integer status) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getStatus, status)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 根据路径查询资源
     */
    default SysResourcePO findByPath(String path) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getPath, path)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectOne(wrapper);
    }

    /**
     * 根据权限标识查询资源
     */
    default SysResourcePO findByAuths(String auths) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getAuths, auths)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectOne(wrapper);
    }

    /**
     * 查询所有启用的菜单资源
     */
    default List<SysResourcePO> findEnabledMenus() {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getType, 1)
                .eq(SysResourcePO::getStatus, 1)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 查询所有启用的按钮资源
     */
    default List<SysResourcePO> findEnabledButtons() {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class)
                .eq(SysResourcePO::getType, 2)
                .eq(SysResourcePO::getStatus, 1)
                .eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 根据标题模糊查询资源
     */
    default List<SysResourcePO> findByTitleLike(String title) {
        LambdaQueryWrapper<SysResourcePO> wrapper = Wrappers.lambdaQuery(SysResourcePO.class);
        if (StringUtils.hasText(title)) {
            wrapper.like(SysResourcePO::getTitle, title);
        }
        wrapper.eq(SysResourcePO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(SysResourcePO::getResourceSort);
        return selectList(wrapper);
    }

    /**
     * 更新资源排序
     */
    default void updateResourceSort(Long id, Integer resourceSort) {
        SysResourcePO po = new SysResourcePO();
        po.setId(id);
        po.setResourceSort(resourceSort);
        updateById(po);
    }
}
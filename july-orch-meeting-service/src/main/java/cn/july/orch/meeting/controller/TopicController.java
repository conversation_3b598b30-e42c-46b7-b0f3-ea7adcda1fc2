package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.TopicCreateCommand;
import cn.july.orch.meeting.domain.command.TopicUpdateCommand;
import cn.july.orch.meeting.domain.dto.TopicDTO;
import cn.july.orch.meeting.domain.query.TopicQuery;
import cn.july.orch.meeting.service.TopicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议题控制器
 * @date 2025-11-06
 */
@Api(tags = "议题管理")
@RestController
@RequestMapping("/topic")
@RequiredArgsConstructor
public class TopicController {

    private final TopicService topicService;

    @PostMapping("/list")
    @ApiOperation("查询启用的议题列表")
    public List<TopicDTO> list() {
        return topicService.listEnabled();
    }

    @GetMapping("/detail")
    @ApiOperation("查询议题详情")
    public TopicDTO detail(@RequestParam("id") Long id) {
        return topicService.getById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询议题")
    public PageResultDTO<TopicDTO> pageQuery(@RequestBody TopicQuery query) {
        return topicService.pageQuery(query);
    }

    @PostMapping("/create")
    @ApiOperation("创建议题")
    public void create(@Valid @RequestBody TopicCreateCommand command) {
        topicService.createTopic(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新议题")
    public void update(@Valid @RequestBody TopicUpdateCommand command) {
        topicService.updateTopic(command);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除议题")
    public void delete(@PathVariable("id") Long id) {
        topicService.deleteTopic(id);
    }

    @PostMapping("/enable/{id}")
    @ApiOperation("启用议题")
    public void enable(@PathVariable("id") Long id) {
        topicService.enableTopic(id);
    }

    @PostMapping("/disable/{id}")
    @ApiOperation("停用议题")
    public void disable(@PathVariable("id") Long id) {
        topicService.disableTopic(id);
    }
}

package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import lombok.Data;

/**
 * 会议室查询参数类
 */
@Data
public class MeetingRoomQuery {

    /**
     * 会议室名称（模糊查询）
     */
    private String name;

    /**
     * 状态 (0-空闲中, 1-使用中, 2-已预定)
     */
    private MeetingRoomStatusEnum status;


    /**
     * 当前页码，默认第1页
     */
    private Integer pageNum = 1;

    /**
     * 每页大小，默认10条
     */
    private Integer pageSize = 10;
}
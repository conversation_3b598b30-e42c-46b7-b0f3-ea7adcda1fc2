package cn.july.orch.meeting.handler.task;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.enums.TaskNotificationTypeEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 任务通知服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskNotificationService {

    private final TaskNotificationHandlerFactory handlerFactory;

    @IgnoreTenant
    public void processNotification(P2TaskUpdatedV1Data notification) {
        if (Objects.isNull(notification)) {
            log.error("任务通知内容为空");
            return;
        }

        log.info("处理任务通知，飞书任务ID：{}，通知类型：{}",
                notification.getTaskId(), notification.getObjType());

        try {
            TaskNotificationHandler handler = handlerFactory.getHandler(
                    TaskNotificationTypeEnum.of(notification.getObjType()));
            handler.handle(notification);
        } catch (Exception e) {
            log.error("处理任务通知失败，飞书任务ID：{}，通知类型：{}",
                    notification.getTaskId(), notification.getObjType(), e);
        }
    }
}

package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @description 会议标签更新Command
 * @date 2025-08-26
 */
@Data
@ApiModel("会议标签更新请求")
public class MeetingTagUpdateCommand {

    @ApiModelProperty(value = "标签ID", required = true)
    @NotNull(message = "标签ID不能为空")
    private Long id;

    @ApiModelProperty(value = "标签名称", required = true)
    @NotBlank(message = "标签名称不能为空")
    private String name;

    @ApiModelProperty(value = "标签颜色(Hex格式, e.g., #3498DB)", required = true)
    @NotBlank(message = "标签颜色不能为空")
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "标签颜色格式不正确，应为Hex格式如#3498DB")
    private String color;

    @ApiModelProperty("标签描述")
    private String description;
}
package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 飞书卡片服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeishuCardService {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 构建会议创建人签到码卡片
     * @param meeting 会议信息
     * @param checkinCode 签到码
     * @return 卡片JSON字符串
     */
    public String buildCheckInCodeCard(NewMeeting meeting, String checkinCode) {
        // 直接构建JSON字符串，确保字段顺序正确
        String cardJson = String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true,\n" +
            "    \"enable_forward\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"blue\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"请查收会议签到码\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**会议标签：** %s\\n**会议名称：** %s\\n**会议时间：** %s\\n**会议地点：** %s\\n\\n**签到码：** <font color='blue' size='large'>%s</font>\\n\\n请将以上签到码分享给参会人员，用于会议签到。\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"button\",\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"进入会议\"\n" +
            "          },\n" +
            "          \"type\": \"primary\",\n" +
            "          \"url\": \"%s\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}",
            getMeetingTagsDisplay(meeting.getMeetingTags()),
            meeting.getMeetingName(),
            meeting.getStartTime().format(TIME_FORMATTER),
            meeting.getMeetingLocation() != null ? meeting.getMeetingLocation() : "待定",
            checkinCode,
            meeting.getMeetingUrl() != null ? meeting.getMeetingUrl() : ""
        );
        
        return cardJson;
    }

    /**
     * 构建参会人员签到提醒卡片
     * @param meeting 会议信息
     * @return 卡片JSON字符串
     */
    public String buildCheckInReminderCard(NewMeeting meeting) {
        // 按照调查问卷的精确结构，只保留基础输入框和按钮
        String cardJson = String.format(
            "{\n" +
            "  \"schema\": \"2.0\",\n" +
            "  \"config\": {\n" +
            "    \"update_multi\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"会议签到提醒\"\n" +
            "    },\n" +
            "    \"template\": \"green\"\n" +
            "  },\n" +
            "  \"body\": {\n" +
            "    \"direction\": \"vertical\",\n" +
            "    \"elements\": [\n" +
            "      {\n" +
            "        \"tag\": \"markdown\",\n" +
            "        \"content\": \"会议标签：%s\\n会议名称：%s\\n会议时间：%s\\n会议地点：%s\\n\\n会议即将开始，请输入签到码完成签到。\"\n" +
            "      },\n" +
            "      {\n" +
            "        \"tag\": \"form\",\n" +
            "        \"name\": \"checkin_form\",\n" +
            "        \"elements\": [\n" +
            "          {\n" +
            "            \"tag\": \"column_set\",\n" +
            "            \"columns\": [\n" +
            "              {\n" +
            "                \"tag\": \"column\",\n" +
            "                \"width\": \"weighted\",\n" +
            "                \"weight\": 1,\n" +
            "                \"elements\": [\n" +
            "                  {\n" +
            "                    \"tag\": \"markdown\",\n" +
            "                    \"content\": \"签到码\"\n" +
            "                  },\n" +
            "                  {\n" +
            "                    \"tag\": \"input\",\n" +
            "                    \"name\": \"checkin_code\",\n" +
            "                    \"placeholder\": {\n" +
            "                      \"tag\": \"plain_text\",\n" +
            "                      \"content\": \"请输入6位签到码\"\n" +
            "                    },\n" +
            "                    \"max_length\": 6\n" +
            "                  }\n" +
            "                ]\n" +
            "              }\n" +
            "            ]\n" +
            "          },\n" +
            "          {\n" +
            "            \"tag\": \"column_set\",\n" +
            "            \"horizontal_spacing\": \"8px\",\n" +
            "            \"columns\": [\n" +
            "              {\n" +
            "                \"tag\": \"column\",\n" +
            "                \"width\": \"weighted\",\n" +
            "                \"weight\": 1,\n" +
            "                \"elements\": [\n" +
            "                  {\n" +
            "                    \"tag\": \"button\",\n" +
            "                    \"text\": {\n" +
            "                      \"tag\": \"plain_text\",\n" +
            "                      \"content\": \"确认签到\"\n" +
            "                    },\n" +
            "                    \"type\": \"primary_filled\",\n" +
            "                    \"behaviors\": [\n" +
            "                      {\n" +
            "                        \"type\": \"callback\",\n" +
            "                        \"value\": \"{\\\"action\\\":\\\"submit_checkin\\\",\\\"meeting_id\\\":\\\"%d\\\"}\"\n" +
            "                      }\n" +
            "                    ],\n" +
            "                    \"form_action_type\": \"submit\",\n" +
            "                    \"name\": \"submit_checkin_button\"\n" +
            "                  }\n" +
            "                ]\n" +
            "              },\n" +
            "              {\n" +
            "                \"tag\": \"column\",\n" +
            "                \"width\": \"weighted\",\n" +
            "                \"weight\": 1,\n" +
            "                \"elements\": [\n" +
            "                  {\n" +
            "                    \"tag\": \"button\",\n" +
            "                    \"text\": {\n" +
            "                      \"tag\": \"plain_text\",\n" +
            "                      \"content\": \"进入会议\"\n" +
            "                    },\n" +
            "                    \"type\": \"primary\",\n" +
            "                    \"url\": \"%s\"\n" +
            "                  }\n" +
            "                ]\n" +
            "              }\n" +
            "            ]\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}",
            getMeetingTagsDisplay(meeting.getMeetingTags()),
            meeting.getMeetingName(),
            meeting.getStartTime().format(TIME_FORMATTER),
            meeting.getMeetingLocation() != null ? meeting.getMeetingLocation() : "待定",
            meeting.getId(),
            meeting.getMeetingUrl() != null ? meeting.getMeetingUrl() : ""
        );
        
        return cardJson;
    }

    /**
     * 获取会议标签显示文本
     * @param meetingTags 会议标签列表
     * @return 标签显示文本
     */
    private String getMeetingTagsDisplay(List<SimpleMeetingTagDTO> meetingTags) {
        if (CollectionUtils.isEmpty(meetingTags)) {
            return "无";
        }
        
        return meetingTags.stream()
                .map(SimpleMeetingTagDTO::getName)
                .collect(Collectors.joining("、"));
    }

    /**
     * 构建签到反馈卡片
     * @param meeting 会议信息
     * @param success 是否成功
     * @param message 反馈消息
     * @return 反馈卡片JSON
     */
    public String buildCheckInFeedbackCard(NewMeeting meeting, boolean success, String message) {
        String statusIcon = success ? "✅" : "❌";
        String statusText = success ? "签到成功" : "签到失败";
        String templateColor = success ? "green" : "red";
        
        String cardJson = String.format(
            "{\n" +
            "  \"schema\": \"2.0\",\n" +
            "  \"config\": {\n" +
            "    \"update_multi\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"%s %s\"\n" +
            "    },\n" +
            "    \"template\": \"%s\"\n" +
            "  },\n" +
            "  \"body\": {\n" +
            "    \"direction\": \"vertical\",\n" +
            "    \"elements\": [\n" +
            "      {\n" +
            "        \"tag\": \"markdown\",\n" +
            "        \"content\": \"会议标签：%s\\n会议名称：%s\\n会议时间：%s\\n会议地点：%s\\n\\n%s\"\n" +
            "      },\n" +
            "      {\n" +
            "        \"tag\": \"div\",\n" +
            "        \"text\": {\n" +
            "          \"tag\": \"lark_md\",\n" +
            "          \"content\": \"**%s**\"\n" +
            "        }\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}",
            statusIcon,
            statusText,
            templateColor,
            getMeetingTagsDisplay(meeting.getMeetingTags()),
            meeting.getMeetingName(),
            meeting.getStartTime().format(TIME_FORMATTER),
            meeting.getMeetingLocation() != null ? meeting.getMeetingLocation() : "待定",
            success ? "会议签到已完成，感谢您的参与！" : "签到失败，请检查签到码是否正确。",
            message
        );
        
        return cardJson;
    }
}

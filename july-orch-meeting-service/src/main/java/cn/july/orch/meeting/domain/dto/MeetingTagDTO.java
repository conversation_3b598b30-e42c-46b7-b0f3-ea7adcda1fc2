package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议标签DTO
 * @date 2025-08-26
 */
@Data
@ApiModel("会议标签信息")
public class MeetingTagDTO {

    @ApiModelProperty("标签ID")
    private Long id;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("标签颜色(Hex格式, e.g., #3498DB)")
    private String color;

    @ApiModelProperty("标签描述")
    private String description;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人ID")
    private String updateUserId;

    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}
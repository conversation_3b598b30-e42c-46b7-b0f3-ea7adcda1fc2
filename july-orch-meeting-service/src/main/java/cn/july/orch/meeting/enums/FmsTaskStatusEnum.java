package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Assistant
 * @description FMS任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum FmsTaskStatusEnum {
    PENDING(0, "未开始"),
    RUNNING(1, "执行中"),
    SUCCESS(2, "成功"),
    FAILED(3, "失败");
    
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;
}

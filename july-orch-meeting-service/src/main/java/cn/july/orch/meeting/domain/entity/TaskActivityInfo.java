package cn.july.orch.meeting.domain.entity;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 任务动态信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskActivityInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联的任务ID (逻辑关联 tasks.id)
     */
    private Long taskId;

    /**
     * 动态类型
     */
    private TaskActivityTypeEnum activityType;

    /**
     * 动态描述 (一句话简单描述)
     */
    private String activityDescription;

    /**
     * 动态内容的结构化描述 (JSON格式)
     */
    private Map<String, Object> contentJson;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间 (即动态发生时间)
     */
    private LocalDateTime createTime;

    /**
     * 操作用户ID
     */
    private String createUserId;

    /**
     * 操作用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private String updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 创建任务动态
     */
    public static TaskActivityInfo create(Long taskId, TaskActivityTypeEnum activityType, String activityDescription, Map<String, Object> contentJson) {
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        return TaskActivityInfo.builder()
                .taskId(taskId)
                .activityType(activityType)
                .activityDescription(activityDescription)
                .contentJson(contentJson)
                .deleted(DeletedEnum.NOT_DELETED)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .createUserId(currentUserOpenId)
                .createUserName(currentUserName)
                .updateUserId(currentUserOpenId)
                .updateUserName(currentUserName)
                .build();
    }

    /**
     * 安全获取当前用户OpenID
     */
    private static String getCurrentUserOpenIdSafely() {
        try {
            return CurrentUserHolder.getOpenId();
        } catch (Exception e) {
            return "system";
        }
    }

    /**
     * 安全获取当前用户名称
     */
    private static String getCurrentUserNameSafely() {
        try {
            return CurrentUserHolder.getCurrentUser().getName();
        } catch (Exception e) {
            return "系统";
        }
    }
}
package cn.july.orch.meeting.domain.po;

import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会议室PO类
 */
@Data
@TableName("meeting_room")
public class MeetingRoomPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 飞书会议室ID (room_id)
     */
    private String fsRoomId;

    /**
     * 会议室名称
     */
    private String name;

    /**
     * 会议室能容纳的人数
     */
    private Integer capacity;

    /**
     * 会议室的相关描述
     */
    private String description;

    /**
     * 设施信息列表 (同步自飞书的device结构)
     */
    private String devices;

    /**
     * 状态 (0-空闲中, 1-使用中, 2-已预定)
     */
    private MeetingRoomStatusEnum status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记 (0-未删除, 1-已删除)
     */
    private Integer deleted;
}
package cn.july.orch.meeting.utils;

import cn.hutool.core.thread.NamedThreadFactory;
import cn.july.core.utils.thread.ContextAwareThreadPoolExecutor;
import cn.july.core.utils.thread.decorator.JulyRequestContextDecorator;
import cn.july.core.utils.thread.decorator.MDCContextAwareDecorator;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池,将不同任务归类
 * <AUTHOR>
 */
public class AsyncTaskExecutor {

    public final static ExecutorService checkInExecutor;
    public final static ExecutorService fmsTaskExecutor;

    static {
        checkInExecutor = new ContextAwareThreadPoolExecutor(
            5,
            5,
            60 * 1000L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(Integer.MAX_VALUE),
            new NamedThreadFactory("check-in-task", false),
            new JulyRequestContextDecorator(new MDCContextAwareDecorator()));
            
        // FMS任务执行器
        fmsTaskExecutor = new ContextAwareThreadPoolExecutor(
            3,
            10,
            60 * 1000L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100),
            new NamedThreadFactory("fms-task", false),
            new JulyRequestContextDecorator(new MDCContextAwareDecorator()));
    }


}

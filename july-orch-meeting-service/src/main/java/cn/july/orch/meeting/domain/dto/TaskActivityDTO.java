package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 任务动态DTO
 */
@Data
@ApiModel("任务动态信息")
public class TaskActivityDTO {

    @ApiModelProperty("动态ID")
    private Long id;

    @ApiModelProperty("关联的任务ID")
    private Long taskId;

    @ApiModelProperty("动态类型")
    private TaskActivityTypeEnum activityType;

    @ApiModelProperty("动态类型描述")
    private String activityTypeDesc;

    @ApiModelProperty("动态描述")
    private String activityDescription;

    @ApiModelProperty("动态内容")
    private Map<String, Object> contentJson;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("操作用户ID")
    private String createUserId;

    @ApiModelProperty("操作用户名")
    private String createUserName;
}
package cn.july.orch.meeting.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Assistant
 * @description 飞书任务集成配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "july.feishu.task")
public class FeishuTaskProperties {

    /**
     * 飞书任务同步开关
     */
    private Boolean syncEnabled = false;

    /**
     * 任务卡片提醒开关
     */
    private Boolean notificationEnabled = true;

    /**
     * 系统跳转链接配置
     */
    private String systemUrl = "https://your-domain.com/task";

    /**
     * 任务详情页面路径模板（{taskId}会被替换为实际任务ID）
     */
    private String detailPath = "/detail/{taskId}";

    /**
     * 生成任务详情链接
     *
     * @param taskId 任务ID
     * @return 任务详情链接
     */
    public String buildTaskDetailUrl(Long taskId) {
        if (taskId == null) {
            return systemUrl;
        }
        return systemUrl + detailPath.replace("{taskId}", taskId.toString());
    }

    /**
     * 是否启用飞书任务同步
     */
    public boolean isSyncEnabled() {
        return Boolean.TRUE.equals(syncEnabled);
    }

    /**
     * 是否启用任务卡片提醒
     */
    public boolean isNotificationEnabled() {
        return Boolean.TRUE.equals(notificationEnabled);
    }
}
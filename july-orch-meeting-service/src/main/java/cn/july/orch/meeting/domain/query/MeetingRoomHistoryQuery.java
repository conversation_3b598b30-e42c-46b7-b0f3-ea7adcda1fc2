package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会议室历史会议查询参数
 */
@Data
@ApiModel(description = "会议室历史会议查询参数")
public class MeetingRoomHistoryQuery {

    @ApiModelProperty(value = "会议室ID")
    private Long meetingRoomId;

    @ApiModelProperty(value = "开始日期", example = "2025-08-01")
    private String startDate;

    @ApiModelProperty(value = "结束日期", example = "2025-08-31")
    private String endDate;

    private Integer pageNum = 1;

    private Integer pageSize = 10;
}
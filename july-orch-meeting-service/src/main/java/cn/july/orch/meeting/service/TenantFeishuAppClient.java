package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.service.*;
import cn.july.orch.meeting.config.TenantContext;
import cn.july.orch.meeting.exception.MessageCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 多租户飞书客户端包装器
 * 自动根据当前租户上下文获取对应的飞书客户端
 */
@Slf4j
@Service
public class TenantFeishuAppClient {
    
    @Resource
    private TenantFeishuClientManager tenantFeishuClientManager;
    
    /**
     * 获取当前租户的认证服务
     */
    public AuthService getAuthService() {
        return getCurrentClient().getAuthService();
    }
    
    /**
     * 获取当前租户的联系人服务
     */
    public ContactService getContactService() {
        return getCurrentClient().getContactService();
    }
    
    /**
     * 获取当前租户的日历服务
     */
    public CalendarService getCalendarService() {
        return getCurrentClient().getCalendarService();
    }
    
    /**
     * 获取当前租户的机器人服务
     */
    public RobotService getRobotService() {
        return getCurrentClient().getRobotService();
    }
    
    /**
     * 获取当前租户的租户服务
     */
    public TenantService getTenantService() {
        return getCurrentClient().getTenantService();
    }
    
    /**
     * 获取当前租户的审批服务
     */
    public ApprovalService getApprovalService() {
        return getCurrentClient().getApprovalService();
    }
    
    /**
     * 获取当前租户的日历事件服务
     */
    public CalendarEventService getCalendarEventService() {
        return getCurrentClient().getCalendarEventService();
    }
    
    /**
     * 获取当前租户的会议服务
     */
    public MeetService getMeetService() {
        return getCurrentClient().getMeetService();
    }
    
    /**
     * 获取当前租户的会议室服务
     */
    public RoomService getRoomService() {
        return getCurrentClient().getRoomService();
    }
    
    /**
     * 获取当前租户的云盘服务
     */
    public DriveService getDriveService() {
        return getCurrentClient().getDriveService();
    }

    public TaskService getTaskService() {
        return getCurrentClient().getTaskService();
    }
    
    /**
     * 获取当前租户的飞书客户端
     */
    private FeishuAppClient getCurrentClient() {
        Long tenantId = TenantContext.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(MessageCode.TENANT_ID_REQUIRED);
        }
        
        return tenantFeishuClientManager.getClient(tenantId);
    }
}

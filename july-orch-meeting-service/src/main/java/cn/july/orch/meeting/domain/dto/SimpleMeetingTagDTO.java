package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Assistant
 * @description 简化会议标签DTO - 轻量级会议标签信息，只包含核心字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("简化会议标签信息")
public class SimpleMeetingTagDTO {

    @ApiModelProperty("标签ID")
    private Long id;

    @ApiModelProperty("标签名称")
    private String name;

    @ApiModelProperty("标签颜色(Hex格式, e.g., #3498DB)")
    private String color;
}
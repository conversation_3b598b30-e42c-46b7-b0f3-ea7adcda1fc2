package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.domain.po.SysResourcePO;
import cn.july.orch.meeting.enums.ResourceTypeEnum;
import cn.july.orch.meeting.enums.StatusBooleanEnum;
import cn.july.orch.meeting.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysResourceTreeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("资源类型（1: 菜单，2：按钮）")
    private ResourceTypeEnum type;

    @ApiModelProperty("菜单、面包屑、多标签页显示的名称")
    private String title;

    @ApiModelProperty("排序")
    private Integer resourceSort;

    @ApiModelProperty("上级菜单")
    private Long pid;

    @ApiModelProperty("前端path")
    private String path;

    @ApiModelProperty("状态（1：启用；2：停用）")
    private StatusEnum status;

    @ApiModelProperty("按钮权限标识")
    private String auths;

    @ApiModelProperty("是否在菜单中显示（默认值：true）")
    private StatusBooleanEnum showLink;

    @ApiModelProperty("是否显示父级菜单（默认值：true）")
    private StatusBooleanEnum showParent;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "子菜单")
    private List<SysResourceTreeDTO> children;


    public SysResourceTreeDTO(SysResourcePO po) {
        this.id = po.getId();
        this.type = po.getType();
        this.title = po.getTitle();
        this.resourceSort = po.getResourceSort();
        this.pid = po.getPid();
        this.path = po.getPath();
        this.status = po.getStatus();
        this.auths = po.getAuths();
        this.showLink = po.getShowLink();
        this.showParent = po.getShowParent();
        this.remark = po.getRemark();
    }
}

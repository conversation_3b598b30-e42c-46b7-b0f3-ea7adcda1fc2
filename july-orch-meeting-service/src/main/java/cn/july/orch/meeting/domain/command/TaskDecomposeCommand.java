package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务拆解命令
 */
@Data
@ApiModel("任务拆解命令")
public class TaskDecomposeCommand {

    @ApiModelProperty(value = "父任务ID", required = true)
    @NotNull(message = "父任务ID不能为空")
    private Long parentTaskId;

    @ApiModelProperty(value = "子任务列表", required = true)
    @NotEmpty(message = "子任务列表不能为空")
    private List<SubTaskInfo> subTasks;

    /**
     * 子任务信息
     */
    @Data
    @ApiModel("子任务信息")
    public static class SubTaskInfo {

        @ApiModelProperty(value = "任务标题", required = true)
        @NotBlank(message = "任务标题不能为空")
        @Size(max = 200, message = "任务标题长度不能超过200个字符")
        private String title;

        @ApiModelProperty(value = "任务描述")
        @Size(max = 1000, message = "任务描述长度不能超过1000个字符")
        private String description;

        @ApiModelProperty(value = "负责人OpenID", required = true)
        @NotBlank(message = "负责人OpenID不能为空")
        private String ownerOpenId;

        @ApiModelProperty(value = "负责人名称", required = true)
        @NotBlank(message = "负责人名称不能为空")
        private String ownerName;

        @ApiModelProperty(value = "优先级")
        private TaskPriorityEnum priority;

        @ApiModelProperty(value = "截止时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime dueDate;

        @ApiModelProperty(value = "附件key列表")
        private List<String> attachmentKeys;
    }
}
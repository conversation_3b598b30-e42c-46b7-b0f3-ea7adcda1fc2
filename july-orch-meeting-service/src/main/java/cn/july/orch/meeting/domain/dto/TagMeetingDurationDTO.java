package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 标签会议时长统计DTO
 * @date 2025-01-24
 */
@Data
public class TagMeetingDurationDTO {

    @ApiModelProperty("标签ID")
    private Long tagId;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("标签颜色")
    private String tagColor;

    @ApiModelProperty("会议总时长（小时）")
    private Double totalDuration;
}

package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingTagPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 会议标签Mapper
 * @date 2025-08-26
 */
@Mapper
public interface MeetingTagMapper extends BaseMapper<MeetingTagPO> {

    /**
     * 根据标签名称统计数量（排除指定ID）
     */
    int countByName(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 统计会议标签被会议标准使用的次数
     */
    int countUsedByMeetingStandard(@Param("tagId") Long tagId);
}
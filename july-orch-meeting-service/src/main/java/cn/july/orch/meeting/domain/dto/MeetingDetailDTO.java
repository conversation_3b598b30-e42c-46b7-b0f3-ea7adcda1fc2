package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会议详情DTO
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "会议详情信息")
public class MeetingDetailDTO {

    @ApiModelProperty(value = "会议ID")
    private Long id;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议描述")
    private String meetingDescription;

    @ApiModelProperty(value = "会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "会议室ID")
    private Long meetingRoomId;

    @ApiModelProperty(value = "会议室名称")
    private String meetingRoomName;

    @ApiModelProperty(value = "主持人ID")
    private String hostUserId;

    @ApiModelProperty(value = "主持人姓名")
    private String hostUserName;

    @ApiModelProperty(value = "记录员ID")
    private String recorderUserId;

    @ApiModelProperty(value = "记录员姓名")
    private String recorderUserName;

    @ApiModelProperty(value = "会议链接")
    private String meetingUrl;

    @ApiModelProperty(value = "会议标签")
    private List<SimpleMeetingTagDTO> meetingTags;

    @ApiModelProperty(value = "参会人员详情")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty(value = "会议议程")
    private List<AgendaItemDTO> agendaItems;

    @ApiModelProperty(value = "会前文档列表")
    private List<PreMeetingDocumentDTO> preMeetingDocuments;

    @ApiModelProperty(value = "AI纪要")
    private String aiTranscriptMd;

    @ApiModelProperty(value = "会议文字记录")
    private MeetingTranscriptDTO meetingTranscript;

    @ApiModelProperty(value = "会议关联任务")
    private Map<TaskStatusEnum, List<TaskInfoDTO>> relatedTasks;

    @ApiModelProperty(value = "会议规划ID")
    private Long meetingPlanId;

    @ApiModelProperty(value = "会议规划详细信息")
    private MeetingPlanDetailDTO meetingPlanDetail;
}
package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.GetMeetingModel;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.service.TenantFeishuClientManager;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import com.lark.oapi.service.vc.v1.model.Meeting;
import com.lark.oapi.service.vc.v1.model.MeetingParticipant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议实际信息补充定时任务
 * @date 2025-01-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MeetingActualInfoSupplementTask {

    private final NewMeetingMapper newMeetingMapper;
    private final TenantFeishuClientManager tenantFeishuClientManager;

    /**
     * 补充会议实际信息定时任务 - 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1800000毫秒
    @IgnoreTenant
    public void supplementMeetingActualInfo() {
        log.info("开始执行会议实际信息补充定时任务");

        try {
            // 查询已结束但缺少实际会议信息的会议
            List<NewMeetingPO> meetingsToSupplement = findMeetingsNeedSupplement();
            log.info("找到{}个需要补充实际信息的会议", meetingsToSupplement.size());

            int successCount = 0;
            int failCount = 0;

            for (NewMeetingPO meeting : meetingsToSupplement) {
                try {
                    boolean success = supplementMeetingActualInfo(meeting);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("补充会议实际信息失败，会议ID：{}，飞书会议ID：{}",
                            meeting.getId(), meeting.getFsMeetingId(), e);
                    failCount++;
                }
            }

            log.info("会议实际信息补充定时任务执行完成，成功：{}个，失败：{}个", successCount, failCount);

        } catch (Exception e) {
            log.error("会议实际信息补充定时任务执行异常", e);
        }
    }

    /**
     * 查询需要补充实际信息的会议
     */
    private List<NewMeetingPO> findMeetingsNeedSupplement() {
        LambdaQueryWrapper<NewMeetingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.ENDED.getCode())
                .isNotNull(NewMeetingPO::getFsMeetingId)
                .ne(NewMeetingPO::getFsMeetingId, "")
                .isNull(NewMeetingPO::getActualStartTime)
                .isNull(NewMeetingPO::getActualEndTime)
                .isNull(NewMeetingPO::getActualAttendees); // 限制每次处理的数量，避免一次性处理太多

        return newMeetingMapper.selectList(queryWrapper);
    }

    /**
     * 补充单个会议的实际信息
     */
    private boolean supplementMeetingActualInfo(NewMeetingPO meeting) {
        try {
            log.info("开始补充会议实际信息，会议ID：{}，飞书会议ID：{}", meeting.getId(), meeting.getFsMeetingId());
            FeishuAppClient client = tenantFeishuClientManager.getClient(meeting.getTenantId());
            // 调用飞书API获取会议详情
            GetMeetingModel fsMeetingReq = GetMeetingModel.builder()
                    .meetingId(meeting.getFsMeetingId())
                    .withParticipants(true)
                    .userIdType("open_id")
                    .build();
            GetMeetingRespBody response = client.getMeetService().getMeeting(fsMeetingReq);

            if (response == null || response.getMeeting() == null) {
                log.warn("飞书API返回空数据，会议ID：{}，飞书会议ID：{}", meeting.getId(), meeting.getFsMeetingId());
                return false;
            }

            Meeting fsMeeting = response.getMeeting();

            // 获取实际开始时间和结束时间
            LocalDateTime actualStartTime = null;
            LocalDateTime actualEndTime = null;

            if (fsMeeting.getStartTime() != null) {
                try {
                    actualStartTime = LocalDateTime.ofEpochSecond(Long.parseLong(fsMeeting.getStartTime()),0, ZoneOffset.ofHours(8));
                } catch (Exception e) {
                    log.warn("解析实际开始时间失败，会议ID：{}，时间：{}", meeting.getId(), fsMeeting.getStartTime());
                }
            }

            if (fsMeeting.getEndTime() != null) {
                try {
                    actualEndTime = LocalDateTime.ofEpochSecond(Long.parseLong(fsMeeting.getEndTime()),0, ZoneOffset.ofHours(8));
                } catch (Exception e) {
                    log.warn("解析实际结束时间失败，会议ID：{}，时间：{}", meeting.getId(), fsMeeting.getEndTime());
                }
            }

            // 获取实际参会人员open_id列表
            List<String> actualAttendees = null;
            if (fsMeeting.getParticipants() != null && fsMeeting.getParticipants().length > 0) {
                actualAttendees = Arrays.stream(fsMeeting.getParticipants())
                        .map(MeetingParticipant::getId)
                        .collect(Collectors.toList());
            }

            // 更新会议实际执行信息
            NewMeetingPO updatePO = new NewMeetingPO();
            updatePO.setId(meeting.getId());

            // 只更新为空的字段，避免覆盖已有数据
            if (meeting.getActualStartTime() == null && actualStartTime != null) {
                updatePO.setActualStartTime(actualStartTime);
            }
            if (meeting.getActualEndTime() == null && actualEndTime != null) {
                updatePO.setActualEndTime(actualEndTime);
            }
            if (meeting.getActualAttendees() == null && actualAttendees != null) {
                updatePO.setActualAttendees(actualAttendees);
            }

            // 如果有需要更新的字段，则执行更新
            if (updatePO.getActualStartTime() != null ||
                    updatePO.getActualEndTime() != null ||
                    updatePO.getActualAttendees() != null) {

                newMeetingMapper.updateById(updatePO);

                log.info("会议实际信息补充成功，会议ID：{}，实际开始时间：{}，实际结束时间：{}，实际参会人数：{}",
                        meeting.getId(),
                        updatePO.getActualStartTime() != null ? updatePO.getActualStartTime() : "未更新",
                        updatePO.getActualEndTime() != null ? updatePO.getActualEndTime() : "未更新",
                        updatePO.getActualAttendees() != null ? updatePO.getActualAttendees().size() : "未更新");

                return true;
            } else {
                log.info("会议实际信息无需更新，会议ID：{}", meeting.getId());
                return true;
            }

        } catch (Exception e) {
            log.error("补充会议实际信息失败，会议ID：{}，飞书会议ID：{}，错误：{}",
                    meeting.getId(), meeting.getFsMeetingId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 手动触发会议实际信息补充（用于测试和调试）
     */
    public void manualSupplementMeetingActualInfo() {
        log.info("手动触发会议实际信息补充任务");
        supplementMeetingActualInfo();
    }
}


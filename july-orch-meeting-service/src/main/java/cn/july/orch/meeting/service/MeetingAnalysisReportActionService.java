package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.command.GenerateAnalysisReportCommand;
import cn.july.orch.meeting.domain.command.MeetingAnalysisReportCommand;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;
import cn.july.orch.meeting.domain.po.MeetingMinutePO;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import cn.july.orch.meeting.enums.ReportStatusEnum;
import cn.july.orch.meeting.mapper.MeetingMinuteMapper;
import cn.july.orch.meeting.repository.IMeetingAnalysisReportRepository;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会议分析报告Action服务
 */
@Slf4j
@Service
public class MeetingAnalysisReportActionService {

    @Resource
    private IMeetingAnalysisReportRepository meetingAnalysisReportRepository;

    @Resource
    private NewMeetingQueryService meetingRepository;

    @Resource
    private AgentInvokeService agentInvokeService;

    @Resource
    private MeetingMinuteMapper meetingMinuteMapper;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 会议分析智能体应用ID
     */
    private String getMeetingAnalysisAppId() {
        return AgentConstants.MEETING_ANALYSIS_APP_ID;
    }

    /**
     * AI智能纪要智能体应用ID
     */
    private String getAiTranscriptAppId() {
        return AgentConstants.AI_TRANSCRIPT_APP_ID;
    }

    /**
     * 创建会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createReport(MeetingAnalysisReportCommand command) {
        log.info("创建会议分析报告，会议ID：{}", command.getMeetingId());

        // 检查是否已存在报告，如果存在则更新状态为生成中
        MeetingAnalysisReport existingReport = meetingAnalysisReportRepository.findByMeetingId(command.getMeetingId());
        if (existingReport != null) {
            log.info("会议已存在分析报告，更新状态为生成中，会议ID：{}", existingReport.getId());
            existingReport.setStatus(ReportStatusEnum.GENERATED);
            existingReport.setOverallScore(command.getOverallScore());
            existingReport.setOverallSummary(command.getOverallSummary());
            existingReport.setContentAnalysisJson(command.getContentAnalysisJson());
            existingReport.setAiSuggestionsJson(command.getAiSuggestionsJson());
            meetingAnalysisReportRepository.update(existingReport);
            return existingReport.getId();
        }

        // 创建新报告，状态设置为生成中
        MeetingAnalysisReport report = MeetingAnalysisReport.builder()
                .meetingId(command.getMeetingId())
                .status(ReportStatusEnum.GENERATED)
                .overallScore(command.getOverallScore())
                .overallSummary(command.getOverallSummary())
                .contentAnalysisJson(command.getContentAnalysisJson())
                .aiSuggestionsJson(command.getAiSuggestionsJson())
                .build();

        meetingAnalysisReportRepository.save(report);
        log.info("会议分析报告创建成功，报告ID：{}", report.getId());

        return report.getId();
    }

    /**
     * 更新会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReport(Long id, MeetingAnalysisReportCommand command) {
        log.info("更新会议分析报告，报告ID：{}", id);

        // 查询现有报告
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findById(id);
        if (report == null) {
            log.warn("会议分析报告不存在，报告ID：{}", id);
            throw new IllegalArgumentException("会议分析报告不存在");
        }

        // 更新报告内容
        report.setOverallScore(command.getOverallScore());
        report.setOverallSummary(command.getOverallSummary());
        report.setContentAnalysisJson(command.getContentAnalysisJson());
        report.setAiSuggestionsJson(command.getAiSuggestionsJson());

        meetingAnalysisReportRepository.update(report);
        log.info("会议分析报告更新成功，报告ID：{}", id);
    }

    /**
     * 删除会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteReport(Long id) {
        log.info("删除会议分析报告，报告ID：{}", id);

        // 查询现有报告
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findById(id);
        if (report == null) {
            log.warn("会议分析报告不存在，报告ID：{}", id);
            throw new IllegalArgumentException("会议分析报告不存在");
        }

        meetingAnalysisReportRepository.deleteById(id);
        log.info("会议分析报告删除成功，报告ID：{}", id);
    }

    /**
     * 初始化报告记录（同步方法）
     */
    @Transactional(rollbackFor = Exception.class)
    public Long initReport(Long meetingId) {
        // 检查会议是否存在
        NewMeetingDTO meetingAgg = meetingRepository.getById(meetingId);
        if (meetingAgg == null) {
            log.warn("会议不存在，会议ID：{}", meetingId);
            throw new IllegalArgumentException("会议不存在");
        }

        // 检查是否已存在报告记录
        QueryWrapper<MeetingMinutePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("meeting_id", meetingId);
        MeetingMinutePO meetingMinute = meetingMinuteMapper.selectOne(queryWrapper);
        if (meetingMinute == null) {
            log.warn("会议记录不存在，会议ID：{}", meetingId);
            throw new BusinessException("妙计还未生成，无法创建分析报告，请稍后再试");
        }

        // 创建或更新报告状态为生成中
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report == null) {
            report = MeetingAnalysisReport.builder()
                    .meetingId(meetingId)
                    .status(ReportStatusEnum.GENERATING)
                    .build();
            meetingAnalysisReportRepository.save(report);
            log.info("创建会议分析报告记录，状态为生成中，会议ID：{}", meetingId);
        } else {
            report.setStatus(ReportStatusEnum.GENERATING);
            meetingAnalysisReportRepository.update(report);
            log.info("更新会议分析报告状态为生成中，报告ID：{}", report.getId());
        }

        return report.getId();
    }

    /**
     * 生成会议分析报告（异步方法）
     */
    @Async("reportExecutor")
    public void generateReport(GenerateAnalysisReportCommand command) {
        log.info("开始异步生成会议分析报告，会议ID：{}", command.getMeetingId());
        Long meetingId = command.getMeetingId();

        try {
            // 构建提示词
            String prompt = "请对这个会议进行全面分析。分析内容包括：\n" +
                    "1. 会议整体评分（0-100分）和总结\n" +
                    "2. 从多个维度（如目标明确度、讨论效率、决策达成度等）进行评分和分析\n" +
                    "3. 提供具体的优化建议";

            // 构建variables参数
            Map<String, Object> variables = new HashMap<>();
            variables.put("meetingId", meetingId);

            // 调用智能体生成分析报告
            AgentCompleteRespDTO response = agentInvokeService.invokeStreamGetAnswer(
                    getMeetingAnalysisAppId(),
                    prompt,
                    variables,
                    AgentConstants.REPORT_AUTHORIZATION
            );

            if (response == null || response.getAnswer() == null) {
                throw new RuntimeException("生成分析报告失败：智能体返回结果为空");
            }

        } catch (Exception e) {
            log.error("生成会议分析报告失败，会议ID：{}", meetingId, e);
            throw new RuntimeException("生成会议分析报告失败：" + e.getMessage());
        }
    }

    /**
     * 生成AI智能纪要（异步方法）
     */
    @Async("reportExecutor")
    public void generateAiTranscript(Long meetingId) {
        log.info("开始异步生成AI智能纪要，会议ID：{}", meetingId);

        try {
            // 查询会议纪要文本
            QueryWrapper<MeetingMinutePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("meeting_id", meetingId);
            MeetingMinutePO meetingMinute = meetingMinuteMapper.selectOne(queryWrapper);
            if (meetingMinute == null || meetingMinute.getMinuteText() == null) {
                throw new RuntimeException("会议纪要不存在，无法生成AI纪要");
            }

            // 更新AI纪要状态为生成中
            updateAiTranscriptStatus(meetingId, ReportStatusEnum.GENERATING);

            // 构建提示词
            String prompt = "请根据以下会议纪要内容，生成一份结构化的AI智能纪要，采用Markdown格式输出。包括：\n" +
                    "1. 会议基本信息\n" +
                    "2. 会议核心摘要\n" +
                    "3. 主要议题与讨论\n" +
                    "4. 决议与结论";

            // 构建 variables参数
            NewMeetingDTO meetingDTO = meetingRepository.getById(meetingId);
            Map<String, Object> variables = new HashMap<>();
            variables.put("meetingId", meetingId);
            variables.put("minuteText", meetingMinute.getMinuteText());
            variables.put("meetingName", meetingDTO.getMeetingName());
            variables.put("meetingDescription", meetingDTO.getMeetingDescription());
            variables.put("meetingLocation", meetingDTO.getMeetingLocation());
            variables.put("hostUserDetail", meetingDTO.getHostUserDetail().getName());
            variables.put("recorderUserDetail", meetingDTO.getRecorderUserDetail().getName());
            variables.put("meetingUrl", meetingDTO.getMeetingUrl());
            variables.put("minuteUrl", meetingDTO.getMinuteUrl());
            variables.put("actualAttendeeDetails", meetingDTO.getActualAttendeeDetails().stream().map(FSUserInfoDTO::getName).collect(Collectors.toList()));
            variables.put("actualStartTime", meetingDTO.getActualStartTime());
            variables.put("actualEndTime", meetingDTO.getActualEndTime());

            // 调用智能体生成AI纪要
            AgentCompleteRespDTO response = agentInvokeService.invokeStreamGetAnswer(
                    getAiTranscriptAppId(),
                    prompt,
                    variables,
                    AgentConstants.AI_TRANSCRIPT_AUTHORIZATION
            );

            if (response == null || response.getAnswer() == null) {
                throw new RuntimeException("生成AI纪要失败：智能体返回结果为空");
            }

            // 更新AI纪要内容和状态
            updateAiTranscriptContent(meetingId, response.getAnswer(), ReportStatusEnum.GENERATED);
            log.info("生成AI智能纪要成功，会议ID：{}", meetingId);

        } catch (Exception e) {
            log.error("生成AI智能纪要失败，会议ID：{}", meetingId, e);
            // 更新状态为失败
            updateAiTranscriptStatus(meetingId, ReportStatusEnum.FAILED);
            throw new RuntimeException("生成AI智能纪要失败：" + e.getMessage());
        }
    }

    /**
     * 更新AI纪要内容
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAiTranscript(Long meetingId, String aiTranscriptMd) {
        log.info("更新AI纪要内容，会议ID：{}", meetingId);

        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report == null) {
            // 如果没有报告记录，先创建一个
            report = MeetingAnalysisReport.builder()
                    .meetingId(meetingId)
                    .status(ReportStatusEnum.GENERATING)
                    .aiTranscriptStatus(ReportStatusEnum.GENERATED)
                    .aiTranscriptMd(aiTranscriptMd)
                    .build();
            meetingAnalysisReportRepository.save(report);
        } else {
            // 更新现有报告
            report.setAiTranscriptMd(aiTranscriptMd);
            report.setAiTranscriptStatus(ReportStatusEnum.GENERATED);
            meetingAnalysisReportRepository.update(report);
        }

        log.info("更新AI纪要内容成功，会议ID：{}", meetingId);
    }

    /**
     * 更新AI纪要状态
     */
    private void updateAiTranscriptStatus(Long meetingId, ReportStatusEnum status) {
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report != null) {
            report.setAiTranscriptStatus(status);
            meetingAnalysisReportRepository.update(report);
        }
    }

    /**
     * 更新AI纪要内容和状态
     */
    private void updateAiTranscriptContent(Long meetingId, String aiTranscriptMd, ReportStatusEnum status) {
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report != null) {
            report.setAiTranscriptMd(aiTranscriptMd);
            report.setAiTranscriptStatus(status);
            meetingAnalysisReportRepository.update(report);
        }
    }
}


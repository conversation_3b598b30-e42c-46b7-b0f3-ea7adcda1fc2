package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.MeetingTagAssembler;
import cn.july.orch.meeting.domain.command.MeetingTagCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingTagUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import cn.july.orch.meeting.domain.entity.MeetingTag;
import cn.july.orch.meeting.domain.po.MeetingTagPO;
import cn.july.orch.meeting.domain.query.MeetingTagQuery;
import cn.july.orch.meeting.mapper.MeetingTagMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标签服务
 * @date 2025-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingTagService {

    private final MeetingTagMapper meetingTagMapper;
    private final MeetingTagAssembler meetingTagAssembler;

    /**
     * 查询所有会议标签列表
     */
    public List<MeetingTagDTO> listAll() {
        LambdaQueryWrapper<MeetingTagPO> wrapper = Wrappers.lambdaQuery(MeetingTagPO.class);
        wrapper.orderByDesc(MeetingTagPO::getCreateTime);
        
        List<MeetingTagPO> pos = meetingTagMapper.selectList(wrapper);
        return pos.stream()
            .map(po -> meetingTagAssembler.toDTO(meetingTagAssembler.toEntity(po)))
            .collect(Collectors.toList());
    }

    /**
     * 根据ID查询会议标签详情
     */
    public MeetingTagDTO getById(Long id) {
        MeetingTagPO po = meetingTagMapper.selectById(id);
        if (po == null) {
            return null;
        }
        MeetingTag entity = meetingTagAssembler.toEntity(po);
        return meetingTagAssembler.toDTO(entity);
    }

    /**
     * 分页查询会议标签
     */
    public PageResultDTO<MeetingTagDTO> pageQuery(MeetingTagQuery query) {
        LambdaQueryWrapper<MeetingTagPO> wrapper = Wrappers.lambdaQuery(MeetingTagPO.class);

        // 标签名称模糊查询
        if (query.getName() != null && !query.getName().trim().isEmpty()) {
            wrapper.like(MeetingTagPO::getName, query.getName().trim());
        }

        // 标签颜色筛选
        if (query.getColor() != null && !query.getColor().trim().isEmpty()) {
            wrapper.eq(MeetingTagPO::getColor, query.getColor().trim());
        }

        // 创建人ID筛选
        if (query.getCreateUserId() != null && !query.getCreateUserId().trim().isEmpty()) {
            wrapper.eq(MeetingTagPO::getCreateUserId, query.getCreateUserId().trim());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(MeetingTagPO::getCreateTime);

        Page<MeetingTagPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<MeetingTagPO> result = meetingTagMapper.selectPage(page, wrapper);

        // 转换为 PageResultDTO
        List<MeetingTagDTO> dtoList = result.getRecords().stream()
                .map(po -> meetingTagAssembler.toDTO(meetingTagAssembler.toEntity(po)))
                .collect(Collectors.toList());

        PageResultDTO<MeetingTagDTO> pageResult = new PageResultDTO<>();
        pageResult.setPageNo(Math.toIntExact(result.getCurrent()));
        pageResult.setPageSize(Math.toIntExact(result.getSize()));
        pageResult.setTotal(result.getTotal());
        pageResult.setTotalPages((int) result.getPages());
        pageResult.setList(dtoList);

        return pageResult;
    }

    /**
     * 创建会议标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMeetingTag(MeetingTagCreateCommand command) {
        // 1. 转换为领域对象
        MeetingTag meetingTag = meetingTagAssembler.toEntity(command);
        
        // 2. 基础验证
        validateMeetingTag(meetingTag);
        
        // 3. 检查名称唯一性
        if (existsByName(meetingTag.getName(), null)) {
            throw new BusinessException("标签名称已存在");
        }
        
        // 4. 保存
        MeetingTagPO po = meetingTagAssembler.toPO(meetingTag);
        meetingTagMapper.insert(po);
    }

    /**
     * 更新会议标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingTag(MeetingTagUpdateCommand command) {
        // 1. 检查是否存在
        MeetingTag existing = meetingTagAssembler.toEntity(meetingTagMapper.selectById(command.getId()));
        if (existing == null) {
            throw new BusinessException("会议标签不存在");
        }
        
        // 2. 转换为领域对象
        MeetingTag meetingTag = meetingTagAssembler.toEntity(command);
        
        // 3. 基础验证
        validateMeetingTag(meetingTag);
        
        // 4. 检查名称唯一性（排除自己）
        if (existsByName(meetingTag.getName(), meetingTag.getId())) {
            throw new BusinessException("标签名称已存在");
        }
        
        // 5. 更新
        MeetingTagPO po = meetingTagAssembler.toPO(meetingTag);
        meetingTagMapper.updateById(po);
    }

    /**
     * 删除会议标签
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeetingTag(Long id) {
        // 1. 检查是否存在
        MeetingTag existing = meetingTagAssembler.toEntity(meetingTagMapper.selectById(id));
        if (existing == null) {
            throw new BusinessException("会议标签不存在");
        }
        
        // 2. 检查是否被使用
        if (isUsedByMeetingStandard(id)) {
            throw new BusinessException("该会议标签已被会议标准使用，无法删除");
        }
        
        // 3. 删除
        meetingTagMapper.deleteById(id);
    }

    /**
     * 验证会议标签基础数据
     */
    private void validateMeetingTag(MeetingTag meetingTag) {
        // 1. 标签名称验证
        if (meetingTag.getName() == null || meetingTag.getName().trim().isEmpty()) {
            throw new BusinessException("标签名称不能为空");
        }
        
        if (meetingTag.getName().length() > 50) {
            throw new BusinessException("标签名称长度不能超过50个字符");
        }
        
        // 2. 标签颜色验证
        if (meetingTag.getColor() == null || meetingTag.getColor().trim().isEmpty()) {
            throw new BusinessException("标签颜色不能为空");
        }
        
        if (!meetingTag.getColor().matches("^#[0-9A-Fa-f]{6}$")) {
            throw new BusinessException("标签颜色格式不正确，应为Hex格式如#3498DB");
        }
        
        // 3. 标签描述验证
        if (meetingTag.getDescription() != null && meetingTag.getDescription().length() > 255) {
            throw new BusinessException("标签描述长度不能超过255个字符");
        }
    }

    /**
     * 检查标签名称是否存在
     */
    private boolean existsByName(String name, Long excludeId) {
        return meetingTagMapper.countByName(name, excludeId) > 0;
    }

    /**
     * 检查是否被会议标准使用
     */
    private boolean isUsedByMeetingStandard(Long tagId) {
        return meetingTagMapper.countUsedByMeetingStandard(tagId) > 0;
    }
}
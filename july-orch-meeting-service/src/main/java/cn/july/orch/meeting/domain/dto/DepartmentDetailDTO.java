package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 部门详情DTO
 * @date 2025-01-24
 */
@Data
public class DepartmentDetailDTO {

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "部门名称")
    private String name;

    @ApiModelProperty(value = "部门英文名称")
    private String nameEn;

    @ApiModelProperty(value = "父部门ID")
    private String parentDepartmentId;

    @ApiModelProperty(value = "部门负责人ID")
    private String managerId;

    @ApiModelProperty(value = "部门描述")
    private String description;

    @ApiModelProperty(value = "部门状态")
    private Integer status;

    @ApiModelProperty(value = "部门类型")
    private Integer departmentType;

    @ApiModelProperty(value = "部门排序")
    private Integer order;

    @ApiModelProperty(value = "部门创建时间")
    private String createTime;

    @ApiModelProperty(value = "部门更新时间")
    private String updateTime;
}

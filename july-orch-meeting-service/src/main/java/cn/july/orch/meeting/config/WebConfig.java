package cn.july.orch.meeting.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    @Lazy
    private SsoAuthFilter ssoAuthFilter;
    
    @Resource
    @Lazy
    private TenantContextFilter tenantContextFilter;

    private static final String[] IGNORE_PATHS = {"/static/**", "/public/**", "/resources/**","/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/doc.html", "/favicon.ico", "/error"};

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加认证过滤器
        registry.addInterceptor(ssoAuthFilter).addPathPatterns("/**").excludePathPatterns(IGNORE_PATHS);
        // 添加租户上下文过滤器，确保在认证过滤器之后执行
        registry.addInterceptor(tenantContextFilter).addPathPatterns("/**").excludePathPatterns(IGNORE_PATHS);
    }

    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new InsertOrUpdateMetaObjectHandler();
    }
}
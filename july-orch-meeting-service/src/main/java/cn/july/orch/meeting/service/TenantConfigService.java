package cn.july.orch.meeting.service;

import cn.hutool.core.util.StrUtil;
import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.domain.dto.TenantConfigDTO;
import cn.july.orch.meeting.domain.po.TenantConfigPO;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.mapper.TenantConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户配置服务
 */
@Slf4j
@Service
public class TenantConfigService {

    @Resource
    private TenantConfigMapper tenantConfigMapper;

    /**
     * 根据租户ID获取配置
     */
    public TenantConfigDTO getByTenantId(Long tenantId) {
        if (tenantId == null) {
            throw new BusinessException(MessageCode.TENANT_ID_REQUIRED);
        }

        TenantConfigPO configPO = tenantConfigMapper.selectByTenantId(tenantId);
        if (configPO == null) {
            throw new BusinessException(MessageCode.TENANT_NOT_FOUND);
        }

        return toDTO(configPO);
    }

    /**
     * 根据飞书应用ID获取配置
     */
    public TenantConfigDTO getByFeishuAppId(String feishuAppId) {
        if (StrUtil.isBlank(feishuAppId)) {
            throw new BusinessException(MessageCode.TENANT_CONFIG_ERROR);
        }

        TenantConfigPO configPO = tenantConfigMapper.selectByFeishuAppId(feishuAppId);
        if (configPO == null) {
            throw new BusinessException(MessageCode.TENANT_NOT_FOUND);
        }

        return toDTO(configPO);
    }

    /**
     * 获取所有启用的租户配置
     */
    public List<TenantConfigDTO> getEnabledConfigs() {
        List<TenantConfigPO> configPOs = tenantConfigMapper.selectEnabledConfigs();
        return configPOs.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 检查租户ID是否存在
     */
    public boolean existsByTenantId(Long tenantId) {
        return tenantConfigMapper.checkTenantIdExists(tenantId) > 0;
    }

    /**
     * 检查飞书应用ID是否存在
     */
    public boolean existsByFeishuAppId(String feishuAppId) {
        return tenantConfigMapper.checkFeishuAppIdExists(feishuAppId) > 0;
    }

    /**
     * PO转DTO
     */
    private TenantConfigDTO toDTO(TenantConfigPO po) {
        TenantConfigDTO dto = new TenantConfigDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
}

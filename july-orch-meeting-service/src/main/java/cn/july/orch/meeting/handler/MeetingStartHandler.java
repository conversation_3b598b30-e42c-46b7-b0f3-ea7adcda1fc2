package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.MeetingStartEndEventCommand;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.service.MeetingCheckInService;
import cn.july.orch.meeting.service.MeetingStandardService;
import cn.july.orch.meeting.service.NewMeetingActionService;
import cn.july.orch.meeting.service.NewMeetingQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议开始回调处理器
 * @date 2024-12-25
 */
@Slf4j
@Component
public class MeetingStartHandler implements CallbackHandler<MeetingStartEndEventCommand> {

    @Resource
    private NewMeetingActionService newMeetingActionService;

    @Resource
    private NewMeetingQueryService newMeetingQueryService;

    @Resource
    private MeetingCheckInService meetingCheckInService;

    @Resource
    private MeetingStandardService meetingStandardService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingStartEndEventCommand startCommand = JsonUtils.parse(meetingJson, MeetingStartEndEventCommand.class);
        MeetingStartEndEventCommand.Meeting fsCommand = startCommand.getMeeting();
        
        // 新会议同步
        newMeetingActionService.handleFeishuMeetingStartCallback(
            fsCommand.getCalendarEventId(),
            fsCommand.getMeetingNo(),
            fsCommand.getId(),
            fsCommand.getStartTime()
        );

        // 处理会议开始时的签到卡片发送
        handleMeetingStartCheckIn(fsCommand.getCalendarEventId());
    }

    /**
     * 处理会议开始时的签到卡片发送
     * @param fsCalendarEventId 飞书日程事件ID
     */
    private void handleMeetingStartCheckIn(String fsCalendarEventId) {
        try {
            log.info("处理会议开始签到卡片发送: fsCalendarEventId={}", fsCalendarEventId);
            
            // 根据飞书日程事件ID查询会议信息
            NewMeetingDTO meetingDTO = newMeetingQueryService.findByFsCalendarEventId(fsCalendarEventId);
            if (meetingDTO == null) {
                log.warn("未找到对应的会议: fsCalendarEventId={}", fsCalendarEventId);
                return;
            }

            // 判断是否需要发送签到卡片
            if (shouldSendCheckInCard(meetingDTO)) {
                log.info("会议需要发送签到卡片: meetingId={}, meetingName={}", 
                        meetingDTO.getId(), meetingDTO.getMeetingName());
                
                // 调用签到服务发送卡片
                meetingCheckInService.processSingleMeetingCheckIn(convertToEntity(meetingDTO));
            } else {
                log.info("会议不需要发送签到卡片: meetingId={}, meetingName={}", 
                        meetingDTO.getId(), meetingDTO.getMeetingName());
            }

        } catch (Exception e) {
            log.error("处理会议开始签到卡片发送失败: fsCalendarEventId={}", fsCalendarEventId, e);
        }
    }

    /**
     * 判断是否需要发送签到卡片
     * @param meetingDTO 会议信息
     * @return 是否需要发送
     */
    private boolean shouldSendCheckInCard(NewMeetingDTO meetingDTO) {
        // 1. 检查是否已经发送过签到卡片
        if (Boolean.TRUE.equals(meetingDTO.getCheckinReminderSent())) {
            log.info("会议已发送过签到卡片，跳过: meetingId={}", meetingDTO.getId());
            return false;
        }

        // 2. 检查会议标准是否启用签到
        if (meetingDTO.getMeetingStandardId() == null) {
            log.info("会议没有关联会议标准，跳过签到: meetingId={}", meetingDTO.getId());
            return false;
        }

        // 3. 查询会议标准的签到配置
        try {
            MeetingStandardDTO standardDTO =
                    meetingStandardService.getById(meetingDTO.getMeetingStandardId());
            
            if (standardDTO == null) {
                log.warn("未找到会议标准: meetingId={}, standardId={}", 
                        meetingDTO.getId(), meetingDTO.getMeetingStandardId());
                return false;
            }

            // 4. 检查签到配置是否启用
            if (standardDTO.getCheckInConfig() == null || 
                !Boolean.TRUE.equals(standardDTO.getCheckInConfig().getEnabled())) {
                log.info("会议标准未启用签到功能: meetingId={}, standardId={}", 
                        meetingDTO.getId(), meetingDTO.getMeetingStandardId());
                return false;
            }

            log.info("会议需要发送签到卡片: meetingId={}, standardId={}, checkInConfig={}", 
                    meetingDTO.getId(), meetingDTO.getMeetingStandardId(), 
                    standardDTO.getCheckInConfig());
            return true;

        } catch (Exception e) {
            log.error("查询会议标准签到配置失败: meetingId={}, standardId={}", 
                    meetingDTO.getId(), meetingDTO.getMeetingStandardId(), e);
            return false;
        }
    }

    /**
     * 将NewMeetingDTO转换为NewMeeting实体
     * @param dto DTO对象
     * @return 实体对象
     */
    private cn.july.orch.meeting.domain.entity.NewMeeting convertToEntity(NewMeetingDTO dto) {
        return cn.july.orch.meeting.domain.entity.NewMeeting.builder()
                .id(dto.getId())
                .tenantId(dto.getTenantId())
                .meetingName(dto.getMeetingName())
                .meetingDescription(dto.getMeetingDescription())
                .meetingPlanId(dto.getMeetingPlanId())
                .meetingStandardId(dto.getMeetingStandardId())
                .meetingNo(dto.getMeetingNo())
                .startTime(dto.getStartTime())
                .endTime(dto.getEndTime())
                .status(dto.getStatus())
                .meetingRoomId(dto.getMeetingRoomId())
                .meetingLocation(dto.getMeetingLocation())
                .attendees(dto.getAttendees())
                .hostUserId(dto.getHostUserId())
                .recorderUserId(dto.getRecorderUserId())
                .fsCalendarEventId(dto.getFsCalendarEventId())
                .fsMeetingId(dto.getFsMeetingId())
                .meetingUrl(dto.getMeetingUrl())
                .minuteUrl(dto.getMinuteUrl())
                .createUserId(dto.getCreateUserId())
                .createUserName(dto.getCreateUserName())
                .createTime(dto.getCreateTime())
                .updateUserId(dto.getUpdateUserId())
                .updateUserName(dto.getUpdateUserName())
                .updateTime(dto.getUpdateTime())
                .enableDocAiSummary(dto.getEnableDocAiSummary())
                .meetingTags(dto.getMeetingTags())
                .preMeetingDocuments(dto.getPreMeetingDocuments())
                .actualStartTime(dto.getActualStartTime())
                .actualEndTime(dto.getActualEndTime())
                .actualAttendees(dto.getActualAttendees())
                .checkinReminderSent(dto.getCheckinReminderSent())
                .sendCheckinTime(dto.getSendCheckinTime())
                .build();
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_START;
    }
}

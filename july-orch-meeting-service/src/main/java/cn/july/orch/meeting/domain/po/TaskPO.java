package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * TaskPO对象
 *
 * <AUTHOR> Assistant
 * @desc 任务信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "tasks", autoResultMap = true)
public class TaskPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 关联的任务清单ID (逻辑关联 task_lists.id)
     */
    @TableField("task_list_id")
    private Long taskListId;

    /**
     * 父任务ID (自关联, 逻辑关联 tasks.id)
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 对应的飞书任务ID，用于API同步
     */
    @TableField("feishu_task_id")
    private String feishuTaskId;

    /**
     * 任务标题
     */
    @TableField("title")
    private String title;

    /**
     * 任务描述
     */
    @TableField("description")
    private String description;

    /**
     * 负责人open_id
     */
    @TableField("owner_open_id")
    private String ownerOpenId;

    /**
     * 负责人名称
     */
    @TableField("owner_name")
    private String ownerName;

    /**
     * 优先级 (0: 低, 1: 中, 2: 高)
     */
    @TableField("priority")
    private TaskPriorityEnum priority;

    /**
     * 任务状态（0:未开始；1:进行中；2:已完成；3:已超期）
     */
    @TableField("status")
    private TaskStatusEnum status;

    /**
     * 截止时间
     */
    @TableField("due_date")
    private LocalDateTime dueDate;

    /**
     * 实际完成时间
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 关联的会议ID (关联 meeting.id)
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 关联的会议标签列表
     */
    @TableField(value = "meeting_tags_json", typeHandler = cn.july.orch.meeting.config.SimpleMeetingTagListTypeHandler.class)
    private List<SimpleMeetingTagDTO> meetingTags;

    /**
     * 附件对象存储key列表 (JSON数组)
     */
    @TableField(value = "attachments_json", typeHandler = cn.july.orch.meeting.config.CustomListStringTypeHandler.class)
    private List<String> attachmentsJson;

    /**
     * 逻辑删除
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    public static final String COL_ID = "id";
    public static final String COL_TASK_LIST_ID = "task_list_id";
    public static final String COL_PARENT_ID = "parent_id";
    public static final String COL_MEETING_ID = "meeting_id";
}

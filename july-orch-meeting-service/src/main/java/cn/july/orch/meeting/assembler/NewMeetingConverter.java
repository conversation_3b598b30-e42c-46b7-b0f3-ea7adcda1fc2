package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NewMeetingConverter {

    /**
     * PO转Entity
     */
    NewMeeting toEntity(NewMeetingPO po);

    /**
     * Entity转PO
     */
    NewMeetingPO toPO(NewMeeting entity);

    /**
     * PO列表转Entity列表
     */
    List<NewMeeting> toEntityList(List<NewMeetingPO> poList);
} 
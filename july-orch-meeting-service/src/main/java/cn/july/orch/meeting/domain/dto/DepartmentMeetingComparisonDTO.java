package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 部门会议数量对比DTO
 * @date 2025-01-24
 */
@Data
public class DepartmentMeetingComparisonDTO {

    @ApiModelProperty("部门会议对比数据列表")
    private List<DepartmentMeetingDataDTO> departmentData;

    @ApiModelProperty("总计规划会议数")
    private Long totalPlannedMeetings;

    @ApiModelProperty("总计实际会议数")
    private Long totalActualMeetings;

    @ApiModelProperty("完成率（百分比）")
    private Double completionRate;

    @Data
    public static class DepartmentMeetingDataDTO {
        @ApiModelProperty("部门ID")
        private String departmentId;

        @ApiModelProperty("部门名称")
        private String departmentName;

        @ApiModelProperty("部门详情")
        private DepartmentDetailDTO departmentDetail;

        @ApiModelProperty("规划会议数")
        private Long plannedMeetings;

        @ApiModelProperty("实际会议数")
        private Long actualMeetings;

        @ApiModelProperty("完成率（百分比）")
        private Double completionRate;

        @ApiModelProperty("完成率描述")
        private String completionRateDesc;
    }
}

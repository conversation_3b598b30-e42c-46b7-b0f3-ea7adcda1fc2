package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.MeetingTagCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingTagUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import cn.july.orch.meeting.domain.query.MeetingTagQuery;
import cn.july.orch.meeting.service.MeetingTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标签控制器
 * @date 2025-08-26
 */
@Api(tags = "会议标签")
@RestController
@RequestMapping("/meeting-tag")
@RequiredArgsConstructor
public class MeetingTagController {

    private final MeetingTagService meetingTagService;

    @PostMapping("/list")
    @ApiOperation("查询所有会议标签列表")
    public List<MeetingTagDTO> list() {
        return meetingTagService.listAll();
    }

    @GetMapping("/detail")
    @ApiOperation("查询会议标签详情")
    public MeetingTagDTO detail(@RequestParam("id") Long id) {
        return meetingTagService.getById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询会议标签")
    public PageResultDTO<MeetingTagDTO> pageQuery(@RequestBody MeetingTagQuery query) {
        return meetingTagService.pageQuery(query);
    }

    @PostMapping("/create")
    @ApiOperation("创建会议标签")
    public void create(@Valid @RequestBody MeetingTagCreateCommand command) {
        meetingTagService.createMeetingTag(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新会议标签")
    public void update(@Valid @RequestBody MeetingTagUpdateCommand command) {
        meetingTagService.updateMeetingTag(command);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除会议标签")
    public void delete(@PathVariable("id") Long id) {
        meetingTagService.deleteMeetingTag(id);
    }
}
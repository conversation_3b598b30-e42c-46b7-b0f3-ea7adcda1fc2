package cn.july.orch.meeting.repository;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.assembler.TaskActivityAssembler;
import cn.july.orch.meeting.domain.entity.TaskActivityInfo;
import cn.july.orch.meeting.domain.po.TaskActivityPO;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import cn.july.orch.meeting.mapper.TaskActivityMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务动态仓储实现
 */
@Slf4j
@Repository
public class TaskActivityRepositoryImpl implements ITaskActivityRepository {

    @Resource
    private TaskActivityMapper taskActivityMapper;
    @Resource
    private TaskActivityAssembler taskActivityAssembler;

    @Override
    public Long insert(TaskActivityInfo taskActivityInfo) {
        log.debug("插入任务动态，任务ID：{}，类型：{}", taskActivityInfo.getTaskId(), taskActivityInfo.getActivityType());
        
        TaskActivityPO taskActivityPO = taskActivityAssembler.toTaskActivityPO(taskActivityInfo);
        taskActivityMapper.insert(taskActivityPO);
        
        // 设置生成的ID到实体对象中
        taskActivityInfo.setId(taskActivityPO.getId());
        
        log.debug("任务动态插入成功，ID：{}", taskActivityPO.getId());
        return taskActivityPO.getId();
    }

    @Override
    public TaskActivityInfo findById(Long id) {
        log.debug("根据ID查询任务动态，ID：{}", id);
        
        TaskActivityPO taskActivityPO = taskActivityMapper.selectById(id);
        if (taskActivityPO == null) {
            log.debug("任务动态不存在，ID：{}", id);
            return null;
        }
        
        return taskActivityAssembler.toTaskActivityInfo(taskActivityPO);
    }

    @Override
    public List<TaskActivityInfo> findByTaskId(Long taskId) {
        log.debug("根据任务ID查询动态列表，任务ID：{}", taskId);
        
        List<TaskActivityPO> taskActivityPOList = taskActivityMapper.findByTaskId(taskId);
        
        return taskActivityAssembler.toTaskActivityInfoList(taskActivityPOList);
    }

    @Override
    public List<TaskActivityInfo> findLatestByTaskId(Long taskId, Integer limit) {
        log.debug("根据任务ID查询最新动态，任务ID：{}，限制条数：{}", taskId, limit);
        
        List<TaskActivityPO> taskActivityPOList = taskActivityMapper.findLatestByTaskId(taskId, limit);
        
        return taskActivityAssembler.toTaskActivityInfoList(taskActivityPOList);
    }

    @Override
    public List<TaskActivityInfo> findByActivityType(TaskActivityTypeEnum activityType) {
        log.debug("根据动态类型查询动态列表，类型：{}", activityType);
        
        List<TaskActivityPO> taskActivityPOList = taskActivityMapper.selectList(
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getActivityType, activityType.getCode())
                .eq(TaskActivityPO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByDesc(TaskActivityPO::getCreateTime)
        );
        
        return taskActivityAssembler.toTaskActivityInfoList(taskActivityPOList);
    }

    @Override
    public List<TaskActivityInfo> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("根据时间范围查询动态列表，开始时间：{}，结束时间：{}", startTime, endTime);
        
        List<TaskActivityPO> taskActivityPOList = taskActivityMapper.selectList(
            new LambdaQueryWrapper<TaskActivityPO>()
                .between(TaskActivityPO::getCreateTime, startTime, endTime)
                .eq(TaskActivityPO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByDesc(TaskActivityPO::getCreateTime)
        );
        
        return taskActivityAssembler.toTaskActivityInfoList(taskActivityPOList);
    }

    @Override
    public List<TaskActivityInfo> findByUserId(String userId) {
        log.debug("根据用户ID查询动态列表，用户ID：{}", userId);
        
        List<TaskActivityPO> taskActivityPOList = taskActivityMapper.selectList(
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getCreateUserId, userId)
                .eq(TaskActivityPO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByDesc(TaskActivityPO::getCreateTime)
        );
        
        return taskActivityAssembler.toTaskActivityInfoList(taskActivityPOList);
    }

    @Override
    public Long countByTaskId(Long taskId) {
        log.debug("统计任务动态数量，任务ID：{}", taskId);
        
        return taskActivityMapper.selectCount(
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getTaskId, taskId)
                .eq(TaskActivityPO::getDeleted, DeletedEnum.NOT_DELETED)
        );
    }

    @Override
    public void delete(TaskActivityInfo taskActivityInfo) {
        log.debug("删除任务动态，ID：{}", taskActivityInfo.getId());
        
        // 软删除：更新deleted字段
        TaskActivityPO taskActivityPO = taskActivityAssembler.toTaskActivityPO(taskActivityInfo);
        taskActivityMapper.updateById(taskActivityPO);
        
        log.debug("任务动态删除成功，ID：{}", taskActivityPO.getId());
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        log.debug("批量删除任务动态，任务ID：{}", taskId);
        
        // 软删除：更新deleted字段
        TaskActivityPO updatePO = new TaskActivityPO();
        updatePO.setDeleted(DeletedEnum.DELETED);
        
        taskActivityMapper.update(updatePO, 
            new LambdaQueryWrapper<TaskActivityPO>()
                .eq(TaskActivityPO::getTaskId, taskId)
                .eq(TaskActivityPO::getDeleted, DeletedEnum.NOT_DELETED)
        );
        
        log.debug("批量删除任务动态成功，任务ID：{}", taskId);
    }
}
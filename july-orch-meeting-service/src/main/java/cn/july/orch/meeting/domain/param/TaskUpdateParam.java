package cn.july.orch.meeting.domain.param;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务更新参数对象
 */
@Data
@Builder
public class TaskUpdateParam {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 负责人OpenID
     */
    private String ownerOpenId;

    /**
     * 负责人名称
     */
    private String ownerName;

    /**
     * 优先级
     */
    private TaskPriorityEnum priority;

    /**
     * 截止时间
     */
    private LocalDateTime dueDate;

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 附件key列表
     */
    private List<String> attachmentKeys;
}
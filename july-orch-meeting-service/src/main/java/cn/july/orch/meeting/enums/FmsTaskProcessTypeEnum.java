package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Assistant
 * @description FMS任务处理方式枚举
 */
@Getter
@AllArgsConstructor
public enum FmsTaskProcessTypeEnum {
    EXCEL_EXPORT(1, "Excel导出", ".xlsx"),
    PDF_EXPORT(2, "PDF导出", ".pdf"),
    CSV_EXPORT(3, "CSV导出", ".csv"),
    CUSTOM_PROCESS(9, "自定义处理", ".dat");
    
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;
    private final String fileExtension;
    
    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        return fileExtension;
    }
}

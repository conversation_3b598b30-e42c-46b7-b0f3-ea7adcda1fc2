package cn.july.orch.meeting.utils.execl;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

// 自定义样式处理器
class CustomSheetWriteHandler implements SheetWriteHandler {
    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 无需操作
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheet = writeSheetHolder.getSheet();
        Row headerRow = sheet.getRow(0);

        // 创建字体
        Font font = workbook.createFont();
        font.setColor(IndexedColors.WHITE.getIndex());
        font.setFontName("宋体");
        font.setBold(true);

        // 创建样式
        CellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setFillForegroundColor((short) 1);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 应用样式到表头单元格
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            cell.setCellStyle(style);
        }
    }
}

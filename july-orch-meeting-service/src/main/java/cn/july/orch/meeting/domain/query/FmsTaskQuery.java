package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> Assistant
 * @description FMS任务查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("FMS任务查询条件")
public class FmsTaskQuery extends PageSortQuery {
    
    @ApiModelProperty("任务代码")
    private String taskCode;
    
    @ApiModelProperty("任务类型：1-上传，2-下载")
    private Integer taskType;
    
    @ApiModelProperty("任务状态：0-未开始，1-执行中，2-成功，3-失败")
    private Integer taskStatus;
    
    @ApiModelProperty("文件名称（模糊查询）")
    private String fileName;
    
}

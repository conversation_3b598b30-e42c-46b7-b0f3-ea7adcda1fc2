package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 议题查询条件
 * @date 2025-11-06
 */
@Data
@ApiModel("议题查询条件")
public class TopicQuery {

    @ApiModelProperty("议题名称(模糊查询)")
    private String name;

    @ApiModelProperty("是否启用(0-否,1-是)")
    private Integer isEnabled;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;
}

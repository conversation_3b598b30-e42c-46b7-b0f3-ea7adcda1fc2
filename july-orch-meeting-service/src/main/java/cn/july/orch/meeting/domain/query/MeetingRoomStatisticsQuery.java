package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会议室使用统计查询参数
 */
@Data
@ApiModel(description = "会议室使用统计查询参数")
public class MeetingRoomStatisticsQuery {

    @ApiModelProperty(value = "年月", example = "2025-08", notes = "格式为yyyy-MM，不传默认为当前年月")
    private String yearMonth;

    @ApiModelProperty(value = "会议室ID", notes = "不传则统计所有会议室")
    private Long meetingRoomId;
}
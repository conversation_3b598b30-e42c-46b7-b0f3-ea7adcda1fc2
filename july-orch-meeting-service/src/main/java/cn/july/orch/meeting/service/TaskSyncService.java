package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务同步服务 - 处理飞书任务与本地任务的同步
 */
@Slf4j
@Service
public class TaskSyncService {

    @Resource
    private TaskDomainService taskDomainService;

    /**
     * 同步飞书任务状态到本地任务
     *
     * @param feishuTaskId 飞书任务ID
     * @param isCompleted 是否已完成
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTaskStatus(String feishuTaskId, boolean isCompleted) {
        log.info("同步飞书任务状态，飞书任务ID：{}，是否完成：{}", feishuTaskId, isCompleted);

        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (taskAgg == null) {
            log.warn("未找到对应的本地任务，飞书任务ID：{}", feishuTaskId);
            return;
        }

        TaskStatusEnum currentStatus = taskAgg.getInfo().getStatus();

        if (isCompleted && currentStatus != TaskStatusEnum.COMPLETED) {
            // 使用系统用户作为操作人完成任务
            taskAgg.complete("system", "系统");
            taskDomainService.update(taskAgg);
            log.info("任务状态同步为已完成，飞书任务ID：{}", feishuTaskId);
        } else if (!isCompleted && currentStatus == TaskStatusEnum.COMPLETED) {
            // 使用系统用户作为操作人重新开始任务
            taskAgg.start("system", "系统");
            taskDomainService.update(taskAgg);
            log.info("任务状态同步为进行中，飞书任务ID：{}", feishuTaskId);
        }
    }

    /**
     * 删除飞书任务对应的本地任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByFeishuTaskId(String feishuTaskId) {
        log.info("删除飞书任务对应的本地任务，飞书任务ID：{}", feishuTaskId);

        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (taskAgg == null) {
            log.warn("未找到对应的本地任务，飞书任务ID：{}", feishuTaskId);
            return;
        }

        taskDomainService.deleteByFeishuTaskId(feishuTaskId);
        log.info("删除本地任务成功，飞书任务ID：{}", feishuTaskId);
    }
}

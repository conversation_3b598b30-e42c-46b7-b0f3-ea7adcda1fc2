package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Assistant
 * @description FMS任务类型枚举
 */
@Getter
@AllArgsConstructor
public enum FmsTaskTypeEnum {
    UPLOAD(1, "上传"),
    DOWNLOAD(2, "下载");
    
    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;
}

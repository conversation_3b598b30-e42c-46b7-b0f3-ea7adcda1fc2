package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 标签下会议类型占比DTO
 * @date 2025-01-24
 */
@Data
public class TagMeetingTypeRatioDTO {

    @ApiModelProperty("标签ID")
    private Long tagId;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("标签颜色")
    private String tagColor;

    @ApiModelProperty("临时会议占比（百分比）")
    private Double tempMeetingRatio;

    @ApiModelProperty("规划会议占比（百分比）")
    private Double plannedMeetingRatio;

    @ApiModelProperty("会议类型详情")
    private List<MeetingTypeDetailDTO> meetingTypeDetails;
}


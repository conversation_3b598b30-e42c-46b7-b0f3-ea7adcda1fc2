package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.CreateTaskCommand;
import cn.july.orch.meeting.domain.dto.FmsTaskDTO;
import cn.july.orch.meeting.domain.query.FmsTaskQuery;
import cn.july.orch.meeting.service.FmsTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 文件上传下载管理
 */
@Slf4j
@Api(tags = "文件上传下载管理")
@RestController
@RequestMapping("/fms")
public class FmsController {

    @Resource
    private FmsTaskService fmsTaskService;

    @PostMapping("/task/create")
    @ApiOperation(value = "创建异步任务")
    public Long createTask(@Validated @RequestBody CreateTaskCommand command) {
        return fmsTaskService.createTask(command);
    }

    @PostMapping("/task/page")
    @ApiOperation(value = "分页查询任务列表")
    public PageResultDTO<FmsTaskDTO> page(@RequestBody FmsTaskQuery query) {
        return fmsTaskService.page(query);
    }
}

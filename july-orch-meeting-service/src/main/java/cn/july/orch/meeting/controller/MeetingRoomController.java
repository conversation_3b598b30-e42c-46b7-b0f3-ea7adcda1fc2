package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomHistoryQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomHistoryDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomStatisticsQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomStatisticsDTO;
import cn.july.orch.meeting.service.MeetingRoomService;
import cn.july.orch.meeting.service.FeishuMeetingRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会议室控制器
 */
@Api(tags = "会议室管理")
@RestController
@RequestMapping("/meeting-room")
@RequiredArgsConstructor
public class MeetingRoomController {

    private final MeetingRoomService meetingRoomService;
    private final FeishuMeetingRoomService feishuMeetingRoomService;

    @PostMapping("/page")
    @ApiOperation("分页查询会议室")
    public PageResultDTO<MeetingRoomDTO> pageQuery(@Valid @RequestBody MeetingRoomQuery query) {
        return meetingRoomService.pageQuery(query);
    }

    @GetMapping("/list")
    @ApiOperation("查询所有会议室")
    public List<MeetingRoomDTO> listAll() {
        return meetingRoomService.listAll();
    }

    @PostMapping("/sync-from-feishu")
    @ApiOperation("从飞书同步会议室数据")
    public String syncFromFeishu() {
        try {
            feishuMeetingRoomService.syncMeetingRooms();
            return "飞书会议室数据同步成功";
        } catch (Exception e) {
            return "飞书会议室数据同步失败: " + e.getMessage();
        }
    }

    @PostMapping("/history")
    @ApiOperation("查询会议室历史会议记录")
    public PageResultDTO<MeetingRoomHistoryDTO> queryHistory(@Valid @RequestBody MeetingRoomHistoryQuery query) {
        return meetingRoomService.queryMeetingHistory(query);
    }

    @PostMapping("/statistics")
    @ApiOperation("查询会议室使用统计")
    public List<MeetingRoomStatisticsDTO> queryStatistics(@Valid @RequestBody MeetingRoomStatisticsQuery query) {
        return meetingRoomService.queryUsageStatistics(query);
    }
}
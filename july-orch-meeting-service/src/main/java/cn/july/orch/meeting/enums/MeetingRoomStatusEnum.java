package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 会议室状态枚举
 */
@Getter
public enum MeetingRoomStatusEnum {
    /**
     * 空闲中
     */
    FREE(0, "空闲中"),
    
    /**
     * 使用中
     */
    IN_USE(1, "使用中"),
    
    /**
     * 已预定
     */
    RESERVED(2, "已预定");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String description;

    MeetingRoomStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static MeetingRoomStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MeetingRoomStatusEnum status : MeetingRoomStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> Assistant
 * @description 会前文档信息DTO
 * @date 2025-08-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会前文档信息")
public class PreMeetingDocumentDTO {

    @ApiModelProperty("文件存储key")
    private String fileKey;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("AI汇总状态 (PENDING-待处理, SUCCESS-成功, FAILED-失败)")
    private String aiSummaryStatus;

    @ApiModelProperty("AI汇总内容")
    private String aiSummaryContent;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("文件临时访问链接")
    private String url;
}
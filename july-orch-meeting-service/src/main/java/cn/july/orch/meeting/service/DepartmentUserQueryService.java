package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 部门用户查询服务
 * 提供根据部门ID批量获取用户的功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DepartmentUserQueryService {

    private final TenantFeishuAppClient tenantFeishuAppClient;

    /**
     * 批量获取多个部门下的所有用户（包含子部门）
     * 
     * @param departmentIds 部门ID列表
     * @return Map<部门ID, 该部门及其所有子部门下的用户OpenID列表>
     */
    public Map<String, List<String>> getUsersByDepartments(List<String> departmentIds) {
        if (CollUtil.isEmpty(departmentIds)) {
            log.warn("部门ID列表为空");
            return new HashMap<>();
        }

        log.info("开始批量获取部门用户，部门数量: {}", departmentIds.size());
        
        try {
            // 调用飞书ContactService获取部门用户
            Map<String, List<String>> result = tenantFeishuAppClient.getContactService()
                    .getUsersByDepartments(departmentIds);
            
            log.info("批量获取部门用户完成，部门数量: {}, 结果数量: {}", 
                    departmentIds.size(), result.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量获取部门用户失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取单个部门下的所有用户（包含子部门）
     * 
     * @param departmentId 部门ID
     * @return 该部门及其所有子部门下的用户OpenID列表
     */
    public List<String> getUsersByDepartment(String departmentId) {
        if (departmentId == null || departmentId.trim().isEmpty()) {
            log.warn("部门ID为空");
            return new ArrayList<>();
        }

        log.info("开始获取部门用户，部门ID: {}", departmentId);
        
        Map<String, List<String>> result = getUsersByDepartments(Collections.singletonList(departmentId));
        
        return result.getOrDefault(departmentId, new ArrayList<>());
    }

    /**
     * 获取多个部门下所有用户的汇总列表（去重）
     * 
     * @param departmentIds 部门ID列表
     * @return 所有部门下的用户OpenID列表（已去重）
     */
    public List<String> getAllUsersFromDepartments(List<String> departmentIds) {
        if (CollUtil.isEmpty(departmentIds)) {
            log.warn("部门ID列表为空");
            return new ArrayList<>();
        }

        log.info("开始获取所有部门的汇总用户列表，部门数量: {}", departmentIds.size());
        
        Map<String, List<String>> result = getUsersByDepartments(departmentIds);
        
        // 汇总所有用户并去重
        Set<String> allUsers = new HashSet<>();
        result.values().forEach(allUsers::addAll);
        
        List<String> userList = new ArrayList<>(allUsers);
        
        log.info("获取部门汇总用户列表完成，总用户数: {}", userList.size());
        
        return userList;
    }
}

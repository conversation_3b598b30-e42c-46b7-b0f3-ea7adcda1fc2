package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.domain.entity.TaskActivityInfo;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务动态仓储接口
 */
public interface ITaskActivityRepository {

    /**
     * 插入任务动态
     *
     * @param taskActivityInfo 任务动态信息
     * @return 动态ID
     */
    Long insert(TaskActivityInfo taskActivityInfo);

    /**
     * 根据ID查询任务动态
     *
     * @param id 动态ID
     * @return 任务动态信息
     */
    TaskActivityInfo findById(Long id);

    /**
     * 根据任务ID查询动态列表
     *
     * @param taskId 任务ID
     * @return 动态列表
     */
    List<TaskActivityInfo> findByTaskId(Long taskId);

    /**
     * 根据任务ID查询最新的动态
     *
     * @param taskId 任务ID
     * @param limit 限制条数
     * @return 动态列表
     */
    List<TaskActivityInfo> findLatestByTaskId(Long taskId, Integer limit);

    /**
     * 根据动态类型查询动态列表
     *
     * @param activityType 动态类型
     * @return 动态列表
     */
    List<TaskActivityInfo> findByActivityType(TaskActivityTypeEnum activityType);

    /**
     * 根据时间范围查询动态列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 动态列表
     */
    List<TaskActivityInfo> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID查询动态列表
     *
     * @param userId 用户ID
     * @return 动态列表
     */
    List<TaskActivityInfo> findByUserId(String userId);

    /**
     * 统计任务动态数量
     *
     * @param taskId 任务ID
     * @return 动态数量
     */
    Long countByTaskId(Long taskId);

    /**
     * 删除任务动态
     *
     * @param taskActivityInfo 任务动态信息
     */
    void delete(TaskActivityInfo taskActivityInfo);

    /**
     * 批量删除任务动态
     *
     * @param taskId 任务ID
     */
    void deleteByTaskId(Long taskId);
}
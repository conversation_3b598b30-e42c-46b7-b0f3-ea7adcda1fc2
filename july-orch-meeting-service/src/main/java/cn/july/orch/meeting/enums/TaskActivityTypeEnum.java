package cn.july.orch.meeting.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Assistant
 * @description 任务动态类型枚举
 */
@Getter
@AllArgsConstructor
public enum TaskActivityTypeEnum {

    CREATE("CREATE", "创建任务"),
    UPDATE_STATUS("UPDATE_STATUS", "更新状态"),
    UPDATE_INFO("UPDATE_INFO", "更新信息"),
    ADD_COMMENT("ADD_COMMENT", "添加评论"),
    ADD_ATTACHMENT("ADD_ATTACHMENT", "添加附件"),
    REMOVE_ATTACHMENT("REMOVE_ATTACHMENT", "移除附件"),
    ASSIGN_USER("ASSIGN_USER", "分配用户"),
    SET_DUE_DATE("SET_DUE_DATE", "设置截止时间"),
    COMPLETE("COMPLETE", "完成任务"),
    START("START", "开始任务"),
    DECOMPOSE("DECOMPOSE", "拆解任务"),
    DELETE("DELETE", "删除任务"),
    SUPERVISE("SUPERVISE", "督办任务"),
    REMIND("REMIND", "提醒任务"),
    ESCALATE("ESCALATE", "升级督办"),
    OVERDUE("OVERDUE", "任务超期"),
    POSTPONE("POSTPONE", "延期任务");

    private final String code;
    private final String description;

    public static TaskActivityTypeEnum fromCode(String code) {
        for (TaskActivityTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown TaskActivityTypeEnum code: " + code);
    }
}
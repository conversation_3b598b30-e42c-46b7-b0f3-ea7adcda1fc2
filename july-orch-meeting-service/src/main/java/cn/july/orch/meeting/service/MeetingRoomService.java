package cn.july.orch.meeting.service;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomHistoryQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomHistoryDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomStatisticsQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomStatisticsDTO;
import java.util.List;

/**
 * 会议室服务接口
 */
public interface MeetingRoomService {

    /**
     * 同步飞书会议室数据
     */
    void syncMeetingRoomsFromFeishu();

    /**
     * 分页查询会议室
     * @param query 查询参数
     * @return 分页结果
     */
    PageResultDTO<MeetingRoomDTO> pageQuery(MeetingRoomQuery query);

    /**
     * 查询所有会议室
     * @return 会议室列表
     */
    List<MeetingRoomDTO> listAll();

    /**
     * 根据ID获取会议室详情
     * @param id 会议室ID
     * @return 会议室详情
     */
    MeetingRoomDTO getById(Long id);

    /**
     * 查询会议室的历史会议记录
     * @param query 查询参数
     * @return 分页结果
     */
    PageResultDTO<MeetingRoomHistoryDTO> queryMeetingHistory(MeetingRoomHistoryQuery query);

    /**
     * 查询会议室使用统计信息
     * @param query 统计查询参数
     * @return 统计结果
     */
    List<MeetingRoomStatisticsDTO> queryUsageStatistics(MeetingRoomStatisticsQuery query);
}
package cn.july.orch.meeting.assembler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.domain.po.MeetingRoomPO;
import cn.july.orch.meeting.domain.dto.MeetingRoomDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * 会议室转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingRoomAssembler {

    /**
     * PO转Entity（处理JSON字段）
     */
    default MeetingRoom toEntity(MeetingRoomPO po) {
        if (po == null) {
            return null;
        }
        MeetingRoom meetingRoom = new MeetingRoom();
        meetingRoom.setId(po.getId());
        meetingRoom.setFsRoomId(po.getFsRoomId());
        meetingRoom.setName(po.getName());
        meetingRoom.setCapacity(po.getCapacity());
        meetingRoom.setDescription(po.getDescription());
        meetingRoom.setStatus(po.getStatus());
        meetingRoom.setCreateTime(po.getCreateTime());
        meetingRoom.setUpdateTime(po.getUpdateTime());
        meetingRoom.setDeleted(po.getDeleted());
        
        if (StringUtils.hasText(po.getDevices())) {
            try {
                List<String> devices = JsonUtils.parseToList(po.getDevices(), String.class);
                meetingRoom.setDevices(devices);
            } catch (Exception e) {
                // JSON解析失败，设置为空列表
                meetingRoom.setDevices(Collections.emptyList());
            }
        }
        return meetingRoom;
    }

    /**
     * Entity转PO（处理JSON字段）
     */
    default MeetingRoomPO toPO(MeetingRoom meetingRoom) {
        if (meetingRoom == null) {
            return null;
        }
        MeetingRoomPO po = new MeetingRoomPO();
        po.setId(meetingRoom.getId());
        po.setFsRoomId(meetingRoom.getFsRoomId());
        po.setName(meetingRoom.getName());
        po.setCapacity(meetingRoom.getCapacity());
        po.setDescription(meetingRoom.getDescription());
        po.setStatus(meetingRoom.getStatus());
        po.setCreateTime(meetingRoom.getCreateTime());
        po.setUpdateTime(meetingRoom.getUpdateTime());
        po.setDeleted(meetingRoom.getDeleted());
        
        if (meetingRoom.getDevices() != null && !meetingRoom.getDevices().isEmpty()) {
            po.setDevices(JsonUtils.toJson(meetingRoom.getDevices()));
        }
        return po;
    }

    /**
     * Entity转DTO
     */
    MeetingRoomDTO toDTO(MeetingRoom meetingRoom);

    /**
     * PO列表转Entity列表
     */
    default List<MeetingRoom> toEntityList(List<MeetingRoomPO> pos) {
        if (pos == null) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::toEntity).collect(java.util.stream.Collectors.toList());
    }

    /**
     * Entity列表转DTO列表
     */
    List<MeetingRoomDTO> toDTOList(List<MeetingRoom> meetingRooms);
}
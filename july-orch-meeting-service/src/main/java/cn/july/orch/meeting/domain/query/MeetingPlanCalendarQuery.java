package cn.july.orch.meeting.domain.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划日历查询对象
 * @date 2025-01-24
 */
@Data
public class MeetingPlanCalendarQuery implements Serializable {

    @ApiModelProperty(value = "开始日期", required = true)
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束日期", required = true)
    private LocalDateTime endDate;

    @ApiModelProperty("关联的标签ID列表")
    private List<Long> tagIds;

    @ApiModelProperty("规划名称")
    private String name;

    @ApiModelProperty("部门ID列表（用于查询部门及其子部门）")
    private List<String> departmentIds;
}

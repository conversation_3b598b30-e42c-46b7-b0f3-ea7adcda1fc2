package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.TopicCreateCommand;
import cn.july.orch.meeting.domain.command.TopicUpdateCommand;
import cn.july.orch.meeting.domain.dto.TopicDTO;
import cn.july.orch.meeting.domain.entity.Topic;
import cn.july.orch.meeting.domain.po.TopicPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 议题对象转换器
 * @date 2025-11-06
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TopicAssembler {

    /**
     * PO转Entity
     */
    Topic toEntity(TopicPO po);

    /**
     * Entity转PO
     */
    TopicPO toPO(Topic entity);

    /**
     * Entity转DTO
     */
    TopicDTO toDTO(Topic entity);

    /**
     * CreateCommand转Entity
     */
    Topic toEntity(TopicCreateCommand command);

    /**
     * UpdateCommand转Entity
     */
    Topic toEntity(TopicUpdateCommand command);
}

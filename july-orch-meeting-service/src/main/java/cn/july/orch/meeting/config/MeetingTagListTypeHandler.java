package cn.july.orch.meeting.config;

import cn.july.orch.meeting.domain.dto.MeetingTagDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 支持JSR310的MeetingTagDTO列表类型处理器
 */
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class MeetingTagListTypeHandler extends BaseTypeHandler<List<MeetingTagDTO>> {

    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<MeetingTagDTO> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
                log.debug("序列化MeetingTagDTO列表成功，内容：{}", json);
            } catch (JsonProcessingException e) {
                log.error("MeetingTagDTO列表序列化失败", e);
                ps.setString(i, null);
            }
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public List<MeetingTagDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseMeetingTagList(value);
    }

    @Override
    public List<MeetingTagDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseMeetingTagList(value);
    }

    @Override
    public List<MeetingTagDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseMeetingTagList(value);
    }

    private List<MeetingTagDTO> parseMeetingTagList(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<MeetingTagDTO> result = objectMapper.readValue(value, new TypeReference<List<MeetingTagDTO>>() {});
            log.debug("反序列化MeetingTagDTO列表成功，内容：{}", result);
            return result != null ? result : new ArrayList<>();
        } catch (JsonProcessingException e) {
            log.error("MeetingTagDTO列表反序列化失败，原始值：{}", value, e);
            return new ArrayList<>();
        }
    }
}
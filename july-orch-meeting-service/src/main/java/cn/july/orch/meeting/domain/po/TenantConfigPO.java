package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.TenantStatusEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 租户配置PO类
 */
@Data
@Accessors(chain = true)
@TableName("tenant_config")
public class TenantConfigPO {

    /**
     * 租户ID（主键）
     */
    @TableId
    private Long tenantId;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 飞书应用ID
     */
    @TableField("feishu_app_id")
    private String feishuAppId;

    /**
     * 飞书应用密钥
     */
    @TableField("feishu_app_secret")
    private String feishuAppSecret;

    /**
     * 租户状态(0-禁用,1-启用)
     */
    @TableField("status")
    private TenantStatusEnum status;

    /**
     * 租户描述
     */
    @TableField("description")
    private String description;

    /**
     * 联系人邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;
}

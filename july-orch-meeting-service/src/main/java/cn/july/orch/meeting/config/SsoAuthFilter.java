package cn.july.orch.meeting.config;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.july.core.context.BaseRequestContext;
import cn.july.feishu.config.FeishuAppContext;
import cn.july.orch.meeting.common.Constants;
import cn.july.orch.meeting.exception.AuthException;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.properties.MeetingSeverProperties;
import cn.july.orch.meeting.service.SsoActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

import cn.july.orch.meeting.config.UserInfoDTO;

@Component
@Slf4j
public class SsoAuthFilter implements HandlerInterceptor, Ordered {

    public static final String TOKEN = "token";

    private final PathMatcher pathMatcher = new AntPathMatcher();

    @Resource
    private MeetingSeverProperties meetingSeverProperties;
    @Resource
    private SsoActionService ssoActionService;

    @Value(value = "${server.servlet.context-path}")
    private String contextPath;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!meetingSeverProperties.getPermission().isEnable()) {
            return true;
        }
        String token = request.getHeader(TOKEN);
        String requestUri = request.getRequestURI();

        try {
            // 无需拦截的url
            if (this.filterIgnoreAccess(requestUri)) {
                log.debug("SsoAuthFilter ignore uri auth requestUri={}", requestUri);
                return true;
            }
            if (StrUtil.isBlank(token)) {
                throw new AuthException(MessageCode.AUTH_FAIL);
            }
            UserInfoDTO userInfoDTO = ssoActionService.getCacheUserInfo(token);
            log.debug("SsoAuthFilter userInfo from token dto={}", JSONUtil.toJsonStr(userInfoDTO));
            if (Objects.isNull(userInfoDTO)) {
                throw new AuthException(MessageCode.TOKEN_INVALID);
            }
            CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.set(userInfoDTO);
            // core-上下文
            BaseRequestContext.putAttachment(Constants.OPEN_ID,userInfoDTO.getOpenId());
            BaseRequestContext.putAttachment(Constants.USER_NAME,userInfoDTO.getName());
            
            // 设置BaseRequestContext中的租户ID，供TenantContextFilter使用
            if (userInfoDTO.getTenantId() != null) {
                BaseRequestContext.get().setTenantId(userInfoDTO.getTenantId());
            }

            FeishuAppContext context = new FeishuAppContext();
            context.setUserAccessToken(userInfoDTO.getUserAccessToken());
            context.setOpenId(userInfoDTO.getOpenId());
            FeishuAppContext.set(context);
        } catch (AuthException ex) {
            log.warn("SsoAuthFilter error ", ex);
            throw ex;
        } catch (Exception ex) {
            log.error("SsoAuthFilter error ", ex);
            throw new AuthException(MessageCode.LOGIN_ERROR.getCode(), "鉴权过滤器异常: " + ex.getMessage());
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.remove();
        // 不再清除租户上下文，由TenantContextFilter处理
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    @Override
    public int getOrder() {
        return -99;
    }

    private boolean filterIgnoreAccess(String requestUri) {
        String requestUriSub = "";
        if (requestUri.startsWith(contextPath)) {
            requestUriSub = requestUri.substring(contextPath.length());
        }
        for (String ignoreUrl : meetingSeverProperties.getPermission().getDefaultUris()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        for (String ignoreUrl : meetingSeverProperties.getPermission().getExcludePatterns()) {
            if (pathMatcher.match(ignoreUrl, requestUri) || pathMatcher.match(ignoreUrl, requestUriSub)) {
                return true;
            }
        }
        return false;
    }

}
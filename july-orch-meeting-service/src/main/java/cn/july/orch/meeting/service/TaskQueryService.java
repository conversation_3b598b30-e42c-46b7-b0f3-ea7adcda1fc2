package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.ddd.IdQuery;
import cn.july.core.model.enums.DeletedEnum;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.TaskAssembler;
import cn.july.orch.meeting.assembler.TaskConverter;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.po.TaskListPO;
import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.domain.query.TaskQuery;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.TaskListMapper;
import cn.july.orch.meeting.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务查询服务
 */
@Slf4j
@Service
public class TaskQueryService {

    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private TaskAssembler taskAssembler;
    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskConverter taskConverter;
    @Resource
    private TaskActivityQueryService taskActivityQueryService;
    @Resource
    private TaskListMapper taskListMapper;

    /**
     * 分页查询任务列表
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListDTO> page(TaskQuery taskQuery) {
        Page<TaskPO> page = new Page<>(taskQuery.getPageNo(), taskQuery.getPageSize());

        // 如果有会议标签筛选条件，使用专门的查询方法
        if (taskQuery.getMeetingTagIds() != null && !taskQuery.getMeetingTagIds().isEmpty()) {
            return pageByMeetingTags(taskQuery);
        }

        LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();

        // 逻辑删除条件
        queryWrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);

        // 标题模糊查询
        if (StringUtils.hasText(taskQuery.getTitle())) {
            queryWrapper.like(TaskPO::getTitle, taskQuery.getTitle());
        }

        // 负责人查询
        if (StringUtils.hasText(taskQuery.getOwnerOpenId())) {
            queryWrapper.eq(TaskPO::getOwnerOpenId, taskQuery.getOwnerOpenId());
        }

        // 负责人名称模糊查询
        if (StringUtils.hasText(taskQuery.getOwnerName())) {
            queryWrapper.like(TaskPO::getOwnerName, taskQuery.getOwnerName());
        }

        // 优先级查询
        if (taskQuery.getPriority() != null) {
            queryWrapper.eq(TaskPO::getPriority, taskQuery.getPriority());
        }

        // 状态查询
        if (taskQuery.getStatus() != null) {
            queryWrapper.eq(TaskPO::getStatus, taskQuery.getStatus());
        }

        // 会议ID查询
        if (taskQuery.getMeetingId() != null) {
            queryWrapper.eq(TaskPO::getMeetingId, taskQuery.getMeetingId());
        }

        // 截止时间范围查询
        if (taskQuery.getDueDateStart() != null) {
            queryWrapper.ge(TaskPO::getDueDate, taskQuery.getDueDateStart());
        }
        if (taskQuery.getDueDateEnd() != null) {
            queryWrapper.le(TaskPO::getDueDate, taskQuery.getDueDateEnd());
        }

        // 创建时间范围查询
        if (taskQuery.getCreateTimeStart() != null) {
            queryWrapper.ge(TaskPO::getCreateTime, taskQuery.getCreateTimeStart());
        }
        if (taskQuery.getCreateTimeEnd() != null) {
            queryWrapper.le(TaskPO::getCreateTime, taskQuery.getCreateTimeEnd());
        }
        if (taskQuery.getTaskListId() != null) {
            queryWrapper.eq(TaskPO::getTaskListId, taskQuery.getTaskListId());
        }

        // 只查询超期任务
        if (Boolean.TRUE.equals(taskQuery.getOnlyOverdue())) {
            queryWrapper.lt(TaskPO::getDueDate, LocalDateTime.now())
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED);
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(TaskPO::getCreateTime);

        IPage<TaskPO> pageResult = taskMapper.selectPage(page, queryWrapper);

        List<TaskListDTO> taskListDTOList = taskConverter.toTaskListDTOList(
            taskAssembler.toTaskInfoList(pageResult.getRecords()));

        return new PageResultDTO<>(taskQuery.getPageNo(), taskQuery.getPageSize(), pageResult.getSize(), taskListDTOList);
    }

    /**
     * 根据会议标签筛选任务列表（分页）
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    private PageResultDTO<TaskListDTO> pageByMeetingTags(TaskQuery taskQuery) {
        Page<TaskPO> page = new Page<>(taskQuery.getPageNo(), taskQuery.getPageSize());

        // 调用自定义SQL查询
        IPage<TaskPO> pageResult = taskMapper.selectTasksByMeetingTags(
            page,
            taskQuery.getMeetingTagIds(),
            taskQuery.getTitle(),
            taskQuery.getOwnerOpenId(),
            taskQuery.getOwnerName(),
            taskQuery.getPriority() != null ? taskQuery.getPriority().getCode() : null,
            taskQuery.getStatus() != null ? taskQuery.getStatus().getCode() : null,
            taskQuery.getMeetingId(),
            taskQuery.getTaskListId(),
            taskQuery.getDueDateStart(),
            taskQuery.getDueDateEnd(),
            taskQuery.getCreateTimeStart(),
            taskQuery.getCreateTimeEnd(),
            taskQuery.getOnlyOverdue()
        );

        List<TaskListDTO> taskListDTOList = taskConverter.toTaskListDTOList(
            taskAssembler.toTaskInfoList(pageResult.getRecords()));

        return new PageResultDTO<>(taskQuery.getPageNo(), taskQuery.getPageSize(), pageResult.getTotal(), taskListDTOList);
    }

    /**
     * 根据ID查询任务详情
     *
     * @param idQuery ID查询
     * @return 任务详情
     */
    public TaskDTO detail(IdQuery idQuery) {
        TaskAgg taskAgg = taskDomainService.findById(idQuery.getId());
        TaskDTO taskDTO = taskConverter.toTaskDTO(taskAgg);
        
        // 获取任务清单的名称和描述
        if (taskDTO != null && taskDTO.getTaskListId() != null) {
            try {
                // 直接查询任务清单表获取清单信息
                TaskListPO taskListPO = taskListMapper.selectById(taskDTO.getTaskListId());
                if (taskListPO != null) {
                    taskDTO.setTaskListName(taskListPO.getName());
                    taskDTO.setTaskListDescription(taskListPO.getDescription());
                    log.debug("任务清单信息设置成功，清单ID：{}，名称：{}", 
                        taskDTO.getTaskListId(), taskDTO.getTaskListName());
                }
            } catch (Exception e) {
                log.warn("获取任务清单信息失败，清单ID：{}", taskDTO.getTaskListId(), e);
                // 异常不影响主流程返回
            }
        }
        
        return taskDTO;
    }

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务列表
     */
    public List<TaskListDTO> listByMeetingId(Long meetingId) {
        List<TaskAgg> taskAggList = taskDomainService.findByMeetingId(meetingId);
        return taskConverter.toTaskListDTOListFromAgg(taskAggList);
    }

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务列表
     */
    public List<TaskListDTO> listByOwner(String ownerOpenId) {
        List<TaskAgg> taskAggList = taskDomainService.findByOwner(ownerOpenId);
        return taskConverter.toTaskListDTOListFromAgg(taskAggList);
    }

    /**
     * 查询我的任务列表
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListDTO> myTasks(TaskQuery taskQuery) {
        // 这里需要从当前用户上下文获取用户OpenID
        // taskQuery.setOwnerOpenId(getCurrentUserOpenId());
        return page(taskQuery);
    }

    /**
     * 获取任务统计信息
     *
     * @param taskListId 任务清单ID（可选）
     * @return 任务统计DTO
     */
    public TaskStatusStatisticsDTO getTaskStatistics(Long taskListId) {
        log.info("开始获取任务统计信息，任务清单ID：{}", taskListId);

        try {
            // 获取总任务数（排除逻辑删除）
            LambdaQueryWrapper<TaskPO> totalQueryWrapper = new LambdaQueryWrapper<>();
            totalQueryWrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                totalQueryWrapper.eq(TaskPO::getTaskListId, taskListId);
            }
            Long totalTasks = taskMapper.selectCount(totalQueryWrapper);

            // 获取各状态任务数（排除逻辑删除）
            LambdaQueryWrapper<TaskPO> notStartedQuery = new LambdaQueryWrapper<>();
            notStartedQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                notStartedQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            notStartedQuery.eq(TaskPO::getStatus, TaskStatusEnum.NOT_STARTED.getCode());
            Long notStartedTasks = taskMapper.selectCount(notStartedQuery);
            
            LambdaQueryWrapper<TaskPO> inProgressQuery = new LambdaQueryWrapper<>();
            inProgressQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                inProgressQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            inProgressQuery.eq(TaskPO::getStatus, TaskStatusEnum.IN_PROGRESS.getCode());
            Long inProgressTasks = taskMapper.selectCount(inProgressQuery);
            
            LambdaQueryWrapper<TaskPO> completedQuery = new LambdaQueryWrapper<>();
            completedQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                completedQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            completedQuery.eq(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode());
            Long completedTasks = taskMapper.selectCount(completedQuery);

            // 获取超期任务数（截止时间小于当前时间且未完成，排除逻辑删除）
            LambdaQueryWrapper<TaskPO> overdueQuery = new LambdaQueryWrapper<>();
            overdueQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                overdueQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            overdueQuery.lt(TaskPO::getDueDate, LocalDateTime.now())
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode());
            Long overdueTasks = taskMapper.selectCount(overdueQuery);

            // 获取今日到期任务数（排除逻辑删除）
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            LambdaQueryWrapper<TaskPO> todayDueQuery = new LambdaQueryWrapper<>();
            todayDueQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                todayDueQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            todayDueQuery.between(TaskPO::getDueDate, todayStart, todayEnd)
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode());
            Long todayDueTasks = taskMapper.selectCount(todayDueQuery);

            // 获取本周到期任务数（排除逻辑删除）
            LocalDateTime weekStart = LocalDateTime.now().with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime weekEnd = LocalDateTime.now().with(DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59);
            LambdaQueryWrapper<TaskPO> weekDueQuery = new LambdaQueryWrapper<>();
            weekDueQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                weekDueQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            weekDueQuery.between(TaskPO::getDueDate, weekStart, weekEnd)
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode());
            Long weekDueTasks = taskMapper.selectCount(weekDueQuery);

            // 获取高优先级任务数（排除逻辑删除）
            LambdaQueryWrapper<TaskPO> highPriorityQuery = new LambdaQueryWrapper<>();
            highPriorityQuery.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            if (taskListId != null) {
                highPriorityQuery.eq(TaskPO::getTaskListId, taskListId);
            }
            highPriorityQuery.eq(TaskPO::getPriority, TaskPriorityEnum.HIGH.getCode());
            Long highPriorityTasks = taskMapper.selectCount(highPriorityQuery);

            // 计算完成率和超期率
            Double completionRate = totalTasks > 0 ? (completedTasks.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;
            Double overdueRate = totalTasks > 0 ? (overdueTasks.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            // 获取各状态统计详情
            List<TaskStatusStatisticsDTO.TaskStatusStatistics> statusStatistics = buildStatusStatistics(totalTasks, taskListId);

            // 获取各优先级统计详情
            List<TaskStatusStatisticsDTO.TaskPriorityStatistics> priorityStatistics = buildPriorityStatistics(totalTasks, taskListId);

            TaskStatusStatisticsDTO statistics = TaskStatusStatisticsDTO.builder()
                .totalTasks(totalTasks)
                .notStartedTasks(notStartedTasks)
                .inProgressTasks(inProgressTasks)
                .completedTasks(completedTasks)
                .overdueTasks(overdueTasks)
                .todayDueTasks(todayDueTasks)
                .weekDueTasks(weekDueTasks)
                .highPriorityTasks(highPriorityTasks)
                .completionRate(Math.round(completionRate * 100.0) / 100.0)
                .overdueRate(Math.round(overdueRate * 100.0) / 100.0)
                .statusStatistics(statusStatistics)
                .priorityStatistics(priorityStatistics)
                .build();

            log.info("任务统计信息获取成功：总任务数={}, 完成率={}%, 超期率={}%, 任务清单ID={}",
                totalTasks, statistics.getCompletionRate(), statistics.getOverdueRate(), taskListId);

            return statistics;

        } catch (Exception e) {
            log.error("获取任务统计信息失败，任务清单ID：{}", taskListId, e);
            throw new BusinessException("获取任务统计信息失败");
        }
    }

    /**
     * 获取任务统计信息（无筛选条件）
     *
     * @return 任务统计DTO
     */
    public TaskStatusStatisticsDTO getTaskStatistics() {
        return getTaskStatistics(null);
    }

    /**
     * 获取需要督办的任务列表
     *
     * @return 督办任务列表
     */
    public List<TaskSupervisionDTO> getTasksForSupervision() {
        log.info("开始获取需要督办的任务列表");

        try {
            // 获取距离截止时间3小时内且未完成的任务
            LocalDateTime threeHoursLater = LocalDateTime.now().plusHours(3);

            List<TaskPO> urgentTasks = taskMapper.selectList(
                new LambdaQueryWrapper<TaskPO>()
                    .le(TaskPO::getDueDate, threeHoursLater)
                    .ge(TaskPO::getDueDate, LocalDateTime.now())
                    .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED)
                    .orderByAsc(TaskPO::getDueDate));

            List<TaskSupervisionDTO> supervisionTasks = urgentTasks.stream()
                .map(this::buildTaskSupervisionDTO)
                .collect(Collectors.toList());

            log.info("获取到需要督办的任务数量：{}", supervisionTasks.size());

            return supervisionTasks;

        } catch (Exception e) {
            log.error("获取督办任务列表失败", e);
            throw new BusinessException("获取督办任务列表失败");
        }
    }

    /**
     * 根据任务ID获取督办信息
     *
     * @param taskId 任务ID
     * @return 督办任务信息
     */
    public TaskSupervisionDTO getTaskSupervisionById(Long taskId) {
        log.info("获取任务督办信息，taskId: {}", taskId);

        TaskPO taskPO = taskMapper.selectById(taskId);
        if (taskPO == null) {
            throw new BusinessException("任务不存在，taskId: " + taskId);
        }

        return buildTaskSupervisionDTO(taskPO);
    }

    /**
     * 嵌套分页查询任务列表（仅显示一级任务，子任务作为嵌套结构）
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListDTO> nestedPage(TaskQuery taskQuery) {
        log.info("开始嵌套分页查询任务列表");
        
        Page<TaskPO> page = new Page<>(taskQuery.getPageNo(), taskQuery.getPageSize());

        IPage<TaskPO> pageResult;
        
        // 如果有会议标签筛选条件，使用专门的查询方法
        if (taskQuery.getMeetingTagIds() != null && !taskQuery.getMeetingTagIds().isEmpty()) {
            // 调用自定义SQL查询，但只返回父任务
            pageResult = taskMapper.selectTasksByMeetingTags(
                page,
                taskQuery.getMeetingTagIds(),
                taskQuery.getTitle(),
                taskQuery.getOwnerOpenId(),
                taskQuery.getOwnerName(),
                taskQuery.getPriority() != null ? taskQuery.getPriority().getCode() : null,
                taskQuery.getStatus() != null ? taskQuery.getStatus().getCode() : null,
                taskQuery.getMeetingId(),
                taskQuery.getTaskListId(),
                taskQuery.getDueDateStart(),
                taskQuery.getDueDateEnd(),
                taskQuery.getCreateTimeStart(),
                taskQuery.getCreateTimeEnd(),
                taskQuery.getOnlyOverdue()
            );
            // 过滤出父任务（parent_id为null）
            List<TaskPO> parentTasks = pageResult.getRecords().stream()
                .filter(task -> task.getParentId() == null)
                .collect(Collectors.toList());
            // 重新设置分页结果
            pageResult.setRecords(parentTasks);
        } else {
            LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();

            // 逻辑删除条件
            queryWrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            
            // 只查询父任务（parent_id为null）
            queryWrapper.isNull(TaskPO::getParentId);

            // 添加其他查询条件
            buildCommonQueryConditions(queryWrapper, taskQuery);

            // 按创建时间倒序
            queryWrapper.orderByDesc(TaskPO::getCreateTime);

            pageResult = taskMapper.selectPage(page, queryWrapper);
        }

        // 转换为TaskListDTO并构建嵌套结构
        List<TaskListDTO> taskListDTOList = buildNestedTaskListDTO(pageResult.getRecords());

        return new PageResultDTO<>(taskQuery.getPageNo(), taskQuery.getPageSize(), pageResult.getSize(), taskListDTOList);
    }

    /**
     * 查询任务详情（包含子任务完成情况和任务动态）
     *
     * @param idQuery ID查询
     * @return 任务详情
     */
    public TaskDTO detailWithActivities(IdQuery idQuery) {
        log.info("查询任务详情（包含动态），taskId: {}", idQuery.getId());

        // 查询任务基础信息
        TaskAgg taskAgg = taskDomainService.findById(idQuery.getId());
        if (taskAgg == null || taskAgg.getInfo() == null) {
            throw new BusinessException("任务不存在，taskId: " + idQuery.getId());
        }

        TaskDTO taskDTO = taskConverter.toTaskDTO(taskAgg);

        // 获取任务清单的名称和描述
        if (taskDTO != null && taskDTO.getTaskListId() != null) {
            try {
                // 直接查询任务清单表获取清单信息
                TaskListPO taskListPO = taskListMapper.selectById(taskDTO.getTaskListId());
                if (taskListPO != null) {
                    taskDTO.setTaskListName(taskListPO.getName());
                    taskDTO.setTaskListDescription(taskListPO.getDescription());
                    log.debug("任务清单信息设置成功，清单ID：{}，名称：{}", 
                        taskDTO.getTaskListId(), taskDTO.getTaskListName());
                }
            } catch (Exception e) {
                log.warn("获取任务清单信息失败，清单ID：{}", taskDTO.getTaskListId(), e);
                // 异常不影响主流程返回
            }
        }

        // 查询子任务完成情况
        List<TaskPO> subTasks = taskMapper.selectList(
            new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getParentId, idQuery.getId())
                .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(TaskPO::getCreateTime)
        );

        if (!subTasks.isEmpty()) {
            List<TaskListDTO> subTaskDTOList = taskConverter.toTaskListDTOList(
                taskAssembler.toTaskInfoList(subTasks)
            );
            
            // 计算子任务完成情况统计
            long completedCount = subTasks.stream()
                .mapToLong(task -> TaskStatusEnum.COMPLETED.equals(task.getStatus()) ? 1 : 0)
                .sum();
            String subTaskSummary = completedCount + "/" + subTasks.size();
            
            // 设置子任务信息
            taskDTO.setSubTasks(subTaskDTOList);
            taskDTO.setSubTaskSummary(subTaskSummary);
            taskDTO.setSubTaskTotal(subTasks.size());
            taskDTO.setSubTaskCompleted((int) completedCount);
        }

        // 查询任务动态信息
        try {
            // 获取任务所有动态
            List<TaskActivityDTO> activities = taskActivityQueryService.getByTaskId(idQuery.getId());
            
            // 设置动态信息
            taskDTO.setActivities(activities);
            taskDTO.setActivityCount(activities != null ? activities.size() : 0);
            
            // 获取并设置最新动态
            TaskActivityDTO latestActivity = taskActivityQueryService.getLatestByTaskId(idQuery.getId());
            taskDTO.setLatestActivity(latestActivity);
            
            log.info("任务动态查询成功，任务ID：{}，动态数量：{}", 
                idQuery.getId(), activities != null ? activities.size() : 0);
        } catch (Exception e) {
            log.warn("查询任务动态失败，taskId：{}", idQuery.getId(), e);
            // 设置默认值，避免NPE
            taskDTO.setActivities(new ArrayList<>());
            taskDTO.setActivityCount(0);
            taskDTO.setLatestActivity(null);
        }

        return taskDTO;
    }

    /**
     * 构建状态统计详情（无筛选条件）
     */
    private List<TaskStatusStatisticsDTO.TaskStatusStatistics> buildStatusStatistics(Long totalTasks) {
        return buildStatusStatistics(totalTasks, null);
    }

    /**
     * 构建状态统计详情
     */
    private List<TaskStatusStatisticsDTO.TaskStatusStatistics> buildStatusStatistics(Long totalTasks, Long taskListId) {
        List<TaskStatusStatisticsDTO.TaskStatusStatistics> statusStatistics = new ArrayList<>();

        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getStatus, status.getCode())
                .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            
            // 如果指定了任务清单ID，则添加筛选条件
            if (taskListId != null) {
                queryWrapper.eq(TaskPO::getTaskListId, taskListId);
            }
            
            Long count = taskMapper.selectCount(queryWrapper);
            Double percentage = totalTasks > 0 ? (count.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            statusStatistics.add(TaskStatusStatisticsDTO.TaskStatusStatistics.builder()
                .statusCode(status.getCode())
                .statusName(status.getDesc())
                .count(count)
                .percentage(Math.round(percentage * 100.0) / 100.0)
                .build());
        }

        return statusStatistics;
    }

    /**
     * 构建优先级统计详情（无筛选条件）
     */
    private List<TaskStatusStatisticsDTO.TaskPriorityStatistics> buildPriorityStatistics(Long totalTasks) {
        return buildPriorityStatistics(totalTasks, null);
    }

    /**
     * 构建优先级统计详情
     */
    private List<TaskStatusStatisticsDTO.TaskPriorityStatistics> buildPriorityStatistics(Long totalTasks, Long taskListId) {
        List<TaskStatusStatisticsDTO.TaskPriorityStatistics> priorityStatistics = new ArrayList<>();

        for (TaskPriorityEnum priority : TaskPriorityEnum.values()) {
            LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<TaskPO>()
                .eq(TaskPO::getPriority, priority.getCode())
                .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            
            // 如果指定了任务清单ID，则添加筛选条件
            if (taskListId != null) {
                queryWrapper.eq(TaskPO::getTaskListId, taskListId);
            }
            
            Long count = taskMapper.selectCount(queryWrapper);
            Double percentage = totalTasks > 0 ? (count.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            priorityStatistics.add(TaskStatusStatisticsDTO.TaskPriorityStatistics.builder()
                .priorityCode(priority.getCode())
                .priorityName(priority.getDesc())
                .count(count)
                .percentage(Math.round(percentage * 100.0) / 100.0)
                .build());
        }

        return priorityStatistics;
    }

    /**
     * 构建任务督办DTO
     */
    private TaskSupervisionDTO buildTaskSupervisionDTO(TaskPO taskPO) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dueDate = taskPO.getDueDate();

        // 计算剩余时间（小时）
        Long remainingHours = dueDate != null ?
            Duration.between(now, dueDate).toHours() : null;

        // 判断是否紧急（3小时内）
        Boolean isUrgent = remainingHours != null && remainingHours <= 3 && remainingHours >= 0;

        return TaskSupervisionDTO.builder()
            .taskId(taskPO.getId())
            .title(taskPO.getTitle())
            .description(taskPO.getDescription())
            .ownerOpenId(taskPO.getOwnerOpenId())
            .ownerName(taskPO.getOwnerName())
            .priority(taskPO.getPriority().getCode())
            .priorityName(TaskPriorityEnum.getByCode(taskPO.getPriority().getCode()) != null ?
                TaskPriorityEnum.getByCode(taskPO.getPriority().getCode()).getDesc() : "未知")
            .status(taskPO.getStatus().getCode())
            .statusName(TaskStatusEnum.getByCode(taskPO.getStatus().getCode()) != null ?
                TaskStatusEnum.getByCode(taskPO.getStatus().getCode()).getDesc() : "未知")
            .dueDate(taskPO.getDueDate())
            .remainingHours(remainingHours)
            .isUrgent(isUrgent)
            .meetingId(taskPO.getMeetingId())
            .createTime(taskPO.getCreateTime())
            .build();
    }

    /**
     * 构建通用查询条件
     */
    private void buildCommonQueryConditions(LambdaQueryWrapper<TaskPO> queryWrapper, TaskQuery taskQuery) {
        // 标题模糊查询
        if (StringUtils.hasText(taskQuery.getTitle())) {
            queryWrapper.like(TaskPO::getTitle, taskQuery.getTitle());
        }

        // 负责人查询
        if (StringUtils.hasText(taskQuery.getOwnerOpenId())) {
            queryWrapper.eq(TaskPO::getOwnerOpenId, taskQuery.getOwnerOpenId());
        }

        // 负责人名称模糊查询
        if (StringUtils.hasText(taskQuery.getOwnerName())) {
            queryWrapper.like(TaskPO::getOwnerName, taskQuery.getOwnerName());
        }

        // 优先级查询
        if (taskQuery.getPriority() != null) {
            queryWrapper.eq(TaskPO::getPriority, taskQuery.getPriority());
        }

        // 状态查询
        if (taskQuery.getStatus() != null) {
            queryWrapper.eq(TaskPO::getStatus, taskQuery.getStatus());
        }

        // 会议ID查询
        if (taskQuery.getMeetingId() != null) {
            queryWrapper.eq(TaskPO::getMeetingId, taskQuery.getMeetingId());
        }

        // 任务清单ID查询
        if (taskQuery.getTaskListId() != null) {
            queryWrapper.eq(TaskPO::getTaskListId, taskQuery.getTaskListId());
        }

        // 截止时间范围查询
        if (taskQuery.getDueDateStart() != null) {
            queryWrapper.ge(TaskPO::getDueDate, taskQuery.getDueDateStart());
        }
        if (taskQuery.getDueDateEnd() != null) {
            queryWrapper.le(TaskPO::getDueDate, taskQuery.getDueDateEnd());
        }

        // 创建时间范围查询
        if (taskQuery.getCreateTimeStart() != null) {
            queryWrapper.ge(TaskPO::getCreateTime, taskQuery.getCreateTimeStart());
        }
        if (taskQuery.getCreateTimeEnd() != null) {
            queryWrapper.le(TaskPO::getCreateTime, taskQuery.getCreateTimeEnd());
        }

        // 只查询超期任务
        if (Boolean.TRUE.equals(taskQuery.getOnlyOverdue())) {
            queryWrapper.lt(TaskPO::getDueDate, LocalDateTime.now())
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED);
        }
    }

    /**
     * 构建嵌套任务列表DTO
     */
    private List<TaskListDTO> buildNestedTaskListDTO(List<TaskPO> parentTasks) {
        if (parentTasks.isEmpty()) {
            return new ArrayList<>();
        }

        List<TaskListDTO> result = new ArrayList<>();
        
        // 批量查询所有子任务
        List<Long> parentTaskIds = parentTasks.stream()
            .map(TaskPO::getId)
            .collect(Collectors.toList());
        
        List<TaskPO> allSubTasks = taskMapper.selectList(
            new LambdaQueryWrapper<TaskPO>()
                .in(TaskPO::getParentId, parentTaskIds)
                .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED)
                .orderByAsc(TaskPO::getCreateTime)
        );
        
        // 按父任务ID分组
        Map<Long, List<TaskPO>> subTasksMap = allSubTasks.stream()
            .collect(Collectors.groupingBy(TaskPO::getParentId));

        for (TaskPO parentTask : parentTasks) {
            TaskListDTO parentTaskDTO = taskConverter.toTaskListDTO(
                taskAssembler.toTaskInfo(parentTask)
            );

            // 获取子任务列表
            List<TaskPO> subTasks = subTasksMap.getOrDefault(parentTask.getId(), new ArrayList<>());
            if (!subTasks.isEmpty()) {
                List<TaskListDTO> subTaskDTOList = taskConverter.toTaskListDTOList(
                    taskAssembler.toTaskInfoList(subTasks)
                );
                
                // 计算子任务完成情况
                long completedCount = subTasks.stream()
                    .mapToLong(task -> TaskStatusEnum.COMPLETED.equals(task.getStatus()) ? 1 : 0)
                    .sum();
                String subTaskSummary = completedCount + "/" + subTasks.size();
                
                parentTaskDTO.setSubTasks(subTaskDTOList);
                parentTaskDTO.setSubTaskSummary(subTaskSummary);
            } else {
                parentTaskDTO.setSubTasks(new ArrayList<>());
                parentTaskDTO.setSubTaskSummary("0/0");
            }

            result.add(parentTaskDTO);
        }

        return result;
    }
}

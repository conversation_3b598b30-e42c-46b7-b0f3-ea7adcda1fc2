package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;
import cn.july.orch.meeting.repository.MeetingRoomRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Assistant
 * @description 会议室状态服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingRoomStatusServiceImpl implements MeetingRoomStatusService {
    
    private final MeetingRoomRepository meetingRoomRepository;
    
    @Override
    public void updateMeetingRoomStatus(Long meetingRoomId, MeetingRoomStatusEnum status) {
        if (meetingRoomId == null) {
            return;
        }
        
        try {
            MeetingRoom meetingRoom = meetingRoomRepository.findById(meetingRoomId);
            if (meetingRoom != null) {
                meetingRoom.setStatus(status);
                meetingRoomRepository.save(meetingRoom);
                log.info("更新会议室状态成功，会议室ID：{}，状态：{}", meetingRoomId, status);
            } else {
                log.warn("未找到会议室，会议室ID：{}", meetingRoomId);
            }
        } catch (Exception e) {
            log.error("更新会议室状态失败，会议室ID：{}，状态：{}", meetingRoomId, status, e);
        }
    }
    
    @Override
    public boolean isMeetingRoomFree(Long meetingRoomId) {
        if (meetingRoomId == null) {
            return true;
        }
        
        try {
            MeetingRoom meetingRoom = meetingRoomRepository.findById(meetingRoomId);
            if (meetingRoom != null) {
                return meetingRoom.getStatus() == MeetingRoomStatusEnum.FREE;
            }
        } catch (Exception e) {
            log.error("检查会议室状态失败，会议室ID：{}", meetingRoomId, e);
        }
        
        // 如果出现异常，默认认为会议室是空闲的
        return true;
    }
}
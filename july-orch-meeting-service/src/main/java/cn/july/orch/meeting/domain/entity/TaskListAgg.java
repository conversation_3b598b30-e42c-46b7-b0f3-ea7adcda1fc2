package cn.july.orch.meeting.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Assistant
 * @description 任务清单聚合根
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskListAgg {

    /**
     * 任务清单信息
     */
    private TaskListInfo info;

    /**
     * 创建任务清单聚合
     */
    public static TaskListAgg create(String name, String description, Long parentId) {
        TaskListInfo taskListInfo = TaskListInfo.create(name, description, parentId);
        return TaskListAgg.builder()
                .info(taskListInfo)
                .build();
    }

    /**
     * 创建任务清单聚合（无父清单）
     */
    public static TaskListAgg create(String name, String description) {
        return create(name, description, null);
    }

    /**
     * 更新任务清单信息
     */
    public void update(String name, String description, Long parentId) {
        this.info.update(name, description, parentId);
    }

    /**
     * 删除任务清单
     */
    public void delete() {
        this.info.delete();
    }
}
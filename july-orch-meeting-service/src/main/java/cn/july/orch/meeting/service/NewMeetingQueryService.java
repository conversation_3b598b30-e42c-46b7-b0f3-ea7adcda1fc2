package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.MeetingExportAssembler;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.command.NewMeetingQuery;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.FmsTaskProcessTypeEnum;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lark.oapi.service.contact.v3.model.Department;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 新会议查询服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingQueryService {

    private final NewMeetingDomainService newMeetingDomainService;
    private final NewMeetingAssembler newMeetingAssembler;
    private final UserInfoAssembler userInfoAssembler;
    private final TenantFeishuAppClient tenantFeishuAppClient;
    private final NewMeetingMapper newMeetingMapper;
    private final FileDetailService fileDetailService;
    private final FileStorageService fileStorageService;
    private final MeetingExportAssembler meetingExportAssembler;
    private final FmsFileProcessor fmsFileProcessor;

    /**
     * 根据ID查询会议详情
     */
    public NewMeetingDTO getById(Long id) {
        NewMeeting meeting = newMeetingDomainService.findById(id);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        fillDepartmentDetails(Collections.singletonList(dto));
        return dto;
    }

    /**
     * 根据飞书日程事件ID查询会议
     */
    public NewMeetingDTO findByFsCalendarEventId(String fsCalendarEventId) {
        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        fillDepartmentDetails(Collections.singletonList(dto));
        return dto;
    }

    /**
     * 根据飞书会议ID查询会议
     */
    public NewMeetingDTO findByFsMeetingId(String fsMeetingId) {
        NewMeeting meeting = newMeetingDomainService.findByFsMeetingId(fsMeetingId);
        if (meeting == null) {
            return null;
        }

        NewMeetingDTO dto = newMeetingAssembler.toDTO(meeting);
        fillUserInfo(dto);
        fillPreMeetingFileUrls(dto);
        fillDepartmentDetails(Collections.singletonList(dto));
        return dto;
    }

    /**
     * 分页查询新会议列表
     */
    public PageResultDTO<NewMeetingListDTO> queryPage(NewMeetingQuery query) {
        // 如果指定了部门ID列表，获取部门及其所有子部门的ID列表
        if (query.getDepartmentIds() != null && !query.getDepartmentIds().isEmpty()) {
            List<String> allDepartmentIds = new ArrayList<>();
            for (String departmentId : query.getDepartmentIds()) {
                allDepartmentIds.addAll(getDepartmentAndChildrenIds(departmentId));
            }
            query.setDepartmentIds(allDepartmentIds);
        }

        Page<NewMeetingPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        Page<NewMeetingListDTO> resultPage = newMeetingMapper.queryPage(page, query);

        // 填充用户详细信息
        List<NewMeetingListDTO> records = resultPage.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            fillUserInfoForList(records);
            // 填充会前文档临时访问链接
            fillPreMeetingFileUrlsForList(records);
            // 填充部门详情
            fillDepartmentDetailsForList(records);
        }

        return new PageResultDTO<>(query.getPageNum(), query.getPageSize(), resultPage.getTotal(), records);
    }

    /**
     * 查询进行中或已结束的会议列表（按时间倒排）
     */
    public List<NewMeetingListDTO> listActiveAndCompletedMeetings() {
        log.info("查询进行中或已结束的会议列表");
        
        // 直接查询列表
        List<NewMeetingListDTO> records = newMeetingMapper.queryActiveAndCompletedMeetings();
        
        // 填充用户详细信息
        if (!CollectionUtils.isEmpty(records)) {
            fillUserInfoForList(records);
            // 填充会前文档临时访问链接
            fillPreMeetingFileUrlsForList(records);
            // 填充部门详情
            fillDepartmentDetailsForList(records);
        }
        
        log.info("查询进行中或已结束的会议列表成功，总数：{}", records.size());
        return records;
    }

    /**
     * 填充用户信息
     */
    private void fillUserInfo(NewMeetingDTO dto) {
        // 收集所有需要查询的用户ID，并进行去重
        List<String> userIds = dto.getAttendees() != null ? new ArrayList<>(dto.getAttendees()) : new ArrayList<>();

        // 添加主持人ID（如果不在参会人员列表中）
        if (dto.getHostUserId() != null && !userIds.contains(dto.getHostUserId())) {
            userIds.add(dto.getHostUserId());
        }

        // 添加记录员ID（如果不在参会人员列表中）
        if (dto.getRecorderUserId() != null && !userIds.contains(dto.getRecorderUserId())) {
            userIds.add(dto.getRecorderUserId());
        }

        // 添加实际参会人员ID（如果不在列表中）
        if (!CollectionUtils.isEmpty(dto.getActualAttendees())) {
            for (String actualAttendeeId : dto.getActualAttendees()) {
                if (!userIds.contains(actualAttendeeId)) {
                    userIds.add(actualAttendeeId);
                }
            }
        }

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        try {
            // 调用飞书API获取用户信息
            List<User> users = tenantFeishuAppClient.getContactService().userBatch(userIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, user -> user));

            // 填充参会人员详细信息
            if (!CollectionUtils.isEmpty(dto.getAttendees())) {
                List<FSUserInfoDTO> attendeeDetails = dto.getAttendees().stream()
                        .map(userId -> userMap.get(userId))
                        .filter(user -> user != null)
                        .map(userInfoAssembler::user2DTO)
                        .collect(Collectors.toList());
                dto.setAttendeeDetails(attendeeDetails);
            }

            // 填充实际参会人员详细信息
            if (!CollectionUtils.isEmpty(dto.getActualAttendees())) {
                List<FSUserInfoDTO> actualAttendeeDetails = dto.getActualAttendees().stream()
                        .map(userId -> userMap.get(userId))
                        .filter(user -> user != null)
                        .map(userInfoAssembler::user2DTO)
                        .collect(Collectors.toList());
                dto.setActualAttendeeDetails(actualAttendeeDetails);
            }

            // 填充主持人详细信息
            if (dto.getHostUserId() != null) {
                User hostUser = userMap.get(dto.getHostUserId());
                if (hostUser != null) {
                    dto.setHostUserDetail(userInfoAssembler.user2DTO(hostUser));
                }
            }

            // 填充记录员详细信息
            if (dto.getRecorderUserId() != null) {
                User recorderUser = userMap.get(dto.getRecorderUserId());
                if (recorderUser != null) {
                    dto.setRecorderUserDetail(userInfoAssembler.user2DTO(recorderUser));
                }
            }
        } catch (Exception e) {
            // 如果飞书API调用失败，不影响主要功能，只记录日志
            // 这里可以根据需要决定是否抛出异常
        }
    }

    /**
     * 为列表填充用户信息
     */
    private void fillUserInfoForList(List<NewMeetingListDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        try {
            // 收集所有需要查询的用户ID，参考 TeamQueryService.arrangeMemberInfo 的实现
            List<String> openIds = dtoList.stream()
                    .flatMap(dto -> Stream.concat(
                            Stream.concat(
                                    Stream.concat(
                                            Optional.ofNullable(dto.getAttendees()).orElse(Collections.emptyList()).stream(),
                                            Optional.ofNullable(dto.getHostUserId()).map(Stream::of).orElseGet(Stream::empty)
                                    ),
                                    Optional.ofNullable(dto.getRecorderUserId()).map(Stream::of).orElseGet(Stream::empty)
                            ),
                            Optional.ofNullable(dto.getActualAttendees()).orElse(Collections.emptyList()).stream()
                    )).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(openIds)) {
                return;
            }

            // 批量获取用户信息
            List<User> users = tenantFeishuAppClient.getContactService().userBatch(openIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, Function.identity()));

            // 为每个DTO填充用户信息
            dtoList.forEach(item -> {
                // 填充参会人员详细信息
                if (CollUtil.isNotEmpty(item.getAttendees())) {
                    List<User> attendees = item.getAttendees().stream()
                            .map(userMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(attendees)) {
                        item.setAttendeeDetails(userInfoAssembler.user2DTO(attendees));
                    }
                }

                // 填充实际参会人员详细信息
                if (CollUtil.isNotEmpty(item.getActualAttendees())) {
                    List<User> actualAttendees = item.getActualAttendees().stream()
                            .map(userMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(actualAttendees)) {
                        item.setActualAttendeeDetails(userInfoAssembler.user2DTO(actualAttendees));
                    }
                }

                // 填充主持人详细信息
                if (StrUtil.isNotBlank(item.getHostUserId())) {
                    User hostUser = userMap.get(item.getHostUserId());
                    if (ObjUtil.isNotNull(hostUser)) {
                        item.setHostUserDetail(userInfoAssembler.user2DTO(hostUser));
                    }
                }

                // 填充记录员详细信息
                if (StrUtil.isNotBlank(item.getRecorderUserId())) {
                    User recorderUser = userMap.get(item.getRecorderUserId());
                    if (ObjUtil.isNotNull(recorderUser)) {
                        item.setRecorderUserDetail(userInfoAssembler.user2DTO(recorderUser));
                    }
                }
            });
        } catch (Exception e) {
            log.warn("填充用户信息失败", e);
        }
    }

    /**
     * 为会前文档补充临时访问链接
     */
    public void fillPreMeetingFileUrls(NewMeetingDTO dto) {
        if (dto == null || CollUtil.isEmpty(dto.getPreMeetingDocuments())) {
            return;
        }

        try {
            for (PreMeetingDocumentDTO doc : dto.getPreMeetingDocuments()) {
                if (doc == null || StrUtil.isBlank(doc.getFileKey())) {
                    continue;
                }
                FileDetailPO fileDetailPO = fileDetailService.getById(doc.getFileKey());
                if (fileDetailPO == null || StrUtil.isBlank(fileDetailPO.getUrl())) {
                    continue;
                }
                FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                String presignedUrl = fileStorageService.generatePresignedUrl(
                        fileInfo,
                        DateUtil.offsetMinute(new Date(), 60)
                );
                doc.setUrl(presignedUrl);
            }
        } catch (Exception e) {
            log.warn("填充会前文档临时链接失败", e);
        }
    }

    /**
     * 导出会议管理数据（内部方法，使用NewMeetingQuery）
     * @param query 会议查询条件
     * @return 文件ID
     */
    public FileDetailPO exportMeetingManagementDataInternal(NewMeetingQuery query) {
        log.info("开始导出会议管理数据，参数：{}", query);

        try {
            // 1. 查询会议数据（不分页，获取所有数据）
            query.setPageNum(1);
            query.setPageSize(50000);
            List<NewMeetingListDTO> meetingList = queryPage(query).getList();

            // 2. 转换为Excel导出数据
            List<MeetingExportDTO> exportData = meetingExportAssembler.convertToExportData(meetingList);

            // 3. 使用通用文件处理器生成和上传文件
            return fmsFileProcessor.processFile(
                    FmsTaskProcessTypeEnum.EXCEL_EXPORT,
                    exportData,
                    MeetingExportDTO.class,
                    "会议管理数据导出",
                    "会议管理数据"
            );


        } catch (Exception e) {
            log.error("导出会议管理数据失败", e);
            throw new RuntimeException("导出会议管理数据失败", e);
        }
    }

    /**
     * 为列表中的会前文档补充临时访问链接
     */
    public void fillPreMeetingFileUrlsForList(List<NewMeetingListDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        try {
            for (NewMeetingListDTO dto : dtoList) {
                if (dto == null || CollUtil.isEmpty(dto.getPreMeetingDocuments())) {
                    continue;
                }

                for (PreMeetingDocumentDTO doc : dto.getPreMeetingDocuments()) {
                    if (doc == null || StrUtil.isBlank(doc.getFileKey())) {
                        continue;
                    }
                    FileDetailPO fileDetailPO = fileDetailService.getById(doc.getFileKey());
                    if (fileDetailPO == null || StrUtil.isBlank(fileDetailPO.getUrl())) {
                        continue;
                    }
                    FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
                    String presignedUrl = fileStorageService.generatePresignedUrl(
                            fileInfo,
                            DateUtil.offsetMinute(new Date(), 60)
                    );
                    doc.setUrl(presignedUrl);
                }
            }
        } catch (Exception e) {
            log.warn("为列表填充会前文档临时链接失败", e);
        }
    }

    /**
     * 批量获取部门详情并填充到DTO中
     */
    private void fillDepartmentDetails(List<NewMeetingDTO> meetingDTOS) {
        if (CollUtil.isEmpty(meetingDTOS)) {
            return;
        }

        // 提取所有的departmentId
        Set<String> departmentIds = meetingDTOS.stream()
                .map(NewMeetingDTO::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(departmentIds)) {
            return;
        }

        try {
            // 将Long类型的departmentId转换为String类型
            List<String> departmentIdStrings = departmentIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            // 调用飞书接口获取部门详情
            List<Department> departments = tenantFeishuAppClient.getContactService().getDepartmentBatch(departmentIdStrings);

            // 构建部门ID到部门详情的映射
            Map<Long, DepartmentDetailDTO> departmentMap = departments.stream()
                    .collect(Collectors.toMap(
                            dept -> Long.valueOf(dept.getDepartmentId()),
                            this::convertToDepartmentDetailDTO,
                            (existing, replacement) -> existing
                    ));

            // 填充部门详情到DTO中
            meetingDTOS.forEach(dto -> {
                if (dto.getDepartmentId() != null) {
                    DepartmentDetailDTO departmentDetail = departmentMap.get(dto.getDepartmentId());
                    dto.setDepartmentDetail(departmentDetail);
                }
            });

        } catch (Exception e) {
            log.error("获取部门详情失败", e);
            // 如果获取部门详情失败，不影响主流程，只是部门详情为空
        }
    }

    /**
     * 批量获取部门详情并填充到列表DTO中
     */
    private void fillDepartmentDetailsForList(List<NewMeetingListDTO> meetingListDTOS) {
        if (CollUtil.isEmpty(meetingListDTOS)) {
            return;
        }

        // 提取所有的departmentId
        Set<String> departmentIds = meetingListDTOS.stream()
                .map(NewMeetingListDTO::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(departmentIds)) {
            return;
        }

        try {
            // 将Long类型的departmentId转换为String类型
            List<String> departmentIdStrings = departmentIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            // 调用飞书接口获取部门详情
            List<Department> departments = tenantFeishuAppClient.getContactService().getDepartmentBatch(departmentIdStrings);

            // 构建部门ID到部门详情的映射
            Map<Long, DepartmentDetailDTO> departmentMap = departments.stream()
                    .collect(Collectors.toMap(
                            dept -> Long.valueOf(dept.getDepartmentId()),
                            this::convertToDepartmentDetailDTO,
                            (existing, replacement) -> existing
                    ));

            // 填充部门详情到DTO中
            meetingListDTOS.forEach(dto -> {
                if (dto.getDepartmentId() != null) {
                    DepartmentDetailDTO departmentDetail = departmentMap.get(dto.getDepartmentId());
                    dto.setDepartmentDetail(departmentDetail);
                }
            });

        } catch (Exception e) {
            log.error("获取部门详情失败", e);
            // 如果获取部门详情失败，不影响主流程，只是部门详情为空
        }
    }

    /**
     * 将飞书Department对象转换为DepartmentDetailDTO
     */
    private DepartmentDetailDTO convertToDepartmentDetailDTO(Department department) {
        if (department == null) {
            return null;
        }

        DepartmentDetailDTO dto = new DepartmentDetailDTO();
        dto.setDepartmentId(department.getDepartmentId());
        dto.setName(department.getName());
        dto.setNameEn(String.valueOf(department.getI18nName()));
        dto.setParentDepartmentId(department.getParentDepartmentId());
        dto.setManagerId(department.getLeaderUserId());
        return dto;
    }

    /**
     * 获取部门及其所有子部门的ID列表
     */
    private List<String> getDepartmentAndChildrenIds(String departmentId) {
        if (departmentId == null) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        result.add(departmentId);

        try {
            // 获取子部门列表
            List<Department> children = tenantFeishuAppClient.getContactService().getDepartmentChildren(departmentId);
            
            // 递归获取所有子部门的ID
            for (Department child : children) {
                String childId = child.getDepartmentId();
                result.add(childId);
                
                // 递归获取子部门的子部门
                List<String> grandChildren = getDepartmentAndChildrenIds(childId);
                result.addAll(grandChildren);
            }
        } catch (Exception e) {
            log.warn("获取部门子部门失败，部门ID：{}", departmentId, e);
        }

        return result;
    }
}

package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 签到配置DTO
 * @date 2025-08-26
 */
@Data
@ApiModel("签到配置信息")
public class CheckInConfigDTO {

    @ApiModelProperty("是否启用签到")
    private Boolean enabled;

    @ApiModelProperty("会议开始前多少分钟可以开始签到")
    private Integer startMinutesBefore;

    @ApiModelProperty("会议开始后多少分钟内仍可签到")
    private Integer endMinutesAfter;
}
package cn.july.orch.meeting.job;

import cn.july.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.july.orch.meeting.service.MeetingRoomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 会议室数据同步定时任务
 * 每小时执行一次，同步飞书会议室数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MeetingRoomSyncTask {

    private final MeetingRoomService meetingRoomService;

    /**
     * 每小时执行一次，同步飞书会议室数据
     * cron表达式：0 0 * * * ? 表示每小时的0分0秒执行
     */
    @Scheduled(cron = "0 0 * * * ?")
    @IgnoreTenant
    public void syncMeetingRooms() {
        log.info("开始执行会议室数据同步任务");
        try {
            meetingRoomService.syncMeetingRoomsFromFeishu();
            log.info("会议室数据同步任务执行完成");
        } catch (Exception e) {
            log.error("会议室数据同步任务执行失败", e);
        }
    }
}
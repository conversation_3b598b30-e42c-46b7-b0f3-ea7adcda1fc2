package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.utils.execl.EasyExcelUtil;
import cn.july.orch.meeting.utils.execl.ExportExcelModel;
import cn.july.orch.meeting.utils.execl.TemplateDateListener;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "导入导出模板")
@RestController
@RequestMapping("/execl")
public class ExeclTestController {

    @PostMapping("export")
    @ApiOperation("下载")
    public void exportTemplate(HttpServletResponse response) {
        List<ExportTemplateDTO> list = new ArrayList<>();
        ExportTemplateDTO dto = new ExportTemplateDTO();
        dto.setUsername("123");
        dto.setPassword("123");
        dto.setCreateTime(LocalDateTime.now());
        dto.setRemark("123");
        list.add(dto);
        ExportExcelModel<ExportTemplateDTO> model = new ExportExcelModel<>();
        model.setSheetName("sheet测试");
        model.setData(list);
        model.setClazz(ExportTemplateDTO.class);
        EasyExcelUtil.exportExcel(response, "导出execl测试", model);
    }

    @PostMapping("import")
    @ApiOperation("上传")
    public List<ExportTemplateCommand> importTemplate(@RequestParam("file") MultipartFile file) throws IOException {
        TemplateDateListener dateListener = new TemplateDateListener();
        EasyExcel.read(file.getInputStream(),ExportTemplateCommand.class,dateListener).excelType(EasyExcelUtil.getExcelType(file)).sheet().doRead();
        return dateListener.getDataList();
    }

    /**
     * 实际开发时单独创建类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @ContentRowHeight(20) // 设置行高
    public static class ExportTemplateDTO {

        @ExcelProperty(value = "用户名", index = 0) // 设置列名和顺序
        @ColumnWidth(20) // 设置列宽
        private String username;

        @ExcelProperty(value = "密码", index = 1) // 设置列名和顺序
        @ColumnWidth(20) // 设置列宽
        private String password;

        @ExcelProperty(value = "创建时间", index = 2) // 设置列名和顺序
        @ColumnWidth(25) // 设置列宽
        private LocalDateTime createTime;

        @ContentStyle(
                fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, // 填充样式
                // fillForegroundColor = 40, // 背景颜色
                horizontalAlignment = HorizontalAlignmentEnum.LEFT, // 水平居中
                verticalAlignment = VerticalAlignmentEnum.CENTER // 垂直居中
        )
        @ContentFontStyle(
                fontName = "宋体", // 字体
                fontHeightInPoints = 12, // 字体大小
                bold = BooleanEnum.TRUE // 加粗
                // color = 10 // 字体颜色
        )
        @ExcelProperty(value = "备注", index = 3)
        private String remark;
    }

    /**
     * 实际开发时单独创建类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ExportTemplateCommand {

        private String username;

        private String password;

        private LocalDateTime createTime;

        private String remark;
    }
}

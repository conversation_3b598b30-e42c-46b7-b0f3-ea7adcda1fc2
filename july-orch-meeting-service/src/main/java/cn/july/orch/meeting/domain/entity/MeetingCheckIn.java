package cn.july.orch.meeting.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 参会人签到信息实体
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingCheckIn {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 参会人员open_id
     */
    private String attendeeOpenId;

    /**
     * 参会人员姓名
     */
    private String attendeeName;

    /**
     * 签到码（6位数字）
     */
    private String checkinCode;

    /**
     * 签到状态(0:未签到,1:已签到)
     */
    private Integer checkinStatus;

    /**
     * 签到时间
     */
    private LocalDateTime checkinTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 简化的任务信息DTO，用于会议详情中
 *
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "任务信息")
public class TaskInfoDTO {

    @ApiModelProperty(value = "任务ID")
    private Long id;

    @ApiModelProperty(value = "任务标题")
    private String title;

    @ApiModelProperty(value = "任务描述")
    private String description;

    @ApiModelProperty(value = "任务状态")
    private TaskStatusEnum status;

    @ApiModelProperty(value = "优先级")
    private TaskPriorityEnum priority;

    @ApiModelProperty(value = "负责人ID")
    private String ownerOpenId;

    @ApiModelProperty(value = "负责人姓名")
    private String ownerName;

    @ApiModelProperty(value = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dueDate;

    @ApiModelProperty(value = "是否超期")
    private Boolean isOverdue;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "任务清单ID")
    private Long taskListId;

    @ApiModelProperty(value = "任务清单名称")
    private String taskListName;
}
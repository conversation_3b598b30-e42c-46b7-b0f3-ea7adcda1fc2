package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.MeetingStandardCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingStandardUpdateCommand;
import cn.july.orch.meeting.domain.dto.AgendaItemDTO;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标准转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingStandardAssembler {

    MeetingStandardPO toPO(MeetingStandard meetingStandard);

    MeetingStandard toEntity(MeetingStandardPO meetingStandardPO);

    MeetingStandardDTO toDTO(MeetingStandard meetingStandard);

    List<MeetingStandardDTO> toDTOList(List<MeetingStandard> meetingStandards);

    MeetingStandard toEntity(MeetingStandardCreateCommand command);

    MeetingStandard toEntity(MeetingStandardUpdateCommand command);

    MeetingStandardDTO PO2DTO(MeetingStandardPO po);

    /**
     * 在转换完成后对议程规划进行排序
     */
    @AfterMapping
    default void afterMappingToDTO(MeetingStandardPO source, @MappingTarget MeetingStandardDTO target) {
        if (target.getAgendaPlan() != null) {
            // 按照sequence正序排序，sequence越小越靠前
            List<AgendaItemDTO> sortedAgenda = target.getAgendaPlan().stream()
                    .sorted(Comparator.comparing(AgendaItemDTO::getSequence, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            target.setAgendaPlan(sortedAgenda);
        }
    }

    /**
     * 在转换完成后对议程规划进行排序
     */
    @AfterMapping
    default void afterMappingToDTO(MeetingStandard source, @MappingTarget MeetingStandardDTO target) {
        if (target.getAgendaPlan() != null) {
            // 按照sequence正序排序，sequence越小越靠前
            List<AgendaItemDTO> sortedAgenda = target.getAgendaPlan().stream()
                    .sorted(Comparator.comparing(AgendaItemDTO::getSequence, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            target.setAgendaPlan(sortedAgenda);
        }
    }
}
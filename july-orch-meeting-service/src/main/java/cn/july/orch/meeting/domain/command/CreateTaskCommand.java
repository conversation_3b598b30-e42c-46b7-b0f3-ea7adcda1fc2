package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 创建任务命令
 */
@Data
@ApiModel("创建任务命令")
public class CreateTaskCommand {
    
    @ApiModelProperty(value = "任务代码", required = true)
    @NotBlank(message = "任务代码不能为空")
    private String taskCode;
    
    @ApiModelProperty(value = "任务参数", required = true)
    @NotNull(message = "任务参数不能为空")
    private Map<String, Object> params;
}

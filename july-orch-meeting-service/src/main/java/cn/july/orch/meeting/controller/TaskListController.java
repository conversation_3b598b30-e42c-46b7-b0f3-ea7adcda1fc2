package cn.july.orch.meeting.controller;

import cn.july.core.model.ddd.IdQuery;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.TaskListCreateCommand;
import cn.july.orch.meeting.domain.command.TaskListUpdateCommand;
import cn.july.orch.meeting.domain.dto.TaskListManagementDTO;
import cn.july.orch.meeting.domain.query.TaskListQuery;
import cn.july.orch.meeting.service.TaskListActionService;
import cn.july.orch.meeting.service.TaskListQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR> Assistant
 * @description 任务清单控制器
 */
@Api(tags = "任务清单管理")
@RestController
@RequestMapping("/task-list")
@RequiredArgsConstructor
public class TaskListController {

    private final TaskListActionService taskListActionService;
    private final TaskListQueryService taskListQueryService;

    @PostMapping("/create")
    @ApiOperation("创建任务清单")
    public Long create(@Valid @RequestBody TaskListCreateCommand command) {
        return taskListActionService.create(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新任务清单")
    public void update(@Valid @RequestBody TaskListUpdateCommand command) {
        taskListActionService.update(command);
    }

    @PostMapping("/delete")
    @ApiOperation("删除任务清单")
    public void delete(@RequestBody IdQuery idQuery) {
        taskListActionService.delete(idQuery.getId());
    }

    @PostMapping("/detail")
    @ApiOperation("查询任务清单详情")
    public TaskListManagementDTO detail(@RequestBody IdQuery idQuery) {
        return taskListQueryService.detail(idQuery.getId());
    }

    @PostMapping("/page")
    @ApiOperation("分页查询任务清单")
    public PageResultDTO<TaskListManagementDTO> page(@RequestBody TaskListQuery query) {
        return taskListQueryService.page(query);
    }
}
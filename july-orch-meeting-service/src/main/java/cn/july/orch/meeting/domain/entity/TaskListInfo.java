package cn.july.orch.meeting.domain.entity;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.config.CurrentUserHolder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务清单信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskListInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 父清单ID (自关联, 逻辑关联 task_lists.id)
     */
    private Long parentId;

    /**
     * 清单名称
     */
    private String name;

    /**
     * 清单描述
     */
    private String description;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private String createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private String updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 创建任务清单
     */
    public static TaskListInfo create(String name, String description, Long parentId) {
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        return TaskListInfo.builder()
                .name(name)
                .description(description)
                .parentId(parentId)
                .deleted(DeletedEnum.NOT_DELETED)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .createUserId(currentUserOpenId)
                .createUserName(currentUserName)
                .updateUserId(currentUserOpenId)
                .updateUserName(currentUserName)
                .build();
    }

    /**
     * 创建任务清单（无父清单）
     */
    public static TaskListInfo create(String name, String description) {
        return create(name, description, null);
    }

    /**
     * 更新任务清单
     */
    public void update(String name, String description, Long parentId) {
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        this.name = name;
        this.description = description;
        this.parentId = parentId;
        this.updateTime = LocalDateTime.now();
        this.updateUserId = currentUserOpenId;
        this.updateUserName = currentUserName;
    }

    /**
     * 更新任务清单（不更新父清单）
     */
    public void update(String name, String description) {
        update(name, description, this.parentId);
    }

    /**
     * 删除任务清单
     */
    public void delete() {
        this.deleted = DeletedEnum.DELETED;
        this.updateTime = LocalDateTime.now();

        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();
        this.updateUserId = currentUserOpenId;
        this.updateUserName = currentUserName;
    }

    /**
     * 安全获取当前用户OpenID
     */
    private static String getCurrentUserOpenIdSafely() {
        try {
            return CurrentUserHolder.getOpenId();
        } catch (Exception e) {
            return "system";
        }
    }

    /**
     * 安全获取当前用户名称
     */
    private static String getCurrentUserNameSafely() {
        try {
            return CurrentUserHolder.getCurrentUser().getName();
        } catch (Exception e) {
            return "系统";
        }
    }
}
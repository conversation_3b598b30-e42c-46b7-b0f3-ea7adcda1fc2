package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会议统计分析结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会议统计分析结果")
public class MeetingStatisticsDTO {

    @ApiModelProperty("基础统计信息")
    private BasicStatistics basicStatistics;

    @ApiModelProperty("AI功能应用统计列表")
    private List<AiStatistics> aiStatistics;

    @ApiModelProperty("会议质量分析列表")
    private List<QualityAnalysis> qualityAnalysis;

    @ApiModelProperty("会议分布统计列表")
    private List<DistributionStatistics> distributionStatistics;

    @ApiModelProperty("典型会议分析")
    private TypicalMeetings typicalMeetings;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("基础统计信息")
    public static class BasicStatistics {
        @ApiModelProperty("AI完成生成数")
        private Integer aiGeneratedCount;

        @ApiModelProperty("会议资料AI应用占比(%)")
        private Double aiApplicationRate;

        @ApiModelProperty("质量分析报告生成数")
        private Integer qualityReportCount;

        @ApiModelProperty("平均会议质量得分")
        private Double averageQualityScore;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("AI功能统计")
    public static class AiStatistics {
        @ApiModelProperty("功能类型")
        private String functionType;

        @ApiModelProperty("使用次数")
        private Integer usageCount;

        @ApiModelProperty("占比(%)")
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("会议质量分析")
    public static class QualityAnalysis {
        @ApiModelProperty("质量得分区间")
        private String scoreRange;

        @ApiModelProperty("会议数量")
        private Integer meetingCount;

        @ApiModelProperty("占比(%)")
        private Double percentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("会议分布统计")
    public static class DistributionStatistics {
        @ApiModelProperty("会议类型")
        private String meetingType;

        @ApiModelProperty("AI质量对比")
        private Double aiQualityRatio;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("典型会议")
    public static class TypicalMeetings {
        @ApiModelProperty("高质量会议Top5")
        private List<TypicalMeetingItem> topQualityMeetings;

        @ApiModelProperty("待改进会议Top5")
        private List<TypicalMeetingItem> improvementMeetings;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("典型会议项")
    public static class TypicalMeetingItem {
        @ApiModelProperty("会议ID")
        private Long id;

        @ApiModelProperty("会议名称")
        private String meetingName;

        @ApiModelProperty("会议标签")
        private List<SimpleMeetingTagDTO> meetingTags;

        @ApiModelProperty("质量得分")
        private Double qualityScore;

        @ApiModelProperty("会议日期")
        private String meetingDate;
    }
}
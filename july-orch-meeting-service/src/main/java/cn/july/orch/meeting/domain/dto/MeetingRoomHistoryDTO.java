package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室历史会议DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "会议室历史会议信息")
public class MeetingRoomHistoryDTO {

    @ApiModelProperty(value = "会议ID")
    private Long id;

    @ApiModelProperty(value = "会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty(value = "主持人ID")
    private String hostUserId;

    @ApiModelProperty(value = "主持人姓名")
    private String hostUserName;

    @ApiModelProperty(value = "参会人数")
    private Integer attendeeCount;

    @ApiModelProperty(value = "实际参会人数")
    private Integer actualAttendeeCount;

    @ApiModelProperty(value = "会议时长(分钟)")
    private Integer duration;
}
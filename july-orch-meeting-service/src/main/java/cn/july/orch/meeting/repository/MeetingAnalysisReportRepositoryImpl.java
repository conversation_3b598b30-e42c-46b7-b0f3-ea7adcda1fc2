package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.assembler.MeetingAnalysisReportAssembler;
import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;
import cn.july.orch.meeting.domain.po.MeetingAnalysisReportPO;
import cn.july.orch.meeting.mapper.MeetingAnalysisReportMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告仓储实现
 */
@Repository
public class MeetingAnalysisReportRepositoryImpl implements IMeetingAnalysisReportRepository {

    @Resource
    private MeetingAnalysisReportMapper meetingAnalysisReportMapper;

    @Resource
    private MeetingAnalysisReportAssembler meetingAnalysisReportConverter;

    @Override
    public void save(MeetingAnalysisReport meetingAnalysisReport) {
        MeetingAnalysisReportPO po = meetingAnalysisReportConverter.toPO(meetingAnalysisReport);
        meetingAnalysisReportMapper.insert(po);
        meetingAnalysisReport.setId(po.getId());
    }

    @Override
    public void update(MeetingAnalysisReport meetingAnalysisReport) {
        MeetingAnalysisReportPO po = meetingAnalysisReportConverter.toPO(meetingAnalysisReport);
        meetingAnalysisReportMapper.updateById(po);
    }

    @Override
    public MeetingAnalysisReport findById(Long id) {
        MeetingAnalysisReportPO po = meetingAnalysisReportMapper.selectById(id);
        return po != null ? meetingAnalysisReportConverter.toEntity(po) : null;
    }

    @Override
    public MeetingAnalysisReport findByMeetingId(Long meetingId) {
        LambdaQueryWrapper<MeetingAnalysisReportPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingAnalysisReportPO::getMeetingId, meetingId);
        wrapper.orderByDesc(MeetingAnalysisReportPO::getCreateTime);
        wrapper.last("limit 1");
        MeetingAnalysisReportPO po = meetingAnalysisReportMapper.selectOne(wrapper);
        return po != null ? meetingAnalysisReportConverter.toEntity(po) : null;
    }

    @Override
    public List<MeetingAnalysisReport> findByMeetingIdsAndTimeRange(List<Long> meetingIds, 
                                                                   LocalDateTime startTime, 
                                                                   LocalDateTime endTime) {
        if (meetingIds == null || meetingIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<MeetingAnalysisReportPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MeetingAnalysisReportPO::getMeetingId, meetingIds);
        wrapper.between(MeetingAnalysisReportPO::getCreateTime, startTime, endTime);
        
        List<MeetingAnalysisReportPO> pos = meetingAnalysisReportMapper.selectList(wrapper);
        return pos.stream()
            .map(meetingAnalysisReportConverter::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public void deleteById(Long id) {
        meetingAnalysisReportMapper.deleteById(id);
    }
}

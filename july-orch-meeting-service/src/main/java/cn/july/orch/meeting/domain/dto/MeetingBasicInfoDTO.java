package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.domain.dto.SimpleMeetingTagDTO;

import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会议基本信息DTO（用于任务详情展示）
 * @date 2025-08-29
 */
@Data
@ApiModel("会议基本信息")
public class MeetingBasicInfoDTO {

    @ApiModelProperty("会议ID")
    private Long id;

    @ApiModelProperty("会议名称")
    private String meetingName;

    @ApiModelProperty("会议描述")
    private String meetingDescription;

    @ApiModelProperty("会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty("会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty("会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty("优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty("会议地点")
    private String meetingLocation;

    @ApiModelProperty("主持人姓名")
    private String hostUserName;

    @ApiModelProperty("参会人员详细信息列表")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty("参会人数")
    private Integer attendeeCount;

    @ApiModelProperty("会议链接")
    private String meetingUrl;

    @ApiModelProperty("妙计链接")
    private String minuteUrl;

    @ApiModelProperty("会议标签列表")
    private List<SimpleMeetingTagDTO> meetingTags;
}
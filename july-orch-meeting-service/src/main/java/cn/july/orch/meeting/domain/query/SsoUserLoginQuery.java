package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.ClientTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsoUserLoginQuery {

    /**
     * 飞书授权码
     */
    @NotBlank(message = "授权码不能为空")
    private String code;

    @NotBlank(message = "授权回调不能为空")
    private String redirectUri;

    @NotNull(message = "登录来源不能为空")
    private ClientTypeEnum clientType;

//    @NotNull(message = "租户不能为空")
    private Long tenantId;
}

package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.core.exception.BusinessException;
import cn.july.core.utils.CronUtils;
import cn.july.orch.meeting.assembler.MeetingPlanAssembler;
import cn.july.orch.meeting.assembler.MeetingStandardAssembler;
import cn.july.orch.meeting.assembler.MeetingTagAssembler;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.po.MeetingTagPO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.domain.query.MeetingPlanInsightQuery;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import cn.july.orch.meeting.mapper.MeetingTagMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.processor.MeetingPlanProcessor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.oapi.service.contact.v3.model.Department;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议规划服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingPlanService {

    private final MeetingPlanMapper meetingPlanMapper;
    private final MeetingPlanAssembler meetingPlanAssembler;
    private final UserInfoQueryService userInfoQueryService;
    private final MeetingPlanProcessor meetingPlanProcessor;
    private final MeetingStandardAssembler meetingStandardAssembler;
    private final MeetingTagAssembler meetingTagAssembler;
    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingTagMapper meetingTagMapper;
    private final NewMeetingAssembler newMeetingAssembler;
    private final NewMeetingMapper newMeetingMapper;
    private final TenantFeishuAppClient tenantFeishuAppClient;

    /**
     * 创建会议规划
     */
    @Transactional
    public void createMeetingPlan(MeetingPlanCreateCommand command) {

        MeetingStandardPO standard = meetingPlanProcessor.checkCreatePlan(command);

        MeetingPlanPO meetingPlanPO = meetingPlanAssembler.toEntity(command);
        meetingPlanPO.setStatus(MeetingPlanStatusEnum.NOT_STARTED);
        meetingPlanPO.setAdvanceNoticeSent(0);

        //处理重复规则
        if (StrUtil.isNotBlank(command.getCron())) {
            Integer defaultDuration = standard.getDefaultDuration();
            meetingPlanPO.setPlannedDuration(defaultDuration);
            //对于重复会议来说,开始结束时间表示下次规划的时间.
            LocalDateTime nextTime = CronUtils.nextExecutionTime(command.getCron(), LocalDateTime.now());
            meetingPlanPO.setPlannedStartTime(nextTime);
            meetingPlanPO.setPlannedEndTime(nextTime.plusMinutes(defaultDuration));
            meetingPlanPO.setUuid(IdUtil.fastSimpleUUID());
            if (ObjUtil.isNotNull(command.getRecurrenceEndDate()) && command.getRecurrenceEndDate().isBefore(nextTime)) {
                throw new BusinessException("当前日期到重复会议截止时间中间必须包含一次可执行时间");
            }
        } else {
            long minutes = Duration.between(command.getPlannedStartTime(), command.getPlannedEndTime()).toMinutes();
            meetingPlanPO.setPlannedDuration((int) minutes);
        }
        meetingPlanMapper.insert(meetingPlanPO);
    }

    /**
     * 更新会议规划
     */
    @Transactional
    public void updateMeetingPlan(MeetingPlanUpdateCommand command) {
        MeetingStandardPO standard = meetingPlanProcessor.checkUpdatePlan(command);
        MeetingPlanPO planPO = meetingPlanMapper.selectById(command.getId());

        planPO.setPlanName(command.getPlanName());
        planPO.setPlanDescription(command.getPlanDescription());
        planPO.setMeetingStandardId(command.getMeetingStandardId());
        planPO.setDepartmentId(command.getDepartmentId());
        planPO.setAttendees(command.getAttendees());
        planPO.setPreMeetingDocuments(command.getPreMeetingDocuments());
        planPO.setTagIds(command.getTagIds());
        //重复规则
        if (StrUtil.isNotBlank(command.getCron())) {
            Integer defaultDuration = standard.getDefaultDuration();
            planPO.setPlannedDuration(defaultDuration);
            //对于重复会议来说,开始结束时间表示下次规划的时间.
            LocalDateTime nextTime = CronUtils.nextExecutionTime(command.getCron(), LocalDateTime.now());
            planPO.setPlannedStartTime(nextTime);
            planPO.setPlannedEndTime(nextTime.plusMinutes(defaultDuration));
            if (ObjUtil.isNotNull(command.getRecurrenceEndDate()) && command.getRecurrenceEndDate().isBefore(nextTime)) {
                throw new BusinessException("当前日期到重复会议截止时间中间必须包含一次可执行时间");
            }
        } else {
            long minutes = Duration.between(command.getPlannedStartTime(), command.getPlannedEndTime()).toMinutes();
            planPO.setPlannedDuration((int) minutes);
            planPO.setPlannedStartTime(command.getPlannedStartTime());
            planPO.setPlannedEndTime(command.getPlannedEndTime());
        }

        meetingPlanMapper.updateById(planPO);
    }

    /**
     * 删除会议规划
     */
    @Transactional
    public void deleteMeetingPlan(Long id) {
        MeetingPlanPO meetingPlanPO = meetingPlanMapper.selectById(id);
        if (meetingPlanPO == null) {
            throw new BusinessException("会议规划不存在");
        }
        if (meetingPlanPO.getStatus() != MeetingPlanStatusEnum.NOT_STARTED) {
            throw new BusinessException("只有未开始的会议规划可以删除");
        }
        meetingPlanMapper.deleteById(id);
    }

    /**
     * 根据ID查询会议规划详情
     */
    public MeetingPlanDTO getById(Long id) {
        MeetingPlanPO po = meetingPlanMapper.selectById(id);
        if (po == null) {
            return null;
        }
        List<MeetingPlanDTO> resultList = arrangeData(Collections.singletonList(meetingPlanAssembler.PO2DTO(po)));
        fillDepartmentDetails(resultList);
        MeetingPlanDTO result = resultList.get(0);

        //关联已完成会议
        List<Long> planIds = Collections.singletonList(result.getId());
        //重复会议显示所有有关的
        if (StrUtil.isNotBlank(result.getCron())) {
            String uuid = result.getUuid();
            List<MeetingPlanPO> pos = meetingPlanMapper.selectByUuid(uuid);
            planIds = pos.stream().map(MeetingPlanPO::getId).distinct().collect(Collectors.toList());
        }
        List<NewMeetingPO> meetingPOS = newMeetingMapper.selectByPlanIds(planIds);
        if (CollUtil.isNotEmpty(meetingPOS)) {
            result.setNewMeetings(newMeetingAssembler.PO2DTO(meetingPOS));
        }
        return result;
    }

    /**
     * 查询日历维度的会议规划
     * 根据查询时间范围与当前时间的关系，采用不同的查询策略
     */
    public List<MeetingPlanDTO> queryCalendar(MeetingPlanCalendarQuery query) {
        // 如果指定了部门ID列表，获取部门及其所有子部门的ID列表
        if (query.getDepartmentIds() != null && !query.getDepartmentIds().isEmpty()) {
            List<String> allDepartmentIds = new ArrayList<>();
            for (String departmentId : query.getDepartmentIds()) {
                allDepartmentIds.addAll(getDepartmentAndChildrenIds(departmentId));
            }
            query.setDepartmentIds(allDepartmentIds);
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime queryStart = query.getStartDate();
        LocalDateTime queryEnd = query.getEndDate();

        List<MeetingPlanDTO> allPlans = new ArrayList<>();

        // 1. 判断查询时间范围与当前时间的关系
        if (queryEnd.isBefore(now)) {
            // 情况1：查询范围完全在当前时间之前
            // 无论是否重复性会议，按查询条件一次查出
            allPlans.addAll(queryHistoricalPlans(query));

        } else if (queryStart.isAfter(now)) {
            // 情况2：查询范围完全在当前时间之后
            // 一次性会议查询出来，重复性会议特殊处理
            allPlans.addAll(queryFuturePlans(query));

        } else {
            // 情况3：查询范围跨越当前时间
            // 分别处理过去和未来的部分

            // 3.1 处理当前时间之前的部分
            if (queryStart.isBefore(now)) {
                MeetingPlanCalendarQuery pastQuery = createSubQuery(query, queryStart, now);
                allPlans.addAll(queryHistoricalPlans(pastQuery));
            }

            // 3.2 处理当前时间之后的部分
            if (queryEnd.isAfter(now)) {
                MeetingPlanCalendarQuery futureQuery = createSubQuery(query, now, queryEnd);
                allPlans.addAll(queryFuturePlans(futureQuery));
            }
        }
        //扩展字段
        arrangeData(allPlans);
        // 2. 排序并返回
        List<MeetingPlanDTO> result = allPlans.stream()
                .sorted(Comparator.comparing(MeetingPlanDTO::getPlannedStartTime))
                .collect(Collectors.toList());

        // 填充部门详情
        fillDepartmentDetails(result);

        return result;
    }


    /**
     * 更新逾期状态
     */
    public void updateOverdueStatus() {
        List<MeetingPlanPO> overduePlans = meetingPlanMapper.findOverduePlans();
        if (CollUtil.isEmpty(overduePlans)) {
            return;
        }
        List<Long> ids = overduePlans.stream().map(MeetingPlanPO::getId).collect(Collectors.toList());
        meetingPlanMapper.updateStatusByIds(ids, MeetingPlanStatusEnum.OVERDUE);
        //重复会议额外处理
        overduePlans.stream().filter(po -> StrUtil.isNotBlank(po.getCron())).forEach(po -> {
            MeetingPlanPO newPO = new MeetingPlanPO();
            BeanUtils.copyProperties(po, newPO);

            LocalDateTime nextTime = CronUtils.nextExecutionTime(po.getCron(), LocalDateTime.now());
            newPO.setPlannedStartTime(nextTime);
            newPO.setPlannedEndTime(nextTime.plusMinutes(po.getPlannedDuration()));
            newPO.setStatus(MeetingPlanStatusEnum.NOT_STARTED);
            newPO.setAdvanceNoticeSent(0);
            newPO.setId(null);
            meetingPlanMapper.insert(newPO);
        });
    }

    /**
     * 查询未完成会议
     *
     * @param query
     * @return
     */
    public List<MeetingPlanDTO> notStartMeeting(MeetingPlanCalendarQuery query) {
        // 如果指定了部门ID列表，获取部门及其所有子部门的ID列表
        if (query.getDepartmentIds() != null && !query.getDepartmentIds().isEmpty()) {
            List<String> allDepartmentIds = new ArrayList<>();
            for (String departmentId : query.getDepartmentIds()) {
                allDepartmentIds.addAll(getDepartmentAndChildrenIds(departmentId));
            }
            query.setDepartmentIds(allDepartmentIds);
        }

        List<MeetingPlanPO> poList = meetingPlanMapper.selectNotStartMeeting(query);
        List<MeetingPlanDTO> result = arrangeData(meetingPlanAssembler.PO2DTO(poList));
        fillDepartmentDetails(result);
        return result;
    }

    /**
     * 查询历史会议规划（当前时间之前的部分）
     * 无论是否重复性会议，都按查询条件一次查出
     */
    private List<MeetingPlanDTO> queryHistoricalPlans(MeetingPlanCalendarQuery query) {
        return meetingPlanAssembler.PO2DTO(meetingPlanMapper.queryAllHistoricalPlans(query));
    }

    /**
     * 查询未来会议规划（当前时间之后的部分）
     * 一次性会议查询出来，重复性会议特殊处理
     */
    private List<MeetingPlanDTO> queryFuturePlans(MeetingPlanCalendarQuery query) {
        List<MeetingPlanDTO> result = new ArrayList<>();

        // 1. 查询一次性会议
        List<MeetingPlanPO> oneTimePlanPOs = meetingPlanMapper.queryFutureOneTimePlans(query);
        if (CollUtil.isNotEmpty(oneTimePlanPOs)) {
            result.addAll(meetingPlanAssembler.PO2DTO(oneTimePlanPOs));
        }
        // 2. 查询重复性会议并特殊处理
        List<MeetingPlanPO> recurringPlans = meetingPlanMapper.queryFutureRecurringPlans(query);
        for (MeetingPlanPO po : recurringPlans) {
            //重复实例展开
            List<MeetingPlanPO> futureInstances = expandFutureRecurringInstances(po, query);
            if (CollUtil.isNotEmpty(futureInstances)) {
                result.addAll(meetingPlanAssembler.PO2DTO(futureInstances));
            }

        }
        return result;
    }

    /**
     * 展开历史重复性会议实例
     */
    private List<MeetingPlanPO> expandFutureRecurringInstances(MeetingPlanPO po, MeetingPlanCalendarQuery query) {
        List<MeetingPlanPO> result = new ArrayList<>();
        String cron = po.getCron();

        LocalDateTime nextTime = LocalDateTime.now();
        while (true) {
            nextTime = CronUtils.nextExecutionTime(cron, nextTime);
            if (nextTime.isAfter(query.getEndDate())) {
                break;
            }
            if (ObjUtil.isNotNull(po.getRecurrenceEndDate()) && nextTime.isAfter(po.getRecurrenceEndDate())) {
                break;
            }
            MeetingPlanPO planPO = new MeetingPlanPO();
            BeanUtils.copyProperties(po, planPO);
            planPO.setPlannedStartTime(nextTime);
            planPO.setPlannedEndTime(nextTime.plusMinutes(po.getPlannedDuration()));
            result.add(planPO);
        }
        return result;
    }

    /**
     * 创建子查询对象
     */
    private MeetingPlanCalendarQuery createSubQuery(MeetingPlanCalendarQuery originalQuery, LocalDateTime startDate, LocalDateTime endDate) {
        MeetingPlanCalendarQuery subQuery = new MeetingPlanCalendarQuery();
        subQuery.setStartDate(startDate);
        subQuery.setEndDate(endDate);
        subQuery.setTagIds(originalQuery.getTagIds());
        return subQuery;
    }

    private List<MeetingPlanDTO> arrangeData(List<MeetingPlanDTO> meetingPlanDTOS) {
        //会议标准名称
        List<Long> standardIds = meetingPlanDTOS.stream().map(MeetingPlanDTO::getMeetingStandardId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(standardIds)) {
            List<MeetingStandardPO> standardPOS = meetingStandardMapper.selectBatchIds(standardIds);
            if (CollUtil.isNotEmpty(standardPOS)) {
                Map<Long, MeetingStandardPO> standardMap = standardPOS.stream().collect(Collectors.toMap(MeetingStandardPO::getId, standard -> standard));
                meetingPlanDTOS.forEach(plan -> {
                    MeetingStandardPO standardPO = standardMap.get(plan.getMeetingStandardId());
                    plan.setMeetingStandard(meetingStandardAssembler.PO2DTO(standardPO));
                });
            }
        }

        //会议标签
        List<Long> tagIds = meetingPlanDTOS.stream()
                .filter(plan -> CollUtil.isNotEmpty(plan.getTagIds()))
                .flatMap(dto -> dto.getTagIds().stream())
                .distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tagIds)) {
            List<MeetingTagPO> tagPOS = meetingTagMapper.selectBatchIds(tagIds);
            Map<Long, MeetingTagPO> tagMap = tagPOS.stream().collect(Collectors.toMap(MeetingTagPO::getId, tag -> tag));
            meetingPlanDTOS.forEach(plan -> {
                if (plan.getTagIds() != null && !plan.getTagIds().isEmpty()) {
                    List<MeetingTagDTO> tags = plan.getTagIds().stream()
                            .map(tag -> meetingTagAssembler.PO2DTO(tagMap.get(tag)))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    plan.setTags(tags);
                }
            });
        }

        //参会人员信息
        List<String> attendeeIds = meetingPlanDTOS.stream().filter(dto -> CollUtil.isNotEmpty(dto.getAttendees()))
                .flatMap(dto -> dto.getAttendees().stream())
                .distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(attendeeIds)) {
            // 批量获取用户信息
            Map<String, FSUserInfoDTO> userInfoMap = userInfoQueryService.getUserInfos(attendeeIds);

            // 为每个会议规划填充发起人详细信息
            meetingPlanDTOS.forEach(plan -> {
                if (plan.getAttendees() != null && !plan.getAttendees().isEmpty()) {
                    List<FSUserInfoDTO> attendeeDetails = plan.getAttendees().stream()
                            .map(userInfoMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    plan.setAttendeeDetails(attendeeDetails);
                }
            });
        }

        return meetingPlanDTOS;
    }

    /**
     * 获取会议规划数据洞察
     */
    public MeetingPlanInsightDTO getMeetingPlanInsight(MeetingPlanInsightQuery query) {
        try {
            log.info("开始获取会议规划数据洞察，开始时间：{}，结束时间：{}，是否使用模拟数据：{}",
                    query.getStartTime(),
                    query.getEndTime(),
                    query.getUseMockData());

            // 如果需要模拟数据，则直接返回模拟数据
            if (query.getUseMockData() != null && query.getUseMockData()) {
                return generateMockMeetingPlanInsight();
            }

            // 1. 查询时间范围内的所有会议规划
            LambdaQueryWrapper<MeetingPlanPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(MeetingPlanPO::getPlannedStartTime, query.getStartTime())
                    .le(MeetingPlanPO::getPlannedStartTime, query.getEndTime())
                    .orderByDesc(MeetingPlanPO::getPlannedStartTime);

            // 如果指定了部门ID，获取部门及其所有子部门的ID列表
            if (query.getDepartmentId() != null) {
                List<String> departmentIds = getDepartmentAndChildrenIds(query.getDepartmentId());
                query.setDepartmentIds(departmentIds);
            }

            // 添加部门筛选条件
            if (query.getDepartmentIds() != null && !query.getDepartmentIds().isEmpty()) {
                queryWrapper.in(MeetingPlanPO::getDepartmentId, query.getDepartmentIds());
            } else if (query.getDepartmentId() != null) {
                queryWrapper.eq(MeetingPlanPO::getDepartmentId, query.getDepartmentId());
            }

            List<MeetingPlanPO> plans = meetingPlanMapper.selectList(queryWrapper);

            // 2. 计算健康度百分比
            Double healthRate = calculateHealthRate(plans);

            // 3. 统计标签使用情况
            List<TagUsageDTO> tagUsage = calculateTagUsage(plans);

            // 4. 构建返回结果
            MeetingPlanInsightDTO result = new MeetingPlanInsightDTO();
            result.setHealthRate(healthRate);
            result.setTagUsage(tagUsage);

            log.info("会议规划数据洞察完成，健康度：{}%，标签使用数量：{}",
                    result.getHealthRate(),
                    result.getTagUsage() != null ? result.getTagUsage().size() : 0);

            return result;
        } catch (Exception e) {
            log.error("获取会议规划数据洞察失败", e);
            throw new BusinessException("获取会议规划数据洞察失败：" + e.getMessage());
        }
    }

    /**
     * 计算健康度百分比
     */
    private Double calculateHealthRate(List<MeetingPlanPO> plans) {
        long totalPlans = plans.size();
        long overduePlans = plans.stream()
                .mapToLong(p -> p.getStatus() == MeetingPlanStatusEnum.OVERDUE ? 1 : 0)
                .sum();
        long notStartedPlans = plans.stream()
                .mapToLong(p -> p.getStatus() == MeetingPlanStatusEnum.NOT_STARTED ? 1 : 0)
                .sum();

        long nonNotStartedPlans = totalPlans - notStartedPlans;
        if (nonNotStartedPlans == 0) {
            return 0.0;
        }

        return (1.0 - (double) overduePlans / nonNotStartedPlans) * 100;
    }

    /**
     * 计算标签使用统计
     */
    private List<TagUsageDTO> calculateTagUsage(List<MeetingPlanPO> plans) {
        // 统计标签使用次数
        Map<Long, Long> tagCount = new HashMap<>();
        long totalUsages = 0;

        for (MeetingPlanPO plan : plans) {
            if (plan.getTagIds() != null && !plan.getTagIds().isEmpty()) {
                for (Long tagId : plan.getTagIds()) {
                    tagCount.merge(tagId, 1L, Long::sum);
                    totalUsages++;
                }
            }
        }

        if (tagCount.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取标签信息
        List<Long> tagIds = new ArrayList<>(tagCount.keySet());
        List<MeetingTagPO> tags = meetingTagMapper.selectBatchIds(tagIds);
        Map<Long, MeetingTagPO> tagMap = tags.stream()
                .collect(Collectors.toMap(MeetingTagPO::getId, tag -> tag));

        // 构建结果
        long finalTotalUsages = totalUsages;
        return tagCount.entrySet().stream()
                .map(entry -> {
                    Long tagId = entry.getKey();
                    Long count = entry.getValue();
                    MeetingTagPO tag = tagMap.get(tagId);

                    TagUsageDTO dto = new TagUsageDTO();
                    dto.setTagId(tagId);
                    dto.setTagName(tag != null ? tag.getName() : "未知标签");
                    dto.setTagColor(tag != null ? tag.getColor() : "#CCCCCC");
                    dto.setCount(count);
                    dto.setPercentage(finalTotalUsages > 0 ? (double) count / finalTotalUsages * 100 : 0.0);

                    return dto;
                })
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .collect(Collectors.toList());
    }

    /**
     * 生成模拟会议规划数据洞察
     */
    private MeetingPlanInsightDTO generateMockMeetingPlanInsight() {
        log.info("开始生成模拟会议规划数据洞察");

        MeetingPlanInsightDTO mockData = new MeetingPlanInsightDTO();

        // 1. 健康度百分比 - 模拟一个较高的健康度
        mockData.setHealthRate(85.6);

        // 2. 标签使用统计（用于饼状图）
        List<TagUsageDTO> tagUsage = new ArrayList<>();
        tagUsage.add(createTagUsage(1L, "战略经营类", "#FF5733", 25L, 35.2));
        tagUsage.add(createTagUsage(2L, "客户与市场", "#33FF57", 18L, 25.4));
        tagUsage.add(createTagUsage(3L, "产品研发", "#3357FF", 15L, 21.1));
        tagUsage.add(createTagUsage(4L, "人力资源", "#FF33E9", 8L, 11.3));
        tagUsage.add(createTagUsage(5L, "财务分析", "#FFC733", 5L, 7.0));
        mockData.setTagUsage(tagUsage);

        log.info("已生成模拟会议规划数据洞察，健康度：{}%，标签使用数量：{}",
                mockData.getHealthRate(),
                mockData.getTagUsage().size());

        return mockData;
    }

    /**
     * 创建标签使用统计DTO
     */
    private TagUsageDTO createTagUsage(Long tagId, String tagName, String tagColor, Long count, Double percentage) {
        TagUsageDTO dto = new TagUsageDTO();
        dto.setTagId(tagId);
        dto.setTagName(tagName);
        dto.setTagColor(tagColor);
        dto.setCount(count);
        dto.setPercentage(percentage);
        return dto;
    }


    /**
     * 批量获取部门详情并填充到DTO中
     */
    private void fillDepartmentDetails(List<MeetingPlanDTO> meetingPlanDTOS) {
        if (CollUtil.isEmpty(meetingPlanDTOS)) {
            return;
        }

        // 提取所有的departmentId
        Set<String> departmentIds = meetingPlanDTOS.stream()
                .map(MeetingPlanDTO::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(departmentIds)) {
            return;
        }

        try {
            // 将Long类型的departmentId转换为String类型
            List<String> departmentIdStrings = departmentIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            // 调用飞书接口获取部门详情
            List<Department> departments = tenantFeishuAppClient.getContactService().getDepartmentBatch(departmentIdStrings);

            // 构建部门ID到部门详情的映射
            Map<Long, DepartmentDetailDTO> departmentMap = departments.stream()
                    .collect(Collectors.toMap(
                            dept -> Long.valueOf(dept.getOpenDepartmentId()),
                            this::convertToDepartmentDetailDTO,
                            (existing, replacement) -> existing
                    ));

            // 填充部门详情到DTO中
            meetingPlanDTOS.forEach(dto -> {
                if (dto.getDepartmentId() != null) {
                    DepartmentDetailDTO departmentDetail = departmentMap.get(dto.getDepartmentId());
                    dto.setDepartmentDetail(departmentDetail);
                }
            });

        } catch (Exception e) {
            log.error("获取部门详情失败", e);
            // 如果获取部门详情失败，不影响主流程，只是部门详情为空
        }
    }

    /**
     * 将飞书Department对象转换为DepartmentDetailDTO
     */
    private DepartmentDetailDTO convertToDepartmentDetailDTO(Department department) {
        if (department == null) {
            return null;
        }

        DepartmentDetailDTO dto = new DepartmentDetailDTO();
        dto.setDepartmentId(department.getDepartmentId());
        dto.setName(department.getName());
        dto.setNameEn(String.valueOf(department.getI18nName()));
        dto.setParentDepartmentId(department.getParentDepartmentId());
        dto.setManagerId(department.getLeaderUserId());
        return dto;
    }

    /**
     * 获取部门及其所有子部门的ID列表
     */
    private List<String> getDepartmentAndChildrenIds(String departmentId) {
        if (departmentId == null) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        result.add(departmentId);

        try {
            // 获取子部门列表
            List<Department> children = tenantFeishuAppClient.getContactService().getDepartmentChildren(departmentId);

            // 递归获取所有子部门的ID
            for (Department child : children) {
                String childId = child.getDepartmentId();
                result.add(childId);

                // 递归获取子部门的子部门
                List<String> grandChildren = getDepartmentAndChildrenIds(childId);
                result.addAll(grandChildren);
            }
        } catch (Exception e) {
            log.warn("获取部门子部门失败，部门ID：{}", departmentId, e);
        }

        return result;
    }

}

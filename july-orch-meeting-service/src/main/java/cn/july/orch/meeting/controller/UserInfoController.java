package cn.july.orch.meeting.controller;

import cn.july.feishu.model.ContactSearchUserDTO;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.dto.ContactSearchUserRespDTO;
import cn.july.orch.meeting.service.DepartmentUserQueryService;
import cn.july.orch.meeting.service.TenantFeishuAppClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "用户信息")
@RestController
@RequestMapping("/user")
public class UserInfoController {

    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;
    @Resource
    private UserInfoAssembler userInfoAssembler;
    @Resource
    private DepartmentUserQueryService departmentUserQueryService;

    /**
     * 手机号获取飞书用户信息(方便测试用)
     * 支持离职员工
     *
     * @param telephone
     * @return
     */
    @GetMapping("/getUserInfo")
    public Object getUserInfo(@RequestParam("telephone") String telephone) {
        return Arrays.asList(tenantFeishuAppClient.getContactService().getOpenId(telephone).getUserList());
    }

    /**
     * 名称模糊查询飞书用户信息
     * 不支持搜索离职员工
     */
    @PostMapping("/searchUser")
    public ContactSearchUserRespDTO searchUser(@RequestBody ContactSearchUserDTO query) {
        return userInfoAssembler.resp2DTO(tenantFeishuAppClient.getContactService().searchUser(query));
    }

    /**
     * 批量获取多个部门下的所有用户（包含子部门）
     * 
     * @param departmentIds 部门ID列表
     * @return Map<部门ID, 该部门及其所有子部门下的用户OpenID列表>
     */
    @ApiOperation("批量获取部门用户")
    @PostMapping("/getUsersByDepartments")
    public Map<String, List<String>> getUsersByDepartments(
            @ApiParam(value = "部门ID列表", required = true) 
            @RequestBody List<String> departmentIds) {
        return departmentUserQueryService.getUsersByDepartments(departmentIds);
    }

    /**
     * 获取单个部门下的所有用户（包含子部门）
     * 
     * @param departmentId 部门ID
     * @return 该部门及其所有子部门下的用户OpenID列表
     */
    @ApiOperation("获取单个部门用户")
    @GetMapping("/getUsersByDepartment")
    public List<String> getUsersByDepartment(
            @ApiParam(value = "部门ID", required = true) 
            @RequestParam("departmentId") String departmentId) {
        return departmentUserQueryService.getUsersByDepartment(departmentId);
    }

    /**
     * 获取多个部门下所有用户的汇总列表（去重）
     * 
     * @param departmentIds 部门ID列表
     * @return 所有部门下的用户OpenID列表（已去重）
     */
    @ApiOperation("获取部门用户汇总列表")
    @PostMapping("/getAllUsersFromDepartments")
    public List<String> getAllUsersFromDepartments(
            @ApiParam(value = "部门ID列表", required = true) 
            @RequestBody List<String> departmentIds) {
        return departmentUserQueryService.getAllUsersFromDepartments(departmentIds);
    }
}

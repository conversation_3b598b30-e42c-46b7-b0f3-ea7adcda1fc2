package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.MeetingStartEndEventCommand;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.service.MeetingEvaluationActionService;
import cn.july.orch.meeting.service.NewMeetingActionService;
import cn.july.orch.meeting.service.NewMeetingQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议结束事件处理器
 * @date 2025-01-02
 */
@Slf4j
@Component
public class MeetingEndHandler implements CallbackHandler<MeetingStartEndEventCommand> {

    @Resource
    private NewMeetingQueryService newMeetingQueryService;
    @Resource
    private NewMeetingActionService newMeetingActionService;
    @Resource
    private MeetingEvaluationActionService evaluationActionService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingStartEndEventCommand fsCommand = JsonUtils.parse(meetingJson, MeetingStartEndEventCommand.class);
        MeetingStartEndEventCommand.Meeting endCommand = fsCommand.getMeeting();
        // 新会议同步
        newMeetingActionService.handleFeishuMeetingEndCallback(
                endCommand.getCalendarEventId(),
                endCommand.getEndTime()
        );

        // 会议结束后发送评价卡片
        try {
            // 根据calendarEventId获取新会议信息
            NewMeetingDTO newMeeting = newMeetingQueryService.findByFsCalendarEventId(endCommand.getCalendarEventId());
            if (newMeeting != null) {
                log.info("找到会议，会议ID：{}，会议名称：{}", newMeeting.getId(), newMeeting.getMeetingName());
                // 发送新会议系统的评价卡片
                evaluationActionService.sendNewMeetingEvaluationCards(newMeeting);
                log.info("新会议系统评价卡片发送成功，会议ID：{}", newMeeting.getId());
            } else {
                log.warn("未找到对应的会议，calendarEventId：{}", endCommand.getCalendarEventId());
            }
        } catch (Exception e) {
            log.error("发送会议评价卡片失败，calendarEventId：{}", endCommand.getCalendarEventId(), e);
        }
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_END;
    }
}

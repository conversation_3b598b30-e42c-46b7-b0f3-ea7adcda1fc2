package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会议基础统计DTO
 * @date 2025-01-24
 */
@Data
public class MeetingBasicStatisticsDTO {

    @ApiModelProperty("会议总数")
    private Long totalMeetings;

    @ApiModelProperty("会议总时长（小时）")
    private Double totalDuration;

    @ApiModelProperty("平均参会人数")
    private Double avgAttendeeCount;

    @ApiModelProperty("临时会议占比（百分比）")
    private Double tempMeetingRatio;
}

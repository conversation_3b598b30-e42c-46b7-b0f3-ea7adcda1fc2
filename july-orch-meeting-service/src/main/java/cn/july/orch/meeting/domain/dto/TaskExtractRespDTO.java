package cn.july.orch.meeting.domain.dto;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务提取响应DTO
 */
@Data
public class TaskExtractRespDTO {

    /**
     * 任务标题
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 责任人姓名
     */
    private String owner;

    /**
     * 截止日期
     */
    private String due;
    
    /**
     * 责任人信息列表（来自飞书用户搜索结果）
     */
    private List<ContactSearchUserRespDTO.User> ownerInfo;
}
package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务创建命令（统一POJO）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("任务创建命令")
public class TaskCreateCommand {

    @ApiModelProperty(value = "任务标题", required = true)
    @NotBlank(message = "任务标题不能为空")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty(value = "负责人OpenID", required = true)
    @NotBlank(message = "负责人OpenID不能为空")
    private String ownerOpenId;

    @ApiModelProperty(value = "负责人名称", required = true)
    @NotBlank(message = "负责人名称不能为空")
    private String ownerName;

    @ApiModelProperty(value = "优先级", required = true)
    @NotNull(message = "优先级不能为空")
    private TaskPriorityEnum priority;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty(value = "任务清单ID", required = true, notes = "任务必须归属任务清单")
    @NotNull(message = "任务清单ID不能为空")
    private Long taskListId;

    @ApiModelProperty("父任务ID（用于创建子任务）")
    private Long parentId;

    @ApiModelProperty("关联的会议ID（可选）")
    private Long meetingId;

    @ApiModelProperty("附件key列表（可选）")
    private List<String> attachmentKeys;
}
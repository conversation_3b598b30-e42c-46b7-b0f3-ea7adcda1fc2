package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.feishu.model.ContactSearchUserDTO;
import cn.july.feishu.model.ContactSearchUserResponse;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.dto.ContactSearchUserRespDTO;
import cn.july.orch.meeting.domain.dto.TaskExtractRespDTO;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 任务提取服务
 */
@Slf4j
@Service
public class TaskExtractService {

    @Resource
    private AgentInvokeService agentInvokeService;
    
    @Resource
    private TenantFeishuAppClient tenantFeishuAppClient;
    
    @Resource
    private UserInfoAssembler userInfoAssembler;
    
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 提取任务信息
     * 
     * @param inputText 输入文本
     * @return 任务信息
     */
    public TaskExtractRespDTO extractTask(String inputText) {
        log.info("开始提取任务信息，输入文本：{}", inputText);
        
        try {
            // 调用智能体服务提取任务信息（非流式）
            AgentCompleteRespDTO response = agentInvokeService.invokeTextGetAnswer(
                AgentConstants.TASK_EXTRACT_APP_ID,
                inputText,
                null,
                AgentConstants.EXTRACT_TASK_AUTHORIZATION
            );
            
            if (response == null || response.getAnswer() == null) {
                throw new BusinessException("智能体返回结果为空");
            }
            
            // 解析智能体返回的JSON
            TaskExtractRespDTO taskInfo = objectMapper.readValue(response.getAnswer(), TaskExtractRespDTO.class);
            
            // 如果有责任人信息，查询飞书用户信息
            if (taskInfo.getOwner() != null && !taskInfo.getOwner().trim().isEmpty()) {
                ContactSearchUserRespDTO userInfo = searchFeishuUser(taskInfo.getOwner());
                taskInfo.setOwnerInfo(userInfo.getUsers());
                log.info("查询到责任人信息：{}", userInfo);
            }
            
            log.info("任务信息提取完成：{}", taskInfo);
            return taskInfo;
        } catch (Exception e) {
            log.error("提取任务信息失败，输入文本：{}", inputText, e);
            throw new BusinessException("提取任务信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询飞书用户信息
     * 
     * @param userName 用户姓名
     * @return 用户信息
     */
    private ContactSearchUserRespDTO searchFeishuUser(String userName) {
        try {
            ContactSearchUserDTO query = ContactSearchUserDTO.builder()
                .query(userName)
                .pageSize(10)
                .build();
            
            ContactSearchUserResponse response = tenantFeishuAppClient.getContactService().searchUser(query);
            return userInfoAssembler.resp2DTO(response);
        } catch (Exception e) {
            log.error("查询飞书用户信息失败，用户名：{}", userName, e);
            throw new BusinessException("查询飞书用户信息失败：" + e.getMessage());
        }
    }
}
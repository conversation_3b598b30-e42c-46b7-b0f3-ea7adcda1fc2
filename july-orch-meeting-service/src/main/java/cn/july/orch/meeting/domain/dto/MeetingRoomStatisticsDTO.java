package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 会议室使用统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "会议室使用统计信息")
public class MeetingRoomStatisticsDTO {

    @ApiModelProperty(value = "会议室ID")
    private Long meetingRoomId;

    @ApiModelProperty(value = "会议室名称")
    private String meetingRoomName;

    @ApiModelProperty(value = "本月使用次数")
    private Integer usageCount;

    @ApiModelProperty(value = "总使用时长(小时)")
    private Double totalUsageHours;

    @ApiModelProperty(value = "平均使用时长(小时)")
    private Double averageUsageHours;

    @ApiModelProperty(value = "每日使用统计")
    private List<DailyStatistics> dailyStatistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyStatistics {
        @ApiModelProperty(value = "日期", example = "2025-08-01")
        private String date;

        @ApiModelProperty(value = "使用次数")
        private Integer usageCount;

        @ApiModelProperty(value = "使用时长(小时)")
        private Double usageHours;
    }
}
package cn.july.orch.meeting.domain.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划更新Command
 * @date 2025-01-24
 */
@Data
public class MeetingPlanUpdateCommand {

    @ApiModelProperty(value = "会议规划ID", required = true)
    @NotNull(message = "会议规划ID不能为空")
    private Long id;

    @ApiModelProperty(value = "会议规划名称", required = true)
    @NotBlank(message = "会议规划名称不能为空")
    private String planName;

    @ApiModelProperty(value = "会议规划描述")
    private String planDescription;

    @ApiModelProperty(value = "会议标准ID", required = true)
    @NotNull(message = "会议标准ID不能为空")
    private Long meetingStandardId;

    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty(value = "提醒人员列表")
    private List<String> attendees;

    @ApiModelProperty(value = "cron")
    private String cron;

    @ApiModelProperty(value = "重复结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime recurrenceEndDate;

    @ApiModelProperty("关联的标签ID列表")
    private List<Long> tagIds;

    @ApiModelProperty(value = "会前文档文件ID列表")
    private List<String> preMeetingDocuments;
}

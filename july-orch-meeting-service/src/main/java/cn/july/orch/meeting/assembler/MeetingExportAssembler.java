package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.dto.MeetingExportDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 会议导出装配器
 */
@Component
public class MeetingExportAssembler {
    
    /**
     * 转换为导出数据
     */
    public List<MeetingExportDTO> convertToExportData(List<NewMeetingListDTO> meetingList) {
        return meetingList.stream()
                .map(this::convertToExportDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 单个会议转换为导出DTO
     */
    public MeetingExportDTO convertToExportDTO(NewMeetingListDTO meeting) {
        if (meeting == null) {
            return null;
        }
        
        MeetingExportDTO dto = new MeetingExportDTO();
        dto.setMeetingName(meeting.getMeetingName());
        dto.setStartTime(formatDateTime(meeting.getStartTime()));
        dto.setEndTime(formatDateTime(meeting.getEndTime()));
        dto.setStatus(meeting.getStatus() != null ? meeting.getStatus().getDesc() : "");
        dto.setMeetingLocation(meeting.getMeetingLocation());
        dto.setAttendeeCount(meeting.getAttendeeCount());
        dto.setHostUserName(meeting.getHostUserDetail() != null ? meeting.getHostUserDetail().getName() : "");
        dto.setRecorderUserName(meeting.getRecorderUserDetail() != null ? meeting.getRecorderUserDetail().getName() : "");
        dto.setMeetingUrl(meeting.getMeetingUrl());
        dto.setMinuteUrl(meeting.getMinuteUrl());
        dto.setCreateUserName(meeting.getCreateUserName());
        dto.setCreateTime(formatDateTime(meeting.getCreateTime()));
        dto.setActualStartTime(formatDateTime(meeting.getActualStartTime()));
        dto.setActualEndTime(formatDateTime(meeting.getActualEndTime()));
        dto.setPlanName(meeting.getPlanName());
        dto.setDepartmentName(meeting.getDepartmentDetail() != null ? meeting.getDepartmentDetail().getName() : "");
        
        return dto;
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(java.time.LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}

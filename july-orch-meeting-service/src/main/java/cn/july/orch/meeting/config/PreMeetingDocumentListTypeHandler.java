package cn.july.orch.meeting.config;

import cn.july.orch.meeting.domain.dto.PreMeetingDocumentDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会前文档列表类型处理器
 */
@Slf4j
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR, JdbcType.OTHER})
@MappedTypes(List.class)
public class PreMeetingDocumentListTypeHandler extends BaseTypeHandler<List<PreMeetingDocumentDTO>> {

    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<PreMeetingDocumentDTO> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
                log.debug("序列化会前文档列表成功，内容：{}", json);
            } catch (JsonProcessingException e) {
                log.error("会前文档列表序列化失败", e);
                ps.setString(i, null);
            }
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public List<PreMeetingDocumentDTO> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        if (value == null) {
            Object obj = rs.getObject(columnName);
            value = obj != null ? String.valueOf(obj) : null;
        }
        return parsePreMeetingDocumentList(value);
    }

    @Override
    public List<PreMeetingDocumentDTO> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        if (value == null) {
            Object obj = rs.getObject(columnIndex);
            value = obj != null ? String.valueOf(obj) : null;
        }
        return parsePreMeetingDocumentList(value);
    }

    @Override
    public List<PreMeetingDocumentDTO> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parsePreMeetingDocumentList(value);
    }

    private List<PreMeetingDocumentDTO> parsePreMeetingDocumentList(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<PreMeetingDocumentDTO> result = objectMapper.readValue(value, new TypeReference<List<PreMeetingDocumentDTO>>() {});
            log.debug("反序列化会前文档列表成功，内容：{}", result);
            return result != null ? result : new ArrayList<>();
        } catch (JsonProcessingException e) {
            log.error("会前文档列表反序列化失败，原始值：{}", value, e);
            return new ArrayList<>();
        }
    }
}
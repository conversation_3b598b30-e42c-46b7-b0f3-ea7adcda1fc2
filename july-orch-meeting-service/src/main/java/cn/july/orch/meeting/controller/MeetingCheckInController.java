package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.service.MeetingCheckInDataService;
import cn.july.orch.meeting.service.MeetingCheckInService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 会议签到控制器
 * @date 2025-01-24
 */
@Slf4j
@Api(tags = "会议签到")
@RestController
@RequestMapping("/meeting/checkin")
@RequiredArgsConstructor
public class MeetingCheckInController {

    private final MeetingCheckInDataService meetingCheckInDataService;
    private final MeetingCheckInService meetingCheckInService;

    /**
     * 用户签到
     */
    @PostMapping("/checkin")
    @ApiOperation("用户签到")
    public String checkIn(@ApiParam("签到码") @RequestParam String checkinCode,
                          @ApiParam("用户open_id") @RequestParam String attendeeOpenId) {
        log.info("用户签到请求: checkinCode={}, attendeeOpenId={}", checkinCode, attendeeOpenId);
        return meetingCheckInDataService.checkIn(checkinCode, attendeeOpenId);
    }

    /**
     * 根据会议ID手动触发签到通知发送
     */
    @PostMapping("/send/{meetingId}")
    @ApiOperation("根据会议ID手动触发签到通知发送")
    public String sendCheckInNotificationByMeetingId(@PathVariable Long meetingId) {
        log.info("手动触发会议签到通知: meetingId={}", meetingId);
        return meetingCheckInService.manualTriggerCheckIn(meetingId);
    }
}

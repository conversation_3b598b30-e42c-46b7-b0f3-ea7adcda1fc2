package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.dto.MeetingTranscriptDTO;
import cn.july.orch.meeting.domain.po.MeetingMinutePO;
import cn.july.orch.meeting.mapper.MeetingMinuteMapper;
import cn.july.orch.meeting.service.TranscriptParserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 文字记录控制器
 * @date 2025-08-26
 */
@Api(tags = "文字记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/minute/transcript")
public class TranscriptController {

    private final TranscriptParserService transcriptParserService;
    private final MeetingMinuteMapper meetingMinuteMapper;

    /**
     * 接收纯文本格式的会议纪要，解析后返回结构化的JSON。
     * @param rawText 原始文本，通过请求体传入
     * @return 结构化的 MeetingTranscript 对象
     */
    @PostMapping(value = "/parse-transcript", consumes = MediaType.TEXT_PLAIN_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation("解析会议纪要文本")
    public MeetingTranscriptDTO parseTranscript(@RequestBody String rawText) {
        return transcriptParserService.parse(rawText);
    }

    /**
     * 根据会议ID查询会议纪要文本并解析
     * @param meetingId 会议ID
     * @return 解析后的结构化会议纪要对象
     */
    @GetMapping("/parse-by-meeting/{meetingId}")
    @ApiOperation("根据会议ID解析会议纪要")
    public MeetingTranscriptDTO parseTranscriptByMeetingId(
            @ApiParam(value = "会议ID", required = true) @PathVariable("meetingId") Long meetingId) {
        
        // 1. 根据会议ID查询会议纪要文本
        MeetingMinutePO meetingMinute = meetingMinuteMapper.selectByMeetingId(meetingId);
        
        if (meetingMinute == null || meetingMinute.getMinuteText() == null || meetingMinute.getMinuteText().trim().isEmpty()) {
            // 如果没有找到会议纪要或纪要文本为空，返回空的结构化对象
            return new MeetingTranscriptDTO();
        }
        
        // 2. 调用TranscriptParserService解析纪要文本
        return transcriptParserService.parse(meetingMinute.getMinuteText());
    }

}

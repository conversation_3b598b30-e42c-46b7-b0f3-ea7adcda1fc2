package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.MeetingTranscriptDTO;
import cn.july.orch.meeting.domain.dto.TranscriptItemDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 文字记录转换服务
 * @date 2025-08-26
 */
@Service
public class TranscriptParserService {

    // 格式一: "说话人 1 - ... (20) 00:00"
    private static final Pattern SPEAKER_LINE_V1_PATTERN = Pattern.compile(
            "^(说话人\\s+\\d+.*?)(\\d{2}:\\d{2})\\s*$", // <--- 已修正: 添加 \\s* 以处理尾随空格
            Pattern.MULTILINE
    );

    // 格式三: "宋宁 00:00"
    private static final Pattern SPEAKER_LINE_V3_PATTERN = Pattern.compile(
            "^(.+?)\\s+(\\d{2}:\\d{2})\\s*$", // <--- 已修正: 添加 \\s* 以处理尾随空格
            Pattern.MULTILINE
    );

    private static final String KEYWORDS_PREFIX = "关键词:";
    private static final String TRANSCRIPT_PREFIX_V1_V3 = "文字记录:";

    /**
     * 解析原始文本的统一入口
     * @param rawText 完整的会议纪要文本
     * @return 结构化的 MeetingTranscriptDTO 对象
     */
    public MeetingTranscriptDTO parse(String rawText) {
        if (rawText == null || rawText.trim().isEmpty()) {
            return new MeetingTranscriptDTO();
        }

        MeetingTranscriptDTO meetingTranscriptDTO = new MeetingTranscriptDTO();
        String[] lines = rawText.trim().split("\\r?\\n");

        int keywordsPrefixIndex = findKeywordsPrefixIndex(lines);
        if (keywordsPrefixIndex == -1) return meetingTranscriptDTO;

        int keywordsLineIndex = keywordsPrefixIndex + 1;
        int bodyStartIndex = keywordsLineIndex + 1;

        String[] headerLines = Arrays.copyOfRange(lines, 0, keywordsLineIndex + 1);
        parseHeader(headerLines, meetingTranscriptDTO);

        if (bodyStartIndex >= lines.length) {
            meetingTranscriptDTO.setTranscript(new ArrayList<>());
            return meetingTranscriptDTO;
        }

        String[] bodyLines = Arrays.copyOfRange(lines, bodyStartIndex, lines.length);
        String body = String.join("\n", bodyLines).trim();

        // 智能调度逻辑
        if (body.startsWith(TRANSCRIPT_PREFIX_V1_V3)) {
            body = body.substring(TRANSCRIPT_PREFIX_V1_V3.length()).trim();
            // 使用正则表达式直接检测，比字符串分割更健壮
            Matcher v1Matcher = SPEAKER_LINE_V1_PATTERN.matcher(body);
            if (v1Matcher.find()) {
                meetingTranscriptDTO.setTranscript(parseTranscriptBodyV1(body));
            } else {
                meetingTranscriptDTO.setTranscript(parseTranscriptBodyV3(body));
            }
        } else {
            meetingTranscriptDTO.setTranscript(parseTranscriptBodyV2(body));
        }

        return meetingTranscriptDTO;
    }

    private int findKeywordsPrefixIndex(String[] lines) {
        for (int i = 0; i < lines.length; i++) {
            if (lines[i].trim().equals(KEYWORDS_PREFIX)) {
                return i;
            }
        }
        return -1;
    }

    private void parseHeader(String[] headerLines, MeetingTranscriptDTO meetingTranscriptDTO) {
        if (headerLines.length > 0 && !headerLines[0].trim().isEmpty()) {
            String[] dateTimeParts = headerLines[0].split("\\|");
            if (dateTimeParts.length > 0) meetingTranscriptDTO.setMeetingDate(dateTimeParts[0].trim());
            if (dateTimeParts.length > 1) meetingTranscriptDTO.setDuration(dateTimeParts[1].trim());
        }

        for (int i = 0; i < headerLines.length; i++) {
            if (headerLines[i].trim().equals(KEYWORDS_PREFIX)) {
                if (i + 1 < headerLines.length) {
                    String keywordsStr = headerLines[i + 1].trim();
                    if (!keywordsStr.isEmpty()) {
                        meetingTranscriptDTO.setKeywords(Arrays.stream(keywordsStr.split("、")).map(String::trim).collect(Collectors.toList()));
                    } else {
                        meetingTranscriptDTO.setKeywords(new ArrayList<>());
                    }
                } else {
                    meetingTranscriptDTO.setKeywords(new ArrayList<>());
                }
                return;
            }
        }
    }

    private List<TranscriptItemDTO> parseTranscriptBodyV1(String body) {
        return parseGenericTranscriptBody(body, SPEAKER_LINE_V1_PATTERN);
    }

    private List<TranscriptItemDTO> parseTranscriptBodyV3(String body) {
        return parseGenericTranscriptBody(body, SPEAKER_LINE_V3_PATTERN);
    }

    private List<TranscriptItemDTO> parseGenericTranscriptBody(String body, Pattern pattern) {
        List<TranscriptItemDTO> transcriptItems = new ArrayList<>();
        Matcher matcher = pattern.matcher(body);

        List<Integer> startIndices = new ArrayList<>();
        List<String> speakerLines = new ArrayList<>();

        while (matcher.find()) {
            startIndices.add(matcher.start());
            speakerLines.add(matcher.group(0));
        }

        for (int i = 0; i < startIndices.size(); i++) {
            int contentStart = startIndices.get(i) + speakerLines.get(i).length();
            int contentEnd = (i + 1 < startIndices.size()) ? startIndices.get(i + 1) : body.length();
            String content = body.substring(contentStart, contentEnd).trim();

            Matcher lineMatcher = pattern.matcher(speakerLines.get(i));
            if (lineMatcher.find()) {
                String speakerName = lineMatcher.group(1).trim();
                String timestamp = lineMatcher.group(2).trim();

                TranscriptItemDTO item = new TranscriptItemDTO();
                item.setSpeakerName(speakerName);
                item.setTimestamp(timestamp);
                item.setContent(content);
                transcriptItems.add(item);
            }
        }
        return transcriptItems;
    }

    private List<TranscriptItemDTO> parseTranscriptBodyV2(String body) {
        List<TranscriptItemDTO> transcriptItems = new ArrayList<>();
        if (body == null || body.isEmpty()) return transcriptItems;

        String[] utteranceBlocks = body.trim().split("\\n\\s*\\n+");
        for (String block : utteranceBlocks) {
            if (block.trim().isEmpty()) continue;
            String[] lines = block.trim().split("\\n", 2);
            if (lines.length > 0) {
                String speakerName = lines[0].trim();
                String content = (lines.length > 1) ? lines[1].trim() : "";
                TranscriptItemDTO item = new TranscriptItemDTO();
                item.setSpeakerName(speakerName);
                item.setContent(content);
                transcriptItems.add(item);
            }
        }
        return transcriptItems;
    }
}

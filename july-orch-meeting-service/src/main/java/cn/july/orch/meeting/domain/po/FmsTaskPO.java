package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.orch.meeting.enums.FmsTaskStatusEnum;
import cn.july.orch.meeting.enums.FmsTaskTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description FMS任务PO
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName("fms_task")
public class FmsTaskPO {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;
    
    /**
     * 任务代码
     */
    @TableField("task_code")
    private String taskCode;
    
    /**
     * 任务类型：1-上传，2-下载
     */
    @TableField("task_type")
    private FmsTaskTypeEnum taskType;
    
    /**
     * 任务状态：0-未开始，1-执行中，2-成功，3-失败
     */
    @TableField("task_status")
    private FmsTaskStatusEnum taskStatus;
    
    /**
     * 任务参数（JSON格式）
     */
    @TableField("task_params")
    private String taskParams;
    
    /**
     * 文件ID
     */
    @TableField("file_id")
    private String fileId;
    
    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;
    
    /**
     * 文件URL
     */
    @TableField("file_url")
    private String fileUrl;
    
    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;
    
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;
    
    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    
    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    /**
     * 删除标记
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;
}
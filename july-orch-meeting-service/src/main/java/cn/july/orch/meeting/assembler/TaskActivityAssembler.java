package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.TaskActivityInfo;
import cn.july.orch.meeting.domain.po.TaskActivityPO;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务动态数据转换器
 */
@Component
public class TaskActivityAssembler {

    /**
     * TaskActivityPO转TaskActivityInfo
     *
     * @param taskActivityPO 任务动态PO
     * @return TaskActivityInfo
     */
    public TaskActivityInfo toTaskActivityInfo(TaskActivityPO taskActivityPO) {
        if (taskActivityPO == null) {
            return null;
        }

        TaskActivityInfo taskActivityInfo = new TaskActivityInfo();
        BeanUtils.copyProperties(taskActivityPO, taskActivityInfo);
        
        // 转换枚举类型
        if (taskActivityPO.getActivityType() != null) {
            taskActivityInfo.setActivityType(TaskActivityTypeEnum.fromCode(taskActivityPO.getActivityType()));
        }
        
        return taskActivityInfo;
    }

    /**
     * TaskActivityInfo转TaskActivityPO
     *
     * @param taskActivityInfo 任务动态Info
     * @return TaskActivityPO
     */
    public TaskActivityPO toTaskActivityPO(TaskActivityInfo taskActivityInfo) {
        if (taskActivityInfo == null) {
            return null;
        }

        TaskActivityPO taskActivityPO = new TaskActivityPO();
        BeanUtils.copyProperties(taskActivityInfo, taskActivityPO);
        
        // 转换枚举类型
        if (taskActivityInfo.getActivityType() != null) {
            taskActivityPO.setActivityType(taskActivityInfo.getActivityType().getCode());
        }
        
        return taskActivityPO;
    }

    /**
     * TaskActivityPO列表转TaskActivityInfo列表
     *
     * @param taskActivityPOList 任务动态PO列表
     * @return TaskActivityInfo列表
     */
    public List<TaskActivityInfo> toTaskActivityInfoList(List<TaskActivityPO> taskActivityPOList) {
        if (taskActivityPOList == null || taskActivityPOList.isEmpty()) {
            return null;
        }

        return taskActivityPOList.stream()
                .map(this::toTaskActivityInfo)
                .collect(Collectors.toList());
    }

    /**
     * TaskActivityInfo列表转TaskActivityPO列表
     *
     * @param taskActivityInfoList 任务动态Info列表
     * @return TaskActivityPO列表
     */
    public List<TaskActivityPO> toTaskActivityPOList(List<TaskActivityInfo> taskActivityInfoList) {
        if (taskActivityInfoList == null || taskActivityInfoList.isEmpty()) {
            return null;
        }

        return taskActivityInfoList.stream()
                .map(this::toTaskActivityPO)
                .collect(Collectors.toList());
    }
}
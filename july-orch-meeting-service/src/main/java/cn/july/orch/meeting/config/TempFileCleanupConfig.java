package cn.july.orch.meeting.config;

import cn.july.orch.meeting.service.AiDocumentSummaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 临时文件清理配置
 */
@Slf4j
@Configuration
@EnableScheduling
public class TempFileCleanupConfig {

    @Resource
    private AiDocumentSummaryService aiDocumentSummaryService;

    /**
     * 每小时清理一次临时文件
     * 清理超过1小时的临时文件
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void cleanupTempFiles() {
        log.info("开始清理临时文件");
        try {
            aiDocumentSummaryService.cleanupTempDirectory();
            log.info("临时文件清理完成");
        } catch (Exception e) {
            log.error("清理临时文件时发生异常", e);
        }
    }
}
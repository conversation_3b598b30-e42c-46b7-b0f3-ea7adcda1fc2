package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.dto.MeetingDetailDTO;
import cn.july.orch.meeting.service.MeetingDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会议详情控制器
 *
 * <AUTHOR> Assistant
 */
@Api(tags = "会议详情")
@RestController
@RequestMapping("/meeting/detail")
@RequiredArgsConstructor
public class MeetingDetailController {

    private final MeetingDetailService meetingDetailService;

    @GetMapping("/{meetingId}")
    @ApiOperation("获取会议详情")
    public MeetingDetailDTO getMeetingDetail(
            @ApiParam(value = "会议ID", required = true) @PathVariable Long meetingId) {
        return meetingDetailService.getMeetingDetail(meetingId);
    }
}
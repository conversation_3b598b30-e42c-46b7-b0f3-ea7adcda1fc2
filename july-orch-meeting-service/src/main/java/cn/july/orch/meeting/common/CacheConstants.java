package cn.july.orch.meeting.common;

/**
 * 缓存相关key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    public static final String CACHE_PRE_DEFAULT = "JULY:CACHE:";

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "JULY:MEETING:CORE";
    public static final String LOCK_PRE = "JULY:MEETING:LOCK";

    /**
     * 具体的缓存key
     */
    // 认证token
    public static final String AUTH_TOKEN = CACHE_PRE + ":AUTH_TOKEN:";
    // 飞书openId
    public static final String FEISHU_OPENID = CACHE_PRE + ":FEISHU_OPENID:";

    public static final String RANGE_COMPANY = CACHE_PRE + ":RANGE_COMPANY:";


    // 飞书回调事件
    public static final String FS_CALLBACK_EVENT_LOCK = LOCK_PRE + ":FS_CALLBACK_EVENT:";
    
    // 会议签到码
    public static final String MEETING_CHECKIN_CODE = CACHE_PRE + ":CHECKIN_CODE:";
    
    public static String getAuthToken(String token){
        return AUTH_TOKEN + token;
    }

    public static String getFeishuOpenId(String clientType,String openId) {
        return FEISHU_OPENID + clientType+"_"+openId;
    }

    public static String getFsCallbackEvent(String eventId){
        return FS_CALLBACK_EVENT_LOCK + eventId;
    }
    
    public static String getMeetingCheckinCode(String checkinCode){
        return MEETING_CHECKIN_CODE + checkinCode;
    }
    
    public static String getMeetingCheckinCodeByMeetingId(Long meetingId){
        return MEETING_CHECKIN_CODE + "MEETING:" + meetingId;
    }

}

package cn.july.orch.meeting.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import lombok.Data;

/**
 * <AUTHOR> Assistant
 * @description 会议导出DTO
 */
@Data
@ContentRowHeight(20)
public class MeetingExportDTO {
    
    @ExcelProperty(value = "会议名称", index = 0)
    @ColumnWidth(30)
    private String meetingName;
    
    @ExcelProperty(value = "开始时间", index = 1)
    @ColumnWidth(20)
    private String startTime;
    
    @ExcelProperty(value = "结束时间", index = 2)
    @ColumnWidth(20)
    private String endTime;
    
    @ExcelProperty(value = "会议状态", index = 3)
    @ColumnWidth(15)
    private String status;
    
    @ExcelProperty(value = "会议地点", index = 4)
    @ColumnWidth(25)
    private String meetingLocation;
    
    @ExcelProperty(value = "参会人数", index = 5)
    @ColumnWidth(12)
    private Integer attendeeCount;
    
    @ExcelProperty(value = "主持人", index = 6)
    @ColumnWidth(15)
    private String hostUserName;
    
    @ExcelProperty(value = "记录员", index = 7)
    @ColumnWidth(15)
    private String recorderUserName;
    
    @ExcelProperty(value = "会议链接", index = 8)
    @ColumnWidth(40)
    private String meetingUrl;
    
    @ExcelProperty(value = "妙计链接", index = 9)
    @ColumnWidth(40)
    private String minuteUrl;
    
    @ExcelProperty(value = "创建人", index = 10)
    @ColumnWidth(15)
    private String createUserName;
    
    @ExcelProperty(value = "创建时间", index = 11)
    @ColumnWidth(20)
    private String createTime;
    
    @ExcelProperty(value = "实际开始时间", index = 12)
    @ColumnWidth(20)
    private String actualStartTime;
    
    @ExcelProperty(value = "实际结束时间", index = 13)
    @ColumnWidth(20)
    private String actualEndTime;
    
    @ExcelProperty(value = "规划名称", index = 14)
    @ColumnWidth(25)
    private String planName;
    
    @ExcelProperty(value = "部门名称", index = 15)
    @ColumnWidth(20)
    private String departmentName;
}

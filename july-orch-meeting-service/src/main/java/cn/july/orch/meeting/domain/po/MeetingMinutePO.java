package cn.july.orch.meeting.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 会议纪要持久化对象
 *
 * <AUTHOR>
 * @desc 会议纪要表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_minute", autoResultMap = true)
public class MeetingMinutePO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 会议ID，关联new_meeting表
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 会议纪要文本内容
     */
    @TableField("minute_text")
    private String minuteText;

    /**
     * 文本长度（字符数）
     */
    @TableField("minute_text_length")
    private Integer minuteTextLength;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField("create_user_name")
    private String createUserName;
}

package cn.july.orch.meeting.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务清单管理DTO
 */
@Data
@ApiModel("任务清单管理信息")
public class TaskListManagementDTO {

    @ApiModelProperty("清单ID")
    private Long id;

    @ApiModelProperty("父清单ID")
    private Long parentId;

    @ApiModelProperty("清单名称")
    private String name;

    @ApiModelProperty("清单描述")
    private String description;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建用户名")
    private String createUserName;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新用户ID")
    private String updateUserId;

    @ApiModelProperty("更新用户名")
    private String updateUserName;

    @ApiModelProperty("任务总数")
    private Integer totalTasks;

    @ApiModelProperty("已完成任务数")
    private Integer completedTasks;

    @ApiModelProperty("子清单列表")
    private List<TaskListManagementDTO> subLists;

    @ApiModelProperty("子清单统计 (格式: 数量)")
    private String subListSummary;

    @ApiModelProperty("子清单数量")
    private Integer subListCount;
}
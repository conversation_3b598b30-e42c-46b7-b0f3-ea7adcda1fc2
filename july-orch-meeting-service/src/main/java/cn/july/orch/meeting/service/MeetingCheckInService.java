package cn.july.orch.meeting.service;

import cn.july.orch.meeting.assembler.MeetingStandardConverter;
import cn.july.orch.meeting.assembler.NewMeetingConverter;
import cn.july.orch.meeting.domain.dto.CheckInConfigDTO;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.utils.AsyncTaskExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingCheckInService {

    private final MeetingStandardMapper meetingStandardMapper;
    private final NewMeetingMapper newMeetingMapper;
    private final MeetingStandardConverter meetingStandardConverter;
    private final NewMeetingConverter newMeetingConverter;
//    private final FeishuApiService feishuApiService;
    private final CheckInCodeService checkInCodeService;
    private final FeishuCardService feishuCardService;
    private final MeetingCheckInDataService meetingCheckInDataService;
    private final TenantFeishuClientManager tenantFeishuClientManager;

    /**
     * 发送签到通知
     */
    public void sendCheckIn() {
        log.info("开始执行签到通知发送任务");

        try {
            // 1. 查询需要签到的会议标准
            List<MeetingStandard> checkInStandards = getCheckInEnabledStandards();
            if (CollectionUtils.isEmpty(checkInStandards)) {
                log.info("没有找到需要签到的会议标准");
                return;
            }

            log.info("找到 {} 个需要签到的会议标准", checkInStandards.size());

            // 2. 查询符合发送条件的会议
            List<NewMeeting> eligibleMeetings = getEligibleMeetings(checkInStandards);
            if (CollectionUtils.isEmpty(eligibleMeetings)) {
                log.info("没有找到符合发送条件的会议");
                return;
            }

            log.info("找到 {} 个符合发送条件的会议", eligibleMeetings.size());

            // 3. 后续发送逻辑
            processCheckInNotifications(eligibleMeetings);

        } catch (Exception e) {
            log.error("执行签到通知发送任务失败", e);
        }
    }

    /**
     * 查询需要签到的会议标准
     * 条件：启用且配置了签到
     */
    private List<MeetingStandard> getCheckInEnabledStandards() {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class)
                .eq(MeetingStandardPO::getIsEnabled, 1)
                .isNotNull(MeetingStandardPO::getCheckInConfig);

        List<MeetingStandardPO> pos = meetingStandardMapper.selectList(wrapper);

        return pos.stream()
                .map(meetingStandardConverter::toEntity)
                .filter(standard -> isCheckInEnabled(standard.getCheckInConfig()))
                .collect(Collectors.toList());
    }

    /**
     * 查询符合发送条件的会议
     * 条件：未开始状态且时间在签到范围内
     */
    private List<NewMeeting> getEligibleMeetings(List<MeetingStandard> checkInStandards) {
        List<Long> standardIds = checkInStandards.stream()
                .map(MeetingStandard::getId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class)
                .eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.NOT_STARTED)
                .in(NewMeetingPO::getMeetingStandardId, standardIds)
                .isNotNull(NewMeetingPO::getStartTime);

        List<NewMeetingPO> pos = newMeetingMapper.selectList(wrapper);

        // 创建会议标准映射，便于快速查找
        Map<Long, MeetingStandard> standardMap = checkInStandards.stream()
                .collect(Collectors.toMap(MeetingStandard::getId, standard -> standard));

        return pos.stream()
                .map(newMeetingConverter::toEntity)
                .filter(meeting -> isEligibleForCheckIn(meeting, standardMap.get(meeting.getMeetingStandardId())))
                .collect(Collectors.toList());
    }

    /**
     * 判断会议是否符合签到发送条件
     * @param meeting 会议信息
     * @param meetingStandard 会议标准（包含签到配置）
     * @return 是否符合发送条件
     */
    private boolean isEligibleForCheckIn(NewMeeting meeting, MeetingStandard meetingStandard) {
        // 入参已保证有会议标准ID和开始时间，这里进行基本校验
        if (meeting.getStartTime() == null || meetingStandard == null) {
            log.debug("会议开始时间或会议标准为空，跳过: meetingId={}", meeting.getId());
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = meeting.getStartTime();

        // 会议已经开始，不再发送签到通知
        if (now.isAfter(startTime)) {
            log.debug("会议已开始，跳过发送签到通知: meetingId={}, startTime={}, now={}", 
                    meeting.getId(), startTime, now);
            return false;
        }

        // 获取签到配置
        CheckInConfigDTO checkInConfig = meetingStandard.getCheckInConfig();
        if (checkInConfig == null || !Boolean.TRUE.equals(checkInConfig.getEnabled())) {
            log.debug("会议标准未启用签到功能，跳过: meetingId={}, standardId={}", 
                    meeting.getId(), meetingStandard.getId());
            return false;
        }

        // 获取开始前多少分钟可以发送通知的配置
        Integer startMinutesBefore = checkInConfig.getStartMinutesBefore();
        if (startMinutesBefore == null || startMinutesBefore <= 0) {
            log.debug("会议标准未配置开始前发送时间，跳过: meetingId={}, standardId={}", 
                    meeting.getId(), meetingStandard.getId());
            return false;
        }

        // 计算可以开始发送签到通知的时间点
        LocalDateTime sendTime = startTime.minusMinutes(startMinutesBefore);

        // 判断当前时间是否到了可以发送的时间
        boolean canSend = now.isAfter(sendTime) || now.isEqual(sendTime);
        
        if (canSend) {
            log.info("会议符合签到发送条件: meetingId={}, startTime={}, sendTime={}, now={}, startMinutesBefore={}", 
                    meeting.getId(), startTime, sendTime, now, startMinutesBefore);
        } else {
            log.debug("会议未到签到发送时间: meetingId={}, startTime={}, sendTime={}, now={}, startMinutesBefore={}", 
                    meeting.getId(), startTime, sendTime, now, startMinutesBefore);
        }

        return canSend;
    }

    /**
     * 判断签到配置是否启用
     */
    private boolean isCheckInEnabled(CheckInConfigDTO checkInConfig) {
        return checkInConfig != null && Boolean.TRUE.equals(checkInConfig.getEnabled());
    }

    /**
     * 处理签到通知发送
     */
    private void processCheckInNotifications(List<NewMeeting> meetings) {
        log.info("开始处理 {} 个会议的签到通知", meetings.size());

        for (NewMeeting meeting : meetings) {
            CompletableFuture.runAsync(() -> processSingleMeetingCheckIn(meeting), AsyncTaskExecutor.checkInExecutor)
                    .exceptionally((ex -> {
                        log.error("发送签到通知失败: ", ex);
                        return null;
                    }));
        }

        log.info("所有会议签到通知处理完成");
    }

    /**
     * 处理单个会议的签到通知
     *
     * @param meeting 会议信息
     */
    public void processSingleMeetingCheckIn(NewMeeting meeting) {
        try {
            log.info("处理会议签到通知: 会议ID={}, 会议名称={}, 开始时间={}",
                    meeting.getId(), meeting.getMeetingName(), meeting.getStartTime());

            // 1. 生成签到码
            String checkinCode = checkInCodeService.generateCheckInCode();
            log.info("为会议生成签到码: meetingId={}, checkinCode={}", meeting.getId(), checkinCode);

            // 2. 存储签到码到缓存
            checkInCodeService.storeCheckInCode(meeting.getId(), checkinCode);

            // 3. 创建参会人签到数据（支持重复发送，避免重复创建）
            meetingCheckInDataService.createCheckInData(meeting, checkinCode);

            // 4. 发送签到码卡片给会议创建人
            sendCheckInCodeCardToCreator(meeting, checkinCode);

            // 5. 发送签到提醒卡片给所有参会人员
            sendCheckInReminderCardsToAttendees(meeting);

            // 6. 更新会议签到卡片发送状态（支持重复发送）
            updateMeetingCheckInStatus(meeting.getId());

            log.info("会议签到通知处理完成: meetingId={}", meeting.getId());

        } catch (Exception e) {
            log.error("处理会议签到通知失败: 会议ID={}", meeting.getId(), e);
        }
    }

    /**
     * 根据会议ID手动触发签到通知发送
     *
     * @param meetingId 会议ID
     * @return 处理结果
     */
    public String manualTriggerCheckIn(Long meetingId) {
        try {
            log.info("手动触发会议签到通知: meetingId={}", meetingId);

            // 查询会议信息
            NewMeetingPO meetingPO = newMeetingMapper.selectById(meetingId);
            if (meetingPO == null) {
                return "会议不存在";
            }

            NewMeeting meeting = newMeetingConverter.toEntity(meetingPO);

            // 检查会议状态
            if (meeting.getStatus() != NewMeetingStatusEnum.NOT_STARTED) {
                return "会议状态不是未开始，无法发送签到通知";
            }

            // 处理单个会议签到（支持重复发送，忽略已发送状态）
            processSingleMeetingCheckIn(meeting);

            return "签到通知发送完成";

        } catch (Exception e) {
            log.error("手动触发会议签到通知失败: meetingId={}", meetingId, e);
            return "签到通知发送失败: " + e.getMessage();
        }
    }

    /**
     * 发送签到码卡片给会议创建人
     */
    private void sendCheckInCodeCardToCreator(NewMeeting meeting, String checkinCode) {
        try {
            String cardContent = feishuCardService.buildCheckInCodeCard(meeting, checkinCode);
            tenantFeishuClientManager.getClient(meeting.getTenantId()).getRobotService().sendCard(meeting.getCreateUserId(), cardContent);
            log.info("签到码卡片发送成功: meetingId={}, creatorOpenId={}", meeting.getId(), meeting.getCreateUserId());
        } catch (Exception e) {
            log.error("发送签到码卡片失败: meetingId={}, creatorOpenId={}", meeting.getId(), meeting.getCreateUserId(), e);
        }
    }

    /**
     * 发送签到提醒卡片给所有参会人员
     */
    private void sendCheckInReminderCardsToAttendees(NewMeeting meeting) {
        if (CollectionUtils.isEmpty(meeting.getAttendees())) {
            log.warn("会议没有参会人员，跳过发送签到提醒: meetingId={}", meeting.getId());
            return;
        }

        String cardContent = feishuCardService.buildCheckInReminderCard(meeting);

        for (String attendeeOpenId : meeting.getAttendees()) {
            try {
                tenantFeishuClientManager.getClient(meeting.getTenantId()).getRobotService().sendCard(attendeeOpenId, cardContent);
                log.info("签到提醒卡片发送成功: meetingId={}, attendeeOpenId={}", meeting.getId(), attendeeOpenId);
            } catch (Exception e) {
                log.error("发送签到提醒卡片失败: meetingId={}, attendeeOpenId={}", meeting.getId(), attendeeOpenId, e);
            }
        }
    }

    /**
     * 更新会议签到卡片发送状态（支持重复发送）
     */
    private void updateMeetingCheckInStatus(Long meetingId) {
        try {
            LambdaUpdateWrapper<NewMeetingPO> updateWrapper = Wrappers.lambdaUpdate(NewMeetingPO.class)
                    .eq(NewMeetingPO::getId, meetingId)
                    .set(NewMeetingPO::getCheckinReminderSent, true)
                    .set(NewMeetingPO::getSendCheckinTime, LocalDateTime.now());

            int updateCount = newMeetingMapper.update(null, updateWrapper);
            if (updateCount > 0) {
                log.info("会议签到状态更新成功: meetingId={}", meetingId);
            } else {
                log.warn("会议签到状态更新失败: meetingId={}", meetingId);
            }
        } catch (Exception e) {
            log.error("更新会议签到状态失败: meetingId={}", meetingId, e);
        }
    }

}

package cn.july.orch.meeting.aspect;

import cn.july.orch.meeting.config.FeishuTaskProperties;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 任务卡片构建器，用于构建飞书消息卡片内容
 */
public class TaskCardBuilder {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 构建任务操作卡片内容
     *
     * @param task 任务信息
     * @param operationType 操作类型
     * @param content 附加内容（变更信息等）
     * @param properties 飞书任务配置
     * @return 卡片内容JSON字符串
     */
    public static String buildTaskOperationCard(TaskDTO task, String operationType, Map<String, Object> content, FeishuTaskProperties properties) {
        String taskDetailUrl = properties.buildTaskDetailUrl(task.getId());
        String title = getOperationTitle(operationType);
        String themeColor = getOperationThemeColor(operationType);
        String description = getOperationDescription(operationType, task, content);

        StringBuilder elements = new StringBuilder();
        
        // 添加操作描述元素
        elements.append(buildOperationDescriptionElement(description));
        
        // 添加任务信息卡片
        elements.append(",\n").append(buildTaskInfoCard(task));
        
        // 如果有变更内容，添加变更详情
        if (content != null && !content.isEmpty()) {
            elements.append(",\n").append(buildChangedFieldsCard(content));
        }
        
        // 添加操作按钮区域
        elements.append(",\n").append(buildEnhancedActionElement(taskDetailUrl, task));

        return String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true,\n" +
            "    \"enable_forward\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"%s\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"%s\"\n" +
            "    },\n" +
            "    \"subtitle\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"🕐 %s\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "%s\n" +
            "  ]\n" +
            "}",
            themeColor,
            escapeJsonString(title),
            escapeJsonString(LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM-dd HH:mm"))),
            elements.toString()
        );
    }

    /**
     * 构建子任务创建卡片内容
     *
     * @param subTask 子任务信息
     * @param parentTask 父任务信息
     * @param properties 飞书任务配置
     * @return 卡片内容JSON字符串
     */
    public static String buildSubTaskCreatedCard(TaskDTO subTask, TaskDTO parentTask, FeishuTaskProperties properties) {
        String taskDetailUrl = properties.buildTaskDetailUrl(parentTask.getId());
        String themeColor = "blue";

        StringBuilder elements = new StringBuilder();
        
        // 添加描述信息
        elements.append(buildOperationDescriptionElement("🎆 子任务拆解成功，让复杂任务变得更加清晰可控！"));
        
        // 添加父任务信息卡片
        elements.append(",\n").append(buildParentTaskCard(parentTask));
        
        // 添加子任务信息卡片
        elements.append(",\n").append(buildSubTaskCard(subTask));
        
        // 添加操作按钮
        elements.append(",\n").append(buildEnhancedActionElement(taskDetailUrl, parentTask));

        return String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true,\n" +
            "    \"enable_forward\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"%s\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"🧩 子任务已创建\"\n" +
            "    },\n" +
            "    \"subtitle\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"🕐 %s\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "%s\n" +
            "  ]\n" +
            "}",
            themeColor,
            escapeJsonString(LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM-dd HH:mm"))),
            elements.toString()
        );
    }

    /**
     * 获取操作标题
     */
    private static String getOperationTitle(String operationType) {
        switch (operationType) {
            case "CREATE":
                return "🎉 任务已创建";
            case "UPDATE_INFO":
                return "📝 任务已更新";
            case "COMPLETE":
                return "✅ 任务已完成";
            case "START":
                return "🚀 任务已开始";
            case "DECOMPOSE":
                return "🧩 任务已拆解";
            case "SUPERVISE":
                return "📢 任务已督办";
            default:
                return "📌 任务有更新";
        }
    }

    /**
     * 获取操作主题颜色
     */
    private static String getOperationThemeColor(String operationType) {
        switch (operationType) {
            case "CREATE":
                return "green";
            case "UPDATE_INFO":
                return "blue";
            case "COMPLETE":
                return "green";
            case "START":
                return "blue";
            case "DECOMPOSE":
                return "blue";
            case "SUPERVISE":
                return "orange";
            default:
                return "blue";
        }
    }

    /**
     * 获取操作描述
     */
    private static String getOperationDescription(String operationType, TaskDTO task, Map<String, Object> content) {
        switch (operationType) {
            case "CREATE":
                return "任务已成功创建";
            case "UPDATE_INFO":
                return content != null ? String.format("更新了%d个字段", content.size()) : "任务信息已更新";
            case "COMPLETE":
                return "任务已完成";
            case "START":
                return "任务已开始处理";
            case "DECOMPOSE":
                return content != null ? String.format("任务已拆解为%d个子任务", content.getOrDefault("subTaskCount", 0)) : "任务已拆解";
            case "SUPERVISE":
                return "任务已被督办";
            default:
                return "任务有更新";
        }
    }

    /**
     * 构建操作描述元素
     */
    private static String buildOperationDescriptionElement(String description) {
        return String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"%s\"\n" +
            "      }\n" +
            "    }",
            escapeJsonString(description)
        );
    }

    /**
     * 构建任务信息卡片
     */
    private static String buildTaskInfoCard(TaskDTO task) {
        StringBuilder elements = new StringBuilder();
        
        // 任务标题（单独成行，增加重要性）
        elements.append(String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**📝 任务标题**\\n\\n%s\"\n" +
            "      }\n" +
            "    }",
            escapeJsonString(task.getTitle())
        ));
        
        // 添加分隔线增加视觉空间
        elements.append(",\n").append(
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    }"
        );
        
        // 基本信息区域（一行两个字段）
        StringBuilder basicFields = new StringBuilder();
        
        // 负责人信息
        basicFields.append(String.format(
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**👥 负责人**\\n%s\"\n" +
            "          }\n" +
            "        }",
            escapeJsonString(task.getOwnerName())
        ));
        
        // 优先级信息
        if (task.getPriority() != null) {
            basicFields.append(",\n").append(String.format(
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**⭐ 优先级**\\n%s\"\n" +
                "          }\n" +
                "        }",
                escapeJsonString(getPriorityText(task.getPriority()))
            ));
        }
        
        // 添加基本信息区域
        elements.append(",\n").append(String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "%s\n" +
            "      ]\n" +
            "    }",
            basicFields.toString()
        ));
        
        // 状态和截止时间区域（一行两个字段）
        StringBuilder statusFields = new StringBuilder();
        
        // 状态信息
        if (task.getStatus() != null) {
            statusFields.append(String.format(
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**🟢 状态**\\n%s\"\n" +
                "          }\n" +
                "        }",
                escapeJsonString(getStatusText(task.getStatus()))
            ));
        }
        
        // 截止时间信息
        if (task.getDueDate() != null) {
            String dueDateStatus = getDueDateStatus(task.getDueDate());
            if (statusFields.length() > 0) {
                statusFields.append(",\n");
            }
            statusFields.append(String.format(
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**📅 截止时间**\\n%s\\n%s\"\n" +
                "          }\n" +
                "        }",
                escapeJsonString(task.getDueDate().format(DATE_FORMATTER)),
                escapeJsonString(dueDateStatus)
            ));
        }
        
        // 如果有状态或截止时间信息，添加该区域
        if (statusFields.length() > 0) {
            elements.append(",\n").append(String.format(
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"fields\": [\n" +
                "%s\n" +
                "      ]\n" +
                "    }",
                statusFields.toString()
            ));
        }
        
        return elements.toString();
    }

    /**
     * 构建变更字段卡片
     */
    private static String buildChangedFieldsCard(Map<String, Object> content) {
        StringBuilder fields = new StringBuilder();
        
        // 添加变更标题
        fields.append(
            "        {\n" +
            "          \"is_short\": false,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**📝 变更详情**\"\n" +
            "          }\n" +
            "        }"
        );
        
        // 处理变更的字段
        if (content.containsKey("changedFields")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> changedFields = (Map<String, Object>) content.get("changedFields");
            for (Map.Entry<String, Object> entry : changedFields.entrySet()) {
                String fieldName = getFieldName(entry.getKey());
                if (entry.getValue() instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> change = (Map<String, Object>) entry.getValue();
                    String oldValue = formatValue(change.get("old"));
                    String newValue = formatValue(change.get("new"));
                    
                    fields.append(",\n").append(String.format(
                        "        {\n" +
                        "          \"is_short\": false,\n" +
                        "          \"text\": {\n" +
                        "            \"tag\": \"lark_md\",\n" +
                        "            \"content\": \"**%s**\\n🔴 旧值：~~%s~~\\n🟢 新值：%s\"\n" +
                        "          }\n" +
                        "        }",
                        escapeJsonString(fieldName),
                        escapeJsonString(oldValue),
                        escapeJsonString(newValue)
                    ));
                }
            }
        }
        
        return String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "%s\n" +
            "      ]\n" +
            "    }",
            fields.toString()
        );
    }

    /**
     * 构建增强版操作按钮元素
     */
    private static String buildEnhancedActionElement(String taskDetailUrl, TaskDTO task) {
        return String.format(
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"button\",\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"📋 查看任务\"\n" +
            "          },\n" +
            "          \"url\": \"%s\",\n" +
            "          \"type\": \"primary\",\n" +
            "          \"size\": \"medium\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }",
            escapeJsonString(taskDetailUrl)
        );
    }

    /**
     * 构建父任务信息卡片
     */
    private static String buildParentTaskCard(TaskDTO parentTask) {
        return String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**📋 父任务**\\n\\n%s\"\n" +
            "      }\n" +
            "    }",
            escapeJsonString(parentTask.getTitle())
        );
    }

    /**
     * 构建子任务信息卡片
     */
    private static String buildSubTaskCard(TaskDTO subTask) {
        StringBuilder elements = new StringBuilder();
        
        // 子任务标题（独立成行，突出重要性）
        elements.append(String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**🧩 子任务标题**\\n\\n%s\"\n" +
            "      }\n" +
            "    }",
            escapeJsonString(subTask.getTitle())
        ));
        
        // 添加分隔线
        elements.append(",\n").append(
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    }"
        );
        
        // 基本信息区域（一行两个字段）
        StringBuilder basicFields = new StringBuilder();
        
        // 负责人信息
        basicFields.append(String.format(
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**👥 负责人**\\n%s\"\n" +
            "          }\n" +
            "        }",
            escapeJsonString(subTask.getOwnerName())
        ));
        
        // 优先级信息
        if (subTask.getPriority() != null) {
            basicFields.append(",\n").append(String.format(
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**⭐ 优先级**\\n%s\"\n" +
                "          }\n" +
                "        }",
                escapeJsonString(getPriorityText(subTask.getPriority()))
            ));
        }
        
        // 添加基本信息区域
        elements.append(",\n").append(String.format(
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "%s\n" +
            "      ]\n" +
            "    }",
            basicFields.toString()
        ));
        
        // 截止时间（如果存在，单独一行展示）
        if (subTask.getDueDate() != null) {
            String dueDateStatus = getDueDateStatus(subTask.getDueDate());
            elements.append(",\n").append(String.format(
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"fields\": [\n" +
                "        {\n" +
                "          \"is_short\": false,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**📅 截止时间**\\n%s\\n%s\"\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    }",
                escapeJsonString(subTask.getDueDate().format(DATE_FORMATTER)),
                escapeJsonString(dueDateStatus)
            ));
        }
        
        return elements.toString();
    }

    /**
     * 获取字段名称
     */
    private static String getFieldName(String fieldKey) {
        switch (fieldKey) {
            case "title":
                return "任务标题";
            case "description":
                return "任务描述";
            case "owner":
                return "负责人";
            case "priority":
                return "优先级";
            case "dueDate":
                return "截止时间";
            default:
                return fieldKey;
        }
    }

    /**
     * 获取优先级文本
     */
    private static String getPriorityText(TaskPriorityEnum priority) {
        if (priority == null) {
            return "未设置";
        }
        switch (priority) {
            case LOW:
                return "🟢 低";
            case MEDIUM:
                return "🟡 中";
            case HIGH:
                return "🔴 高";
            default:
                return priority.getDesc();
        }
    }

    /**
     * 获取状态文本
     */
    private static String getStatusText(TaskStatusEnum status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case NOT_STARTED:
                return "🕐 未开始";
            case IN_PROGRESS:
                return "🏃 进行中";
            case COMPLETED:
                return "✅ 已完成";
            case OVERDUE:
                return "⏰ 已超期";
            default:
                return status.getDesc();
        }
    }

    /**
     * 获取截止时间状态
     */
    private static String getDueDateStatus(LocalDateTime dueDate) {
        if (dueDate == null) {
            return "";
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (dueDate.isBefore(now)) {
            return "❗ 已超期";
        } else if (dueDate.isBefore(now.plusDays(1))) {
            return "🔥 今日到期";
        } else if (dueDate.isBefore(now.plusDays(3))) {
            return "⚠️ 临近到期";
        } else {
            return "🟢 时间充裕";
        }
    }

    /**
     * 格式化值
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return "空";
        }
        if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).format(DATE_FORMATTER);
        }
        return value.toString();
    }

    /**
     * 转义JSON字符串
     */
    private static String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
}
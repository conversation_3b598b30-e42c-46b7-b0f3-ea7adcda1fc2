package cn.july.orch.meeting.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 用户角色关联PO
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName(value = "sys_user_role_rel", autoResultMap = true)
public class SysUserRoleRelPO {

    /**
     * 主键自增
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色类型（系统，自定义）
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;
}

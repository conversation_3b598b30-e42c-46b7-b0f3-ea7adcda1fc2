package cn.july.orch.meeting.repository;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.domain.query.MeetingRoomQuery;
import java.util.List;

/**
 * 会议室Repository接口
 */
public interface MeetingRoomRepository {

    /**
     * 保存会议室
     * @param meetingRoom 会议室实体
     * @return 保存后的会议室
     */
    MeetingRoom save(MeetingRoom meetingRoom);

    /**
     * 根据ID查找会议室
     * @param id 会议室ID
     * @return 会议室实体
     */
    MeetingRoom findById(Long id);

    /**
     * 根据飞书会议室ID查找会议室
     * @param fsRoomId 飞书会议室ID
     * @return 会议室实体
     */
    MeetingRoom findByFsRoomId(String fsRoomId);

    /**
     * 分页查询会议室
     * @param query 查询参数
     * @return 分页结果
     */
    PageResultDTO<MeetingRoom> pageQuery(MeetingRoomQuery query);

    /**
     * 查询所有未删除的会议室
     * @return 会议室列表
     */
    List<MeetingRoom> findAll();

    /**
     * 根据ID删除会议室
     * @param id 会议室ID
     */
    void deleteById(Long id);
}
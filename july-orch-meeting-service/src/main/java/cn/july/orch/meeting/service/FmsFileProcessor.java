package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.enums.FmsTaskProcessTypeEnum;
import cn.july.orch.meeting.utils.execl.EasyExcelUtil;
import cn.july.orch.meeting.utils.execl.ExportExcelModel;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description FMS文件处理器
 */
@Slf4j
@Service
public class FmsFileProcessor {
    
    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileDetailService fileDetailService;
    
    /**
     * 处理文件生成和上传（流式处理，避免内存问题）
     * @param processType 处理方式
     * @param data 数据列表
     * @param clazz 数据类型
     * @param fileName 文件名
     * @param sheetName sheet名称
     * @return 文件ID
     */
    public FileDetailPO processFile(FmsTaskProcessTypeEnum processType, List<?> data, Class<?> clazz, String fileName, String sheetName) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fullFileName = fileName + "_" + timestamp + processType.getFileExtension();
            
            // 使用流式处理，避免大文件内存问题
            java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();
            generateFileToStream(processType, data, clazz, sheetName, outputStream);
            java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(outputStream.toByteArray());
            
            FileInfo fileInfo = fileStorageService.of(inputStream).setOriginalFilename(fullFileName).upload();
            FileDetailPO fileDetailPO = fileDetailService.getById(fileInfo.getId());
            return fileDetailPO;
        } catch (Exception e) {
            log.error("处理文件失败: processType={}, fileName={}", processType, fileName, e);
            throw new RuntimeException("处理文件失败", e);
        }
    }
    
    /**
     * 生成文件到输出流（流式处理）
     */
    private void generateFileToStream(FmsTaskProcessTypeEnum processType, List<?> data, Class<?> clazz, String sheetName, java.io.OutputStream outputStream) {
        switch (processType) {
            case EXCEL_EXPORT:
                generateExcelToStream(data, clazz, sheetName, outputStream);
                break;
            case PDF_EXPORT:
                // TODO: 实现PDF生成逻辑
                throw new UnsupportedOperationException("PDF导出功能暂未实现");
            case CSV_EXPORT:
                // TODO: 实现CSV生成逻辑
                throw new UnsupportedOperationException("CSV导出功能暂未实现");
            case CUSTOM_PROCESS:
                // TODO: 实现自定义处理逻辑
                throw new UnsupportedOperationException("自定义处理功能暂未实现");
            default:
                throw new IllegalArgumentException("不支持的处理方式: " + processType);
        }
    }
    
    
    /**
     * 生成Excel到输出流（流式处理）
     */
    @SuppressWarnings("unchecked")
    private void generateExcelToStream(List<?> data, Class<?> clazz, String sheetName, java.io.OutputStream outputStream) {
        ExportExcelModel model = new ExportExcelModel();
        model.setSheetName(sheetName);
        model.setData(data);
        model.setClazz(clazz);
        
        EasyExcelUtil.exportExcelToStream("数据导出", model, outputStream);
    }
    
    
}

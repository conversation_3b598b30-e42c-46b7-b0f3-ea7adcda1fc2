package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.july.core.exception.BusinessException;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.TopicAssembler;
import cn.july.orch.meeting.domain.command.TopicCreateCommand;
import cn.july.orch.meeting.domain.command.TopicUpdateCommand;
import cn.july.orch.meeting.domain.dto.FileInfoDTO;
import cn.july.orch.meeting.domain.dto.TopicDTO;
import cn.july.orch.meeting.domain.entity.Topic;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.domain.po.TopicPO;
import cn.july.orch.meeting.domain.query.TopicQuery;
import cn.july.orch.meeting.mapper.TopicMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 议题服务
 * @date 2025-11-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TopicService {

    private final TopicMapper topicMapper;
    private final TopicAssembler topicAssembler;
    private final FileDetailService fileDetailService;

    /**
     * 查询启用的议题列表
     */
    public List<TopicDTO> listEnabled() {
        LambdaQueryWrapper<TopicPO> wrapper = Wrappers.lambdaQuery(TopicPO.class);
        wrapper.eq(TopicPO::getIsEnabled, 1);
        wrapper.orderByDesc(TopicPO::getCreateTime);
        
        List<TopicPO> pos = topicMapper.selectList(wrapper);
        return pos.stream()
            .map(po -> {
                Topic entity = topicAssembler.toEntity(po);
                // 加载附件信息
                loadAttachments(entity);
                return topicAssembler.toDTO(entity);
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据ID查询议题详情
     */
    public TopicDTO getById(Long id) {
        TopicPO po = topicMapper.selectById(id);
        if (po == null) {
            return null;
        }
        Topic entity = topicAssembler.toEntity(po);
        // 加载附件信息
        loadAttachments(entity);
        return topicAssembler.toDTO(entity);
    }

    /**
     * 分页查询议题
     */
    public PageResultDTO<TopicDTO> pageQuery(TopicQuery query) {
        LambdaQueryWrapper<TopicPO> wrapper = Wrappers.lambdaQuery(TopicPO.class);

        // 议题名称模糊查询
        if (query.getName() != null && !query.getName().trim().isEmpty()) {
            wrapper.like(TopicPO::getName, query.getName().trim());
        }

        // 启用状态筛选
        if (query.getIsEnabled() != null) {
            wrapper.eq(TopicPO::getIsEnabled, query.getIsEnabled());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(TopicPO::getCreateTime);

        Page<TopicPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<TopicPO> result = topicMapper.selectPage(page, wrapper);

        // 转换为 PageResultDTO
        List<TopicDTO> dtoList = result.getRecords().stream()
                .map(po -> {
                    Topic entity = topicAssembler.toEntity(po);
                    // 加载附件信息
                    loadAttachments(entity);
                    return topicAssembler.toDTO(entity);
                })
                .collect(Collectors.toList());

        PageResultDTO<TopicDTO> pageResult = new PageResultDTO<>();
        pageResult.setPageNo(Math.toIntExact(result.getCurrent()));
        pageResult.setPageSize(Math.toIntExact(result.getSize()));
        pageResult.setTotal(result.getTotal());
        pageResult.setTotalPages((int) result.getPages());
        pageResult.setList(dtoList);

        return pageResult;
    }

    /**
     * 创建议题
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTopic(TopicCreateCommand command) {
        // 1. 转换为领域对象
        Topic topic = topicAssembler.toEntity(command);
        
        // 2. 基础验证
        validateTopic(topic);
        
        // 3. 验证附件存在性
        validateAttachmentKeys(command.getAttachmentKeys());
        
        // 4. 检查名称唯一性
        if (existsByName(topic.getName(), null)) {
            throw new BusinessException("议题名称已存在");
        }
        
        // 5. 保存
        TopicPO po = topicAssembler.toPO(topic);
        topicMapper.insert(po);
    }

    /**
     * 更新议题
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTopic(TopicUpdateCommand command) {
        // 1. 检查是否存在
        Topic existing = topicAssembler.toEntity(topicMapper.selectById(command.getId()));
        if (existing == null) {
            throw new BusinessException("议题不存在");
        }
        
        // 2. 转换为领域对象
        Topic topic = topicAssembler.toEntity(command);
        
        // 3. 基础验证
        validateTopic(topic);
        
        // 4. 验证附件存在性
        validateAttachmentKeys(command.getAttachmentKeys());
        
        // 5. 检查名称唯一性（排除自己）
        if (existsByName(topic.getName(), topic.getId())) {
            throw new BusinessException("议题名称已存在");
        }
        
        // 6. 更新
        TopicPO po = topicAssembler.toPO(topic);
        topicMapper.updateById(po);
    }

    /**
     * 删除议题
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTopic(Long id) {
        // 1. 检查是否存在
        Topic existing = topicAssembler.toEntity(topicMapper.selectById(id));
        if (existing == null) {
            throw new BusinessException("议题不存在");
        }
        
        // 2. 删除议题
        topicMapper.deleteById(id);
    }

    /**
     * 启用议题
     */
    @Transactional(rollbackFor = Exception.class)
    public void enableTopic(Long id) {
        // 1. 检查是否存在
        TopicPO po = topicMapper.selectById(id);
        if (po == null) {
            throw new BusinessException("议题不存在");
        }
        
        // 2. 启用
        po.setIsEnabled(1);
        topicMapper.updateById(po);
    }

    /**
     * 停用议题
     */
    @Transactional(rollbackFor = Exception.class)
    public void disableTopic(Long id) {
        // 1. 检查是否存在
        TopicPO po = topicMapper.selectById(id);
        if (po == null) {
            throw new BusinessException("议题不存在");
        }
        
        // 2. 停用
        po.setIsEnabled(0);
        topicMapper.updateById(po);
    }

    /**
     * 验证议题基础数据
     */
    private void validateTopic(Topic topic) {
        // 1. 议题名称验证
        if (topic.getName() == null || topic.getName().trim().isEmpty()) {
            throw new BusinessException("议题名称不能为空");
        }
        
        // 2. 启用状态验证
        if (topic.getIsEnabled() == null) {
            throw new BusinessException("是否启用不能为空");
        }
        
        if (topic.getIsEnabled() != 0 && topic.getIsEnabled() != 1) {
            throw new BusinessException("是否启用只能是0或1");
        }
    }

    /**
     * 检查议题名称是否存在
     */
    private boolean existsByName(String name, Long excludeId) {
        return topicMapper.countByName(name, excludeId) > 0;
    }

    /**
     * 加载议题的附件信息
     */
    private void loadAttachments(Topic topic) {
        if (CollUtil.isEmpty(topic.getAttachmentKeys())) {
            topic.setAttachments(new ArrayList<>());
            return;
        }
        
        List<FileInfoDTO> attachments = new ArrayList<>();
        for (String key : topic.getAttachmentKeys()) {
            try {
                FileDetailPO fileDetailPO = fileDetailService.getById(key);
                if (fileDetailPO != null) {
                    FileInfoDTO fileInfoDTO = FileInfoDTO.builder()
                            .fileKey(key)
                            .originalFilename(fileDetailPO.getOriginalFilename())
                            .url(fileDetailPO.getUrl())
                            .build();
                    attachments.add(fileInfoDTO);
                } else {
                    log.warn("附件不存在: {}", key);
                }
            } catch (Exception e) {
                log.error("加载附件失败: {}", key, e);
            }
        }
        topic.setAttachments(attachments);
    }

    /**
     * 验证附件Key的存在性
     */
    private void validateAttachmentKeys(List<String> attachmentKeys) {
        if (CollUtil.isEmpty(attachmentKeys)) {
            return;
        }
        
        for (String key : attachmentKeys) {
            FileDetailPO fileDetailPO = fileDetailService.getById(key);
            if (fileDetailPO == null) {
                throw new BusinessException("附件不存在: " + key);
            }
        }
    }
}

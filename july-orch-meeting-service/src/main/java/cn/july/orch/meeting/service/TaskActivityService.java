package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.TaskActivityInfo;
import cn.july.orch.meeting.domain.entity.TaskInfo;
import cn.july.orch.meeting.enums.TaskActivityTypeEnum;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.repository.ITaskActivityRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 任务动态服务
 */
@Slf4j
@Service
public class TaskActivityService {

    @Resource
    private ITaskActivityRepository taskActivityRepository;

    /**
     * 记录任务创建动态
     *
     * @param taskInfo 任务信息
     */
    public void recordCreateActivity(TaskInfo taskInfo) {
        log.info("记录任务创建动态，任务ID：{}，标题：{}", taskInfo.getId(), taskInfo.getTitle());
        
        Map<String, Object> content = new HashMap<>();
        content.put("taskId", taskInfo.getId());
        content.put("title", taskInfo.getTitle());
        content.put("description", taskInfo.getDescription());
        content.put("ownerOpenId", taskInfo.getOwnerOpenId());
        content.put("ownerName", taskInfo.getOwnerName());
        content.put("priority", taskInfo.getPriority());
        content.put("dueDate", taskInfo.getDueDate());
        content.put("meetingId", taskInfo.getMeetingId());
        content.put("taskListId", taskInfo.getTaskListId());
        content.put("parentId", taskInfo.getParentId());
        
        String description = String.format("%s 创建了任务「%s」", 
            taskInfo.getCreateUserName(), taskInfo.getTitle());
        
        recordActivity(taskInfo.getId(), TaskActivityTypeEnum.CREATE, description, content);
    }

    /**
     * 记录任务状态更新动态
     *
     * @param taskId 任务ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     */
    public void recordStatusUpdateActivity(Long taskId, TaskStatusEnum oldStatus, TaskStatusEnum newStatus) {
        log.info("记录任务状态更新动态，任务ID：{}，原状态：{}，新状态：{}", taskId, oldStatus, newStatus);
        
        Map<String, Object> content = new HashMap<>();
        content.put("oldStatus", oldStatus);
        content.put("newStatus", newStatus);
        content.put("oldStatusDesc", oldStatus != null ? oldStatus.getDesc() : null);
        content.put("newStatusDesc", newStatus != null ? newStatus.getDesc() : null);
        
        String description = String.format("将任务状态从「%s」更新为「%s」", 
            oldStatus != null ? oldStatus.getDesc() : "未知",
            newStatus != null ? newStatus.getDesc() : "未知");
        
        recordActivity(taskId, TaskActivityTypeEnum.UPDATE_STATUS, description, content);
    }

    /**
     * 记录任务信息更新动态
     *
     * @param taskId 任务ID
     * @param changedFields 变更的字段Map
     */
    public void recordInfoUpdateActivity(Long taskId, Map<String, Object> changedFields) {
        log.info("记录任务信息更新动态，任务ID：{}，变更字段：{}", taskId, changedFields.keySet());
        
        Map<String, Object> content = new HashMap<>();
        content.put("changedFields", changedFields);
        content.put("changeCount", changedFields.size());
        
        String description = String.format("更新了任务信息，修改了%d个字段", changedFields.size());
        
        recordActivity(taskId, TaskActivityTypeEnum.UPDATE_INFO, description, content);
    }

    /**
     * 记录任务完成动态
     *
     * @param taskId 任务ID
     */
    public void recordCompleteActivity(Long taskId) {
        log.info("记录任务完成动态，任务ID：{}", taskId);
        
        Map<String, Object> content = new HashMap<>();
        content.put("completedAt", LocalDateTime.now());
        
        String description = "完成了任务";
        
        recordActivity(taskId, TaskActivityTypeEnum.COMPLETE, description, content);
    }

    /**
     * 记录任务开始动态
     *
     * @param taskId 任务ID
     */
    public void recordStartActivity(Long taskId) {
        log.info("记录任务开始动态，任务ID：{}", taskId);
        
        Map<String, Object> content = new HashMap<>();
        content.put("startedAt", LocalDateTime.now());
        
        String description = "开始处理任务";
        
        recordActivity(taskId, TaskActivityTypeEnum.START, description, content);
    }

    /**
     * 记录任务拆解动态
     *
     * @param parentTaskId 父任务ID
     * @param subTaskIds 子任务ID列表
     */
    public void recordDecomposeActivity(Long parentTaskId, List<Long> subTaskIds) {
        log.info("记录任务拆解动态，父任务ID：{}，子任务数量：{}", parentTaskId, subTaskIds.size());
        
        Map<String, Object> content = new HashMap<>();
        content.put("subTaskIds", subTaskIds);
        content.put("subTaskCount", subTaskIds.size());
        content.put("decomposedAt", LocalDateTime.now());
        
        String description = String.format("将任务拆解为%d个子任务", subTaskIds.size());
        
        recordActivity(parentTaskId, TaskActivityTypeEnum.DECOMPOSE, description, content);
    }

    /**
     * 记录任务删除动态
     *
     * @param taskId 任务ID
     * @param reason 删除原因
     */
    public void recordDeleteActivity(Long taskId, String reason) {
        log.info("记录任务删除动态，任务ID：{}，原因：{}", taskId, reason);
        
        Map<String, Object> content = new HashMap<>();
        content.put("reason", reason);
        content.put("deletedAt", LocalDateTime.now());
        
        String description = "删除了任务";
        
        recordActivity(taskId, TaskActivityTypeEnum.DELETE, description, content);
    }

    /**
     * 记录任务督办动态
     *
     * @param taskId 任务ID
     * @param superviseReason 督办原因
     * @param superviseLevel 督办级别
     */
    public void recordSuperviseActivity(Long taskId, String superviseReason, String superviseLevel) {
        log.info("记录任务督办动态，任务ID：{}，督办原因：{}，督办级别：{}", taskId, superviseReason, superviseLevel);
        
        Map<String, Object> content = new HashMap<>();
        content.put("superviseReason", superviseReason);
        content.put("superviseLevel", superviseLevel);
        content.put("supervisedAt", LocalDateTime.now());
        
        String description = String.format("对任务进行%s督办", superviseLevel != null ? superviseLevel : "");
        
        recordActivity(taskId, TaskActivityTypeEnum.SUPERVISE, description, content);
    }

    /**
     * 记录任务提醒动态
     *
     * @param taskId 任务ID
     * @param remindType 提醒类型
     * @param remindTarget 提醒对象
     */
    public void recordRemindActivity(Long taskId, String remindType, String remindTarget) {
        log.info("记录任务提醒动态，任务ID：{}，提醒类型：{}，提醒对象：{}", taskId, remindType, remindTarget);
        
        Map<String, Object> content = new HashMap<>();
        content.put("remindType", remindType);
        content.put("remindTarget", remindTarget);
        content.put("remindedAt", LocalDateTime.now());
        
        String description = String.format("对任务进行了%s提醒", remindType != null ? remindType : "");
        
        recordActivity(taskId, TaskActivityTypeEnum.REMIND, description, content);
    }

    /**
     * 记录任务升级督办动态
     *
     * @param taskId 任务ID
     * @param escalateReason 升级原因
     * @param fromLevel 原级别
     * @param toLevel 目标级别
     */
    public void recordEscalateActivity(Long taskId, String escalateReason, String fromLevel, String toLevel) {
        log.info("记录任务升级督办动态，任务ID：{}，升级原因：{}，从{}升级到{}", taskId, escalateReason, fromLevel, toLevel);
        
        Map<String, Object> content = new HashMap<>();
        content.put("escalateReason", escalateReason);
        content.put("fromLevel", fromLevel);
        content.put("toLevel", toLevel);
        content.put("escalatedAt", LocalDateTime.now());
        
        String description = String.format("将督办级别从「%s」升级为「%s」", 
            fromLevel != null ? fromLevel : "未知", 
            toLevel != null ? toLevel : "未知");
        
        recordActivity(taskId, TaskActivityTypeEnum.ESCALATE, description, content);
    }

    /**
     * 记录任务超期动态
     *
     * @param taskId 任务ID
     * @param dueDate 截止时间
     * @param overdueDays 超期天数
     */
    public void recordOverdueActivity(Long taskId, LocalDateTime dueDate, Long overdueDays) {
        log.info("记录任务超期动态，任务ID：{}，截止时间：{}，超期天数：{}", taskId, dueDate, overdueDays);
        
        Map<String, Object> content = new HashMap<>();
        content.put("dueDate", dueDate);
        content.put("overdueDays", overdueDays);
        content.put("overdueAt", LocalDateTime.now());
        
        String description = String.format("任务已超期%d天", overdueDays != null ? overdueDays : 0);
        
        recordActivity(taskId, TaskActivityTypeEnum.OVERDUE, description, content);
    }

    /**
     * 记录任务延期动态
     *
     * @param taskId 任务ID
     * @param oldDueDate 原截止时间
     * @param newDueDate 新截止时间
     * @param postponeReason 延期原因
     */
    public void recordPostponeActivity(Long taskId, LocalDateTime oldDueDate, LocalDateTime newDueDate, String postponeReason) {
        log.info("记录任务延期动态，任务ID：{}，原截止时间：{}，新截止时间：{}，延期原因：{}", taskId, oldDueDate, newDueDate, postponeReason);
        
        Map<String, Object> content = new HashMap<>();
        content.put("oldDueDate", oldDueDate);
        content.put("newDueDate", newDueDate);
        content.put("postponeReason", postponeReason);
        content.put("postponedAt", LocalDateTime.now());
        
        String description = "延期了任务的截止时间";
        
        recordActivity(taskId, TaskActivityTypeEnum.POSTPONE, description, content);
    }

    /**
     * 记录任务分配用户动态
     *
     * @param taskId 任务ID
     * @param oldOwner 原负责人
     * @param newOwner 新负责人
     */
    public void recordAssignUserActivity(Long taskId, String oldOwner, String newOwner) {
        log.info("记录任务分配用户动态，任务ID：{}，原负责人：{}，新负责人：{}", taskId, oldOwner, newOwner);
        
        Map<String, Object> content = new HashMap<>();
        content.put("oldOwner", oldOwner);
        content.put("newOwner", newOwner);
        content.put("assignedAt", LocalDateTime.now());
        
        String description = String.format("将任务分配给「%s」", newOwner != null ? newOwner : "未知");
        
        recordActivity(taskId, TaskActivityTypeEnum.ASSIGN_USER, description, content);
    }

    /**
     * 记录任务截止时间设置动态
     *
     * @param taskId 任务ID
     * @param oldDueDate 原截止时间
     * @param newDueDate 新截止时间
     */
    public void recordSetDueDateActivity(Long taskId, LocalDateTime oldDueDate, LocalDateTime newDueDate) {
        log.info("记录任务截止时间设置动态，任务ID：{}，原截止时间：{}，新截止时间：{}", taskId, oldDueDate, newDueDate);
        
        Map<String, Object> content = new HashMap<>();
        content.put("oldDueDate", oldDueDate);
        content.put("newDueDate", newDueDate);
        content.put("setAt", LocalDateTime.now());
        
        String description = "设置了任务的截止时间";
        
        recordActivity(taskId, TaskActivityTypeEnum.SET_DUE_DATE, description, content);
    }

    /**
     * 记录任务评论动态
     *
     * @param taskId 任务ID
     * @param comment 评论内容
     */
    public void recordAddCommentActivity(Long taskId, String comment) {
        log.info("记录任务评论动态，任务ID：{}，评论长度：{}", taskId, comment != null ? comment.length() : 0);
        
        Map<String, Object> content = new HashMap<>();
        content.put("comment", comment);
        content.put("commentLength", comment != null ? comment.length() : 0);
        content.put("commentedAt", LocalDateTime.now());
        
        String description = "添加了评论";
        
        recordActivity(taskId, TaskActivityTypeEnum.ADD_COMMENT, description, content);
    }

    /**
     * 记录任务附件添加动态
     *
     * @param taskId 任务ID
     * @param attachmentName 附件名称
     * @param attachmentKey 附件Key
     */
    public void recordAddAttachmentActivity(Long taskId, String attachmentName, String attachmentKey) {
        log.info("记录任务附件添加动态，任务ID：{}，附件名称：{}", taskId, attachmentName);
        
        Map<String, Object> content = new HashMap<>();
        content.put("attachmentName", attachmentName);
        content.put("attachmentKey", attachmentKey);
        content.put("addedAt", LocalDateTime.now());
        
        String description = String.format("添加了附件「%s」", attachmentName != null ? attachmentName : "未知");
        
        recordActivity(taskId, TaskActivityTypeEnum.ADD_ATTACHMENT, description, content);
    }

    /**
     * 记录任务附件移除动态
     *
     * @param taskId 任务ID
     * @param attachmentName 附件名称
     * @param attachmentKey 附件Key
     */
    public void recordRemoveAttachmentActivity(Long taskId, String attachmentName, String attachmentKey) {
        log.info("记录任务附件移除动态，任务ID：{}，附件名称：{}", taskId, attachmentName);
        
        Map<String, Object> content = new HashMap<>();
        content.put("attachmentName", attachmentName);
        content.put("attachmentKey", attachmentKey);
        content.put("removedAt", LocalDateTime.now());
        
        String description = String.format("移除了附件「%s」", attachmentName != null ? attachmentName : "未知");
        
        recordActivity(taskId, TaskActivityTypeEnum.REMOVE_ATTACHMENT, description, content);
    }

    /**
     * 获取任务动态列表
     *
     * @param taskId 任务ID
     * @return 动态列表
     */
    public List<TaskActivityInfo> getTaskActivities(Long taskId) {
        log.info("获取任务动态列表，任务ID：{}", taskId);
        
        return taskActivityRepository.findByTaskId(taskId);
    }

    /**
     * 获取任务最新动态
     *
     * @param taskId 任务ID
     * @param limit 限制条数
     * @return 动态列表
     */
    public List<TaskActivityInfo> getLatestTaskActivities(Long taskId, Integer limit) {
        log.info("获取任务最新动态，任务ID：{}，限制条数：{}", taskId, limit);
        
        return taskActivityRepository.findLatestByTaskId(taskId, limit);
    }

    /**
     * 统计任务动态数量
     *
     * @param taskId 任务ID
     * @return 动态数量
     */
    public Long countTaskActivities(Long taskId) {
        log.info("统计任务动态数量，任务ID：{}", taskId);
        
        return taskActivityRepository.countByTaskId(taskId);
    }

    /**
     * 记录动态的通用方法
     *
     * @param taskId 任务ID
     * @param activityType 动态类型
     * @param activityDescription 动态描述
     * @param contentJson 动态内容
     */
    private void recordActivity(Long taskId, TaskActivityTypeEnum activityType, String activityDescription, Map<String, Object> contentJson) {
        try {
            TaskActivityInfo activityInfo = TaskActivityInfo.create(taskId, activityType, activityDescription, contentJson);
            taskActivityRepository.insert(activityInfo);
            
            log.debug("任务动态记录成功，任务ID：{}，类型：{}，描述：{}", taskId, activityType, activityDescription);
        } catch (Exception e) {
            log.error("记录任务动态失败，任务ID：{}，类型：{}", taskId, activityType, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}
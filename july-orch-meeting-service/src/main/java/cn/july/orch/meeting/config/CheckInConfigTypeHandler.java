package cn.july.orch.meeting.config;

import cn.july.orch.meeting.domain.dto.CheckInConfigDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @description 签到配置JSON类型处理器
 * @date 2025-08-26
 */
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(CheckInConfigDTO.class)
public class CheckInConfigTypeHandler extends BaseTypeHandler<CheckInConfigDTO> {

    private static final ObjectMapper objectMapper = JacksonConfig.getSharedObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, CheckInConfigDTO parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
            } catch (JsonProcessingException e) {
                log.error("签到配置序列化失败", e);
                ps.setString(i, null);
            }
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public CheckInConfigDTO getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseCheckInConfig(value);
    }

    @Override
    public CheckInConfigDTO getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseCheckInConfig(value);
    }

    @Override
    public CheckInConfigDTO getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseCheckInConfig(value);
    }

    private CheckInConfigDTO parseCheckInConfig(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.readValue(value, CheckInConfigDTO.class);
        } catch (JsonProcessingException e) {
            log.error("签到配置反序列化失败: {}", value, e);
            return null;
        }
    }
}
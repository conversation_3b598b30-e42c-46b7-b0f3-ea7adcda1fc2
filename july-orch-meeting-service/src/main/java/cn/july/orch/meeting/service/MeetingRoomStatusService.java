package cn.july.orch.meeting.service;

import cn.july.orch.meeting.enums.MeetingRoomStatusEnum;

/**
 * <AUTHOR> Assistant
 * @description 会议室状态服务接口
 */
public interface MeetingRoomStatusService {
    
    /**
     * 更新会议室状态
     * @param meetingRoomId 会议室ID
     * @param status 会议室状态
     */
    void updateMeetingRoomStatus(Long meetingRoomId, MeetingRoomStatusEnum status);
    
    /**
     * 检查会议室是否空闲
     * @param meetingRoomId 会议室ID
     * @return 是否空闲
     */
    boolean isMeetingRoomFree(Long meetingRoomId);
}
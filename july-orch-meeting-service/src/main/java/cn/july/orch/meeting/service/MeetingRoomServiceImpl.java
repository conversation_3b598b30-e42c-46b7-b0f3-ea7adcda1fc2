package cn.july.orch.meeting.service;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.entity.MeetingRoom;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.domain.query.MeetingRoomQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomHistoryQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomHistoryDTO;
import cn.july.orch.meeting.domain.query.MeetingRoomStatisticsQuery;
import cn.july.orch.meeting.domain.dto.MeetingRoomStatisticsDTO;
import cn.july.orch.meeting.domain.dto.MeetingRoomStatisticsDTO.DailyStatistics;
import cn.july.orch.meeting.repository.MeetingRoomRepository;
import cn.july.orch.meeting.assembler.MeetingRoomAssembler;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会议室服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingRoomServiceImpl implements MeetingRoomService {

    private final MeetingRoomRepository meetingRoomRepository;
    private final MeetingRoomAssembler meetingRoomAssembler;
    private final FeishuMeetingRoomService feishuMeetingRoomService;
    private final NewMeetingMapper newMeetingMapper;
    private final NewMeetingAssembler newMeetingAssembler;

    @Override
    public void syncMeetingRoomsFromFeishu() {
        try {
            log.info("开始同步飞书会议室数据");
            // 调用飞书会议室服务同步数据
            feishuMeetingRoomService.syncMeetingRooms();
            log.info("飞书会议室数据同步完成");
        } catch (Exception e) {
            log.error("同步飞书会议室数据失败", e);
        }
    }

    @Override
    public PageResultDTO<MeetingRoomDTO> pageQuery(MeetingRoomQuery query) {
        PageResultDTO<MeetingRoom> pageResult = meetingRoomRepository.pageQuery(query);
        List<MeetingRoomDTO> dtoList = meetingRoomAssembler.toDTOList(pageResult.getList());
        return new PageResultDTO<>(query.getPageNum(), query.getPageSize(), pageResult.getTotal(), dtoList);
    }

    @Override
    public List<MeetingRoomDTO> listAll() {
        List<MeetingRoom> meetingRooms = meetingRoomRepository.findAll();
        return meetingRoomAssembler.toDTOList(meetingRooms);
    }

    @Override
    public MeetingRoomDTO getById(Long id) {
        MeetingRoom meetingRoom = meetingRoomRepository.findById(id);
        return meetingRoom == null ? null : meetingRoomAssembler.toDTO(meetingRoom);
    }

    @Override
    public PageResultDTO<MeetingRoomHistoryDTO> queryMeetingHistory(MeetingRoomHistoryQuery query) {
        if (query.getMeetingRoomId() == null) {
            throw new IllegalArgumentException("会议室ID不能为空");
        }
        
        // 设置默认时间范围（如果未指定）
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        
        if (query.getStartDate() != null && !query.getStartDate().isEmpty()) {
            startDateTime = LocalDate.parse(query.getStartDate()).atStartOfDay();
        } else {
            // 默认为当前月份第一天
            startDateTime = YearMonth.now().atDay(1).atStartOfDay();
        }
        
        if (query.getEndDate() != null && !query.getEndDate().isEmpty()) {
            endDateTime = LocalDate.parse(query.getEndDate()).plusDays(1).atStartOfDay();
        } else {
            // 默认为当前月份最后一天
            endDateTime = YearMonth.now().atEndOfMonth().plusDays(1).atStartOfDay();
        }
        
        // 查询指定会议室的历史会议记录
        List<NewMeetingPO> meetingPOs = newMeetingMapper.findByMeetingRoomIdAndTimeRange(
            query.getMeetingRoomId(), 
            startDateTime, 
            endDateTime,
            query.getPageNum(),
            query.getPageSize()
        );
        
        // 查询总记录数
        long total = newMeetingMapper.countByMeetingRoomIdAndTimeRange(
            query.getMeetingRoomId(), 
            startDateTime, 
            endDateTime
        );
        
        // 转换为DTO
        List<MeetingRoomHistoryDTO> dtoList = meetingPOs.stream()
            .map(po -> {
                NewMeeting meeting = newMeetingAssembler.toEntity(po);
                return convertToHistoryDTO(meeting);
            })
            .collect(Collectors.toList());
        
        return new PageResultDTO<>(query.getPageNum(), query.getPageSize(), total, dtoList);
    }

    @Override
    public List<MeetingRoomStatisticsDTO> queryUsageStatistics(MeetingRoomStatisticsQuery query) {
        // 解析年月
        YearMonth yearMonth;
        if (query.getYearMonth() != null && !query.getYearMonth().isEmpty()) {
            try {
                yearMonth = YearMonth.parse(query.getYearMonth());
            } catch (Exception e) {
                // 如果解析失败，使用当前年月
                yearMonth = YearMonth.now();
                log.warn("解析年月参数失败，使用当前年月：{}", yearMonth);
            }
        } else {
            // 默认为当前年月
            yearMonth = YearMonth.now();
        }
        
        int year = yearMonth.getYear();
        int month = yearMonth.getMonthValue();
        
        // 设置时间范围
        LocalDateTime startDateTime = LocalDate.of(year, month, 1).atStartOfDay();
        LocalDateTime endDateTime = yearMonth.atEndOfMonth().plusDays(1).atStartOfDay();
        
        List<MeetingRoomStatisticsDTO> result = new ArrayList<>();
        
        // 如果指定了会议室ID，则只统计该会议室
        if (query.getMeetingRoomId() != null) {
            MeetingRoom meetingRoom = meetingRoomRepository.findById(query.getMeetingRoomId());
            if (meetingRoom != null) {
                result.add(calculateStatistics(meetingRoom, startDateTime, endDateTime));
            }
        } else {
            // 否则统计所有会议室
            List<MeetingRoom> meetingRooms = meetingRoomRepository.findAll();
            for (MeetingRoom meetingRoom : meetingRooms) {
                result.add(calculateStatistics(meetingRoom, startDateTime, endDateTime));
            }
        }
        
        return result;
    }
    
    /**
     * 计算会议室使用统计
     */
    private MeetingRoomStatisticsDTO calculateStatistics(MeetingRoom meetingRoom, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        // 查询该会议室在指定时间范围内的所有会议
        List<NewMeetingPO> meetingPOs = newMeetingMapper.findByMeetingRoomIdAndTimeRange(
            meetingRoom.getId(), 
            startDateTime, 
            endDateTime,
            1, // 这里使用1和Integer.MAX_VALUE表示不分页，获取所有记录
            Integer.MAX_VALUE
        );
        
        List<NewMeeting> meetings = meetingPOs.stream()
            .map(newMeetingAssembler::toEntity)
            .collect(Collectors.toList());
        
        // 计算总使用次数
        int usageCount = meetings.size();
        
        // 计算总使用时长（小时）
        double totalUsageHours = meetings.stream()
            .mapToLong(meeting -> {
                // 计算会议时长（分钟）
                long minutes = ChronoUnit.MINUTES.between(meeting.getStartTime(), meeting.getEndTime());
                return Math.max(0, minutes); // 避免负值
            })
            .sum() / 60.0; // 转换为小时
        
        // 计算平均使用时长（小时）
        double averageUsageHours = usageCount > 0 ? totalUsageHours / usageCount : 0;
        
        // 按天统计
        Map<String, DailyStatistics> dailyMap = new LinkedHashMap<>();
        
        // 初始化每一天的统计数据
        LocalDate startDate = startDateTime.toLocalDate();
        LocalDate endDate = endDateTime.toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        // 初始化所有日期的统计数据（Java 8 兼容方式）
        LocalDate date = startDate;
        while (!date.isAfter(endDate.minusDays(1))) { // endDate是不包含的，所以减1天
            String dateStr = date.format(formatter);
            dailyMap.put(dateStr, DailyStatistics.builder()
                .date(dateStr)
                .usageCount(0)
                .usageHours(0.0)
                .build());
            date = date.plusDays(1);
        }
        
        // 计算每天的使用统计
        for (NewMeeting meeting : meetings) {
            LocalDate meetingDate = meeting.getStartTime().toLocalDate();
            String dateStr = meetingDate.format(formatter);
            
            if (dailyMap.containsKey(dateStr)) {
                DailyStatistics stats = dailyMap.get(dateStr);
                
                // 增加使用次数
                stats.setUsageCount(stats.getUsageCount() + 1);
                
                // 计算会议时长（小时）
                long minutes = ChronoUnit.MINUTES.between(meeting.getStartTime(), meeting.getEndTime());
                double hours = Math.max(0, minutes) / 60.0;
                
                // 增加使用时长
                stats.setUsageHours(stats.getUsageHours() + hours);
            }
        }
        
        // 构建结果
        return MeetingRoomStatisticsDTO.builder()
            .meetingRoomId(meetingRoom.getId())
            .meetingRoomName(meetingRoom.getName())
            .usageCount(usageCount)
            .totalUsageHours(totalUsageHours)
            .averageUsageHours(averageUsageHours)
            .dailyStatistics(new ArrayList<>(dailyMap.values()))
            .build();
    }
    
    /**
     * 将会议实体转换为历史会议DTO
     */
    private MeetingRoomHistoryDTO convertToHistoryDTO(NewMeeting meeting) {
        // 计算会议时长（分钟）
        long duration = ChronoUnit.MINUTES.between(meeting.getStartTime(), meeting.getEndTime());
        
        return MeetingRoomHistoryDTO.builder()
            .id(meeting.getId())
            .meetingName(meeting.getMeetingName())
            .startTime(meeting.getStartTime())
            .endTime(meeting.getEndTime())
            .status(meeting.getStatus())
            .hostUserId(meeting.getHostUserId())
            .hostUserName(meeting.getCreateUserName()) // 使用创建人姓名作为主持人姓名
            .attendeeCount(meeting.getAttendees() != null ? meeting.getAttendees().size() : 0)
            .actualAttendeeCount(meeting.getActualAttendees() != null ? meeting.getActualAttendees().size() : 0)
            .duration((int) duration)
            .build();
    }
}
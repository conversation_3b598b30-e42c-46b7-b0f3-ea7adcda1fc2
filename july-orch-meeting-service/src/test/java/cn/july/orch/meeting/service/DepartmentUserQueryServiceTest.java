package cn.july.orch.meeting.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 部门用户查询服务测试类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class DepartmentUserQueryServiceTest {

    @Resource
    private DepartmentUserQueryService departmentUserQueryService;

    /**
     * 测试获取单个部门下的所有用户
     */
    @Test
    public void testGetUsersByDepartment() {
        // 替换为实际的部门ID进行测试
        String departmentId = "0";  // 使用根部门ID或其他有效的部门ID
        
        log.info("开始测试获取单个部门用户，部门ID: {}", departmentId);
        
        List<String> userOpenIds = departmentUserQueryService.getUsersByDepartment(departmentId);
        
        assertNotNull(userOpenIds, "返回结果不应为null");
        log.info("获取到的用户数量: {}", userOpenIds.size());
        
        if (!userOpenIds.isEmpty()) {
            log.info("部分用户OpenID示例: {}", userOpenIds.subList(0, Math.min(5, userOpenIds.size())));
        }
    }

    /**
     * 测试批量获取多个部门下的所有用户
     */
    @Test
    public void testGetUsersByDepartments() {
        // 替换为实际的部门ID列表进行测试
        List<String> departmentIds = Arrays.asList("0");  // 可以添加多个部门ID
        
        log.info("开始测试批量获取部门用户，部门ID列表: {}", departmentIds);
        
        Map<String, List<String>> result = departmentUserQueryService.getUsersByDepartments(departmentIds);
        
        assertNotNull(result, "返回结果不应为null");
        assertEquals(departmentIds.size(), result.size(), "返回的部门数量应与输入一致");
        
        result.forEach((deptId, users) -> {
            log.info("部门ID: {}, 用户数量: {}", deptId, users.size());
            if (!users.isEmpty()) {
                log.info("部门 {} 的部分用户OpenID示例: {}", 
                        deptId, users.subList(0, Math.min(3, users.size())));
            }
        });
    }

    /**
     * 测试获取多个部门的汇总用户列表
     */
    @Test
    public void testGetAllUsersFromDepartments() {
        // 替换为实际的部门ID列表进行测试
        List<String> departmentIds = Arrays.asList("0");  // 可以添加多个部门ID
        
        log.info("开始测试获取部门汇总用户列表，部门ID列表: {}", departmentIds);
        
        List<String> allUsers = departmentUserQueryService.getAllUsersFromDepartments(departmentIds);
        
        assertNotNull(allUsers, "返回结果不应为null");
        log.info("汇总后的总用户数量: {}", allUsers.size());
        
        if (!allUsers.isEmpty()) {
            log.info("部分用户OpenID示例: {}", allUsers.subList(0, Math.min(5, allUsers.size())));
        }
    }

    /**
     * 测试空参数处理
     */
    @Test
    public void testEmptyDepartmentIds() {
        log.info("开始测试空参数处理");
        
        // 测试空列表
        Map<String, List<String>> emptyResult = departmentUserQueryService.getUsersByDepartments(Arrays.asList());
        assertNotNull(emptyResult, "空列表应返回空Map而不是null");
        assertTrue(emptyResult.isEmpty(), "空列表应返回空Map");
        
        // 测试null参数
        Map<String, List<String>> nullResult = departmentUserQueryService.getUsersByDepartments(null);
        assertNotNull(nullResult, "null参数应返回空Map而不是null");
        assertTrue(nullResult.isEmpty(), "null参数应返回空Map");
        
        log.info("空参数处理测试通过");
    }

    /**
     * 测试无效的部门ID
     */
    @Test
    public void testInvalidDepartmentId() {
        String invalidDeptId = "invalid_dept_id_12345";
        
        log.info("开始测试无效部门ID处理，部门ID: {}", invalidDeptId);
        
        List<String> users = departmentUserQueryService.getUsersByDepartment(invalidDeptId);
        
        assertNotNull(users, "即使部门ID无效，也应返回空列表而不是null");
        log.info("无效部门ID返回的用户数量: {}", users.size());
    }
}

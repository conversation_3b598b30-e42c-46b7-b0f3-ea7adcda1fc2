package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.TaskExtractRespDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class TaskExtractServiceTest {

    @Resource
    private TaskExtractService taskExtractService;

    @Test
    void testExtractTask() {
        // 准备测试数据
        String inputText = "明天下班前，王小二必须拿出技术评审方案。";
        
        // 调用服务方法
        TaskExtractRespDTO result = taskExtractService.extractTask(inputText);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getTaskName());
        assertNotNull(result.getTaskDescription());
        assertNotNull(result.getOwner());
        assertNotNull(result.getDue());
        
        // 验证用户信息列表
        if (result.getOwnerInfo() != null) {
            assertFalse(result.getOwnerInfo().isEmpty());
            assertNotNull(result.getOwnerInfo().get(0).getName());
            assertNotNull(result.getOwnerInfo().get(0).getOpenId());
        }
        
        System.out.println("提取的任务信息：" + result);
    }
}
package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 会议部门用户功能测试类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class MeetingDepartmentUserTest {

    @Resource
    private NewMeetingActionService newMeetingActionService;

    @Resource
    private DepartmentUserQueryService departmentUserQueryService;

    /**
     * 测试创建会议时传入部门ID
     */
    @Test
    public void testCreateMeetingWithDepartmentIds() {
        log.info("开始测试创建会议时传入部门ID");

        // 构造测试命令
        NewMeetingCreateCommand command = NewMeetingCreateCommand.builder()
                .meetingName("测试会议-部门用户功能")
                .meetingDescription("测试通过部门ID自动添加参会人员")
                .startTime(LocalDateTime.now().plusDays(1))
                .endTime(LocalDateTime.now().plusDays(1).plusHours(1))
                .meetingLocation("会议室A")
                .attendees(Arrays.asList("user_001")) // 手动指定的参会人员
                .departmentIds(Arrays.asList("dept_001", "dept_002")) // 部门ID列表
                .build();

        try {
            // 执行创建
            newMeetingActionService.createMeeting(command);
            log.info("创建会议成功");
        } catch (Exception e) {
            log.error("创建会议失败", e);
        }
    }

    /**
     * 测试创建会议时只传入部门ID（不传用户）
     */
    @Test
    public void testCreateMeetingWithOnlyDepartmentIds() {
        log.info("开始测试创建会议时只传入部门ID");

        NewMeetingCreateCommand command = NewMeetingCreateCommand.builder()
                .meetingName("测试会议-仅部门ID")
                .meetingDescription("测试仅通过部门ID添加参会人员")
                .startTime(LocalDateTime.now().plusDays(2))
                .endTime(LocalDateTime.now().plusDays(2).plusHours(1))
                .meetingLocation("会议室B")
                .attendees(Arrays.asList()) // 空的参会人员列表
                .departmentIds(Arrays.asList("dept_001")) // 仅部门ID
                .build();

        try {
            newMeetingActionService.createMeeting(command);
            log.info("创建会议成功");
        } catch (Exception e) {
            log.error("创建会议失败", e);
        }
    }

    /**
     * 测试创建会议时同时传入用户和部门ID
     */
    @Test
    public void testCreateMeetingWithBothUsersAndDepartments() {
        log.info("开始测试创建会议时同时传入用户和部门ID");

        NewMeetingCreateCommand command = NewMeetingCreateCommand.builder()
                .meetingName("测试会议-用户和部门混合")
                .meetingDescription("测试同时传入用户ID和部门ID")
                .startTime(LocalDateTime.now().plusDays(3))
                .endTime(LocalDateTime.now().plusDays(3).plusHours(1))
                .meetingLocation("会议室C")
                .attendees(Arrays.asList("user_001", "user_002", "user_003")) // 手动指定的参会人员
                .departmentIds(Arrays.asList("dept_001", "dept_002")) // 部门ID列表
                .hostUserId("user_001") // 主持人
                .recorderUserId("user_002") // 记录员
                .build();

        try {
            newMeetingActionService.createMeeting(command);
            log.info("创建会议成功");
        } catch (Exception e) {
            log.error("创建会议失败", e);
        }
    }

    /**
     * 测试更新会议时修改部门ID
     */
    @Test
    public void testUpdateMeetingWithDepartmentIds() {
        log.info("开始测试更新会议时修改部门ID");

        // 假设已有会议ID为1
        Long meetingId = 1L;

        NewMeetingUpdateCommand command = NewMeetingUpdateCommand.builder()
                .id(meetingId)
                .meetingName("更新后的会议-部门用户功能")
                .meetingDescription("测试更新会议时修改部门参会人员")
                .startTime(LocalDateTime.now().plusDays(4))
                .endTime(LocalDateTime.now().plusDays(4).plusHours(2))
                .meetingLocation("会议室D")
                .attendees(Arrays.asList("user_004")) // 新的参会人员
                .departmentIds(Arrays.asList("dept_003")) // 新的部门ID
                .build();

        try {
            newMeetingActionService.updateMeeting(command);
            log.info("更新会议成功");
        } catch (Exception e) {
            log.error("更新会议失败", e);
        }
    }

    /**
     * 测试部门用户查询服务
     */
    @Test
    public void testDepartmentUserQuery() {
        log.info("开始测试部门用户查询");

        List<String> departmentIds = Arrays.asList("dept_001", "dept_002");

        try {
            // 测试获取汇总用户列表
            List<String> allUsers = departmentUserQueryService.getAllUsersFromDepartments(departmentIds);
            log.info("从部门 {} 获取到 {} 个用户", departmentIds, allUsers.size());
            
            if (!allUsers.isEmpty()) {
                log.info("用户列表示例（前5个）: {}", 
                        allUsers.subList(0, Math.min(5, allUsers.size())));
            }

            // 测试获取每个部门的用户映射
            Map<String, List<String>> usersByDept = departmentUserQueryService.getUsersByDepartments(departmentIds);
            usersByDept.forEach((deptId, users) -> {
                log.info("部门 {} 有 {} 个用户", deptId, users.size());
            });

        } catch (Exception e) {
            log.error("查询部门用户失败", e);
        }
    }

    /**
     * 测试去重功能
     */
    @Test
    public void testDuplicateRemoval() {
        log.info("开始测试参会人员去重功能");

        // 假设部门dept_001中包含用户user_001和user_002
        // 我们手动也添加user_001，测试是否会去重
        NewMeetingCreateCommand command = NewMeetingCreateCommand.builder()
                .meetingName("测试会议-去重功能")
                .meetingDescription("测试参会人员去重")
                .startTime(LocalDateTime.now().plusDays(5))
                .endTime(LocalDateTime.now().plusDays(5).plusHours(1))
                .meetingLocation("会议室E")
                .attendees(Arrays.asList("user_001", "user_002")) // 手动添加
                .departmentIds(Arrays.asList("dept_001")) // 部门中可能也包含这些用户
                .hostUserId("user_001") // 主持人（也在参会人员中）
                .build();

        try {
            newMeetingActionService.createMeeting(command);
            log.info("创建会议成功，参会人员应已自动去重");
        } catch (Exception e) {
            log.error("创建会议失败", e);
        }
    }
}

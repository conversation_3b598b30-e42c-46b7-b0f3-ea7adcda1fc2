package cn.july.orch.meeting.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> Assistant
 * @description 飞书云文档服务测试
 */
@SpringBootTest
public class FeishuCloudDocumentServiceTest {

    @Test
    public void testTempDirectoryCreation() throws IOException {
        // 测试临时目录创建
        Path projectRoot = Paths.get("").toAbsolutePath();
        Path tempDir = projectRoot.resolve("temp");
        
        // 确保临时目录存在
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }
        
        assertTrue(Files.exists(tempDir), "临时目录应该存在");
        assertTrue(Files.isDirectory(tempDir), "临时目录应该是一个目录");
    }

    @Test
    public void testTempFileCleanup(@TempDir Path tempDir) throws IOException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test_file.md");
        Files.write(testFile, "test content".getBytes());
        
        assertTrue(Files.exists(testFile), "测试文件应该存在");
        
        // 删除文件
        Files.delete(testFile);
        
        assertFalse(Files.exists(testFile), "测试文件应该被删除");
    }
}
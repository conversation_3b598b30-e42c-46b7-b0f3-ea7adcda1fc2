package cn.july.orch.meeting.service;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.TopicCreateCommand;
import cn.july.orch.meeting.domain.command.TopicUpdateCommand;
import cn.july.orch.meeting.domain.dto.TopicDTO;
import cn.july.orch.meeting.domain.query.TopicQuery;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 议题服务测试
 * @date 2025-11-06
 */
@Slf4j
@SpringBootTest
public class TopicServiceTest {

    @Resource
    private TopicService topicService;

    /**
     * 测试创建议题
     */
    @Test
    public void testCreateTopic() {
        TopicCreateCommand command = new TopicCreateCommand();
        command.setName("测试议题-" + System.currentTimeMillis());
        command.setDescription("这是一个测试议题描述");
        command.setAttachmentKeys(Arrays.asList());
        command.setIsEnabled(1);
        
        topicService.createTopic(command);
        log.info("创建议题成功");
    }

    /**
     * 测试查询启用的议题列表
     */
    @Test
    public void testListEnabled() {
        List<TopicDTO> list = topicService.listEnabled();
        log.info("查询到启用的议题数量: {}", list.size());
        list.forEach(topic -> log.info("议题: {}", topic.getName()));
    }

    /**
     * 测试分页查询议题
     */
    @Test
    public void testPageQuery() {
        TopicQuery query = new TopicQuery();
        query.setName("测试");
        query.setIsEnabled(1);
        query.setPageNum(1);
        query.setPageSize(10);
        
        PageResultDTO<TopicDTO> page = topicService.pageQuery(query);
        log.info("查询到议题总数: {}", page.getTotal());
        page.getList().forEach(topic -> log.info("议题: {}", topic.getName()));
    }

    /**
     * 测试根据ID查询议题详情
     */
    @Test
    public void testGetById() {
        // 需要替换为实际存在的ID
        Long id = 1L;
        TopicDTO topic = topicService.getById(id);
        if (topic != null) {
            log.info("查询到议题: {}", topic.getName());
            log.info("附件数量: {}", topic.getAttachments() != null ? topic.getAttachments().size() : 0);
        } else {
            log.info("议题不存在");
        }
    }

    /**
     * 测试更新议题
     */
    @Test
    public void testUpdateTopic() {
        // 需要替换为实际存在的ID
        Long id = 1L;
        
        TopicUpdateCommand command = new TopicUpdateCommand();
        command.setId(id);
        command.setName("更新后的议题名称-" + System.currentTimeMillis());
        command.setDescription("更新后的议题描述");
        command.setAttachmentKeys(Arrays.asList());
        command.setIsEnabled(1);
        
        topicService.updateTopic(command);
        log.info("更新议题成功");
    }

    /**
     * 测试启用议题
     */
    @Test
    public void testEnableTopic() {
        // 需要替换为实际存在的ID
        Long id = 1L;
        topicService.enableTopic(id);
        log.info("启用议题成功");
    }

    /**
     * 测试停用议题
     */
    @Test
    public void testDisableTopic() {
        // 需要替换为实际存在的ID
        Long id = 1L;
        topicService.disableTopic(id);
        log.info("停用议题成功");
    }

    /**
     * 测试删除议题
     */
    @Test
    public void testDeleteTopic() {
        // 需要替换为实际存在的ID
        Long id = 1L;
        topicService.deleteTopic(id);
        log.info("删除议题成功");
    }
}

package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.dto.TaskListDTO;
import cn.july.orch.meeting.domain.entity.TaskInfo;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class TaskConverterTest {

    @Resource
    private TaskConverter taskConverter;

    @Test
    public void testToTaskListDTOWithMeetingName() {
        // 创建一个带有会议ID的TaskInfo对象
        TaskInfo taskInfo = TaskInfo.builder()
                .id(1L)
                .title("测试任务")
                .description("测试任务描述")
                .ownerOpenId("user123")
                .ownerName("测试用户")
                .priority(TaskPriorityEnum.HIGH)
                .status(TaskStatusEnum.NOT_STARTED)
                .dueDate(LocalDateTime.now().plusDays(1))
                .meetingId(1L) // 设置会议ID
                .createTime(LocalDateTime.now())
                .createUserId("user123")
                .createUserName("测试用户")
                .updateTime(LocalDateTime.now())
                .updateUserId("user123")
                .updateUserName("测试用户")
                .build();

        // 转换为TaskListDTO
        TaskListDTO taskListDTO = taskConverter.toTaskListDTO(taskInfo);

        // 验证转换结果
        assertNotNull(taskListDTO);
        assertEquals(taskInfo.getId(), taskListDTO.getId());
        assertEquals(taskInfo.getTitle(), taskListDTO.getTitle());
        assertEquals(taskInfo.getDescription(), taskListDTO.getDescription());
        assertEquals(taskInfo.getOwnerOpenId(), taskListDTO.getOwnerOpenId());
        assertEquals(taskInfo.getOwnerName(), taskListDTO.getOwnerName());
        assertEquals(taskInfo.getPriority(), taskListDTO.getPriority());
        assertEquals(taskInfo.getStatus(), taskListDTO.getStatus());
        assertEquals(taskInfo.getDueDate(), taskListDTO.getDueDate());
        assertEquals(taskInfo.getMeetingId(), taskListDTO.getMeetingId());
        // 注意：由于我们没有实际的会议数据，这里无法验证meetingName是否正确设置
        // 在实际测试中，我们需要mock NewMeetingQueryService来返回特定的会议名称
    }

    @Test
    public void testToTaskListDTOListWithMeetingNames() {
        // 创建多个带有会议ID的TaskInfo对象
        TaskInfo taskInfo1 = TaskInfo.builder()
                .id(1L)
                .title("测试任务1")
                .description("测试任务描述1")
                .ownerOpenId("user123")
                .ownerName("测试用户")
                .priority(TaskPriorityEnum.HIGH)
                .status(TaskStatusEnum.NOT_STARTED)
                .dueDate(LocalDateTime.now().plusDays(1))
                .meetingId(1L) // 设置会议ID
                .createTime(LocalDateTime.now())
                .createUserId("user123")
                .createUserName("测试用户")
                .updateTime(LocalDateTime.now())
                .updateUserId("user123")
                .updateUserName("测试用户")
                .build();

        TaskInfo taskInfo2 = TaskInfo.builder()
                .id(2L)
                .title("测试任务2")
                .description("测试任务描述2")
                .ownerOpenId("user456")
                .ownerName("测试用户2")
                .priority(TaskPriorityEnum.MEDIUM)
                .status(TaskStatusEnum.IN_PROGRESS)
                .dueDate(LocalDateTime.now().plusDays(2))
                .meetingId(2L) // 设置会议ID
                .createTime(LocalDateTime.now())
                .createUserId("user456")
                .createUserName("测试用户2")
                .updateTime(LocalDateTime.now())
                .updateUserId("user456")
                .updateUserName("测试用户2")
                .build();

        List<TaskInfo> taskInfoList = Arrays.asList(taskInfo1, taskInfo2);

        // 转换为TaskListDTO列表
        List<TaskListDTO> taskListDTOList = taskConverter.toTaskListDTOList(taskInfoList);

        // 验证转换结果
        assertNotNull(taskListDTOList);
        assertEquals(2, taskListDTOList.size());
        
        TaskListDTO taskListDTO1 = taskListDTOList.get(0);
        assertEquals(taskInfo1.getId(), taskListDTO1.getId());
        assertEquals(taskInfo1.getTitle(), taskListDTO1.getTitle());
        assertEquals(taskInfo1.getMeetingId(), taskListDTO1.getMeetingId());
        
        TaskListDTO taskListDTO2 = taskListDTOList.get(1);
        assertEquals(taskInfo2.getId(), taskListDTO2.getId());
        assertEquals(taskInfo2.getTitle(), taskListDTO2.getTitle());
        assertEquals(taskInfo2.getMeetingId(), taskListDTO2.getMeetingId());
        
        // 注意：由于我们没有实际的会议数据，这里无法验证meetingName是否正确设置
        // 在实际测试中，我们需要mock NewMeetingQueryService来返回特定的会议名称
    }
}
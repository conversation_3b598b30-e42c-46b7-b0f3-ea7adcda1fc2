# 议题管理功能说明

## 功能概述

议题管理功能提供了对会议议题的完整生命周期管理，包括议题的创建、编辑、查询、删除以及启用/停用操作。

## 功能特性

- ✅ 议题的增删改查
- ✅ 议题的启用/停用
- ✅ 附件管理（基于TOS对象存储）
- ✅ 分页查询和条件筛选
- ✅ 名称唯一性验证

## 数据库表结构

### topic 表

```sql
CREATE TABLE `topic` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `name` varchar(255) NOT NULL COMMENT '议题名称',
  `description` text COMMENT '议题描述',
  `attachment_keys` json DEFAULT NULL COMMENT '附件对象存储key列表(JSON数组)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `create_user_id` varchar(64) DEFAULT NULL COMMENT '创建人ID',
  `create_user_name` varchar(128) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` varchar(64) DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(128) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_name` (`name`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='议题管理表';
```

## API接口说明

### 1. 查询启用的议题列表

**接口地址：** `POST /topic/list`

**说明：** 查询所有启用状态的议题列表，按创建时间倒序排列

**响应示例：**
```json
[
  {
    "id": 1,
    "name": "产品需求讨论",
    "description": "讨论Q1季度产品路线图",
    "attachments": [
      {
        "fileKey": "xxx",
        "originalFilename": "产品需求文档.pdf",
        "url": "https://..."
      }
    ],
    "isEnabled": 1,
    "createUserName": "张三",
    "createTime": "2025-11-06 10:00:00"
  }
]
```

### 2. 查询议题详情

**接口地址：** `GET /topic/detail?id={id}`

**说明：** 根据ID查询议题详细信息

**请求参数：**
- `id`: 议题ID（必填）

**响应示例：** 同上

### 3. 分页查询议题

**接口地址：** `POST /topic/page`

**说明：** 支持分页和条件筛选的议题查询

**请求示例：**
```json
{
  "name": "产品",
  "isEnabled": 1,
  "pageNum": 1,
  "pageSize": 10
}
```

**响应示例：**
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "total": 100,
  "totalPages": 10,
  "list": [...]
}
```

### 4. 创建议题

**接口地址：** `POST /topic/create`

**说明：** 创建新的议题

**请求示例：**
```json
{
  "name": "产品需求讨论",
  "description": "讨论Q1季度产品路线图",
  "attachmentKeys": ["fileKey1", "fileKey2"],
  "isEnabled": 1
}
```

**字段说明：**
- `name`: 议题名称（必填，唯一）
- `description`: 议题描述（可选）
- `attachmentKeys`: 附件Key列表（可选，需先上传文件获取Key）
- `isEnabled`: 是否启用（必填，0-否，1-是）

### 5. 更新议题

**接口地址：** `POST /topic/update`

**说明：** 更新已有议题

**请求示例：**
```json
{
  "id": 1,
  "name": "产品需求讨论（更新）",
  "description": "讨论Q1季度产品路线图（更新）",
  "attachmentKeys": ["fileKey1", "fileKey2"],
  "isEnabled": 1
}
```

**字段说明：** 同创建议题，额外需要提供`id`字段

### 6. 删除议题

**接口地址：** `POST /topic/delete/{id}`

**说明：** 删除指定议题（逻辑删除）

**路径参数：**
- `id`: 议题ID

### 7. 启用议题

**接口地址：** `POST /topic/enable/{id}`

**说明：** 启用指定议题

**路径参数：**
- `id`: 议题ID

### 8. 停用议题

**接口地址：** `POST /topic/disable/{id}`

**说明：** 停用指定议题

**路径参数：**
- `id`: 议题ID

## 代码结构

### 领域模型层
- `TopicPO.java` - 持久化对象
- `TopicDTO.java` - 数据传输对象
- `Topic.java` - 领域实体

### 命令对象
- `TopicCreateCommand.java` - 创建命令
- `TopicUpdateCommand.java` - 更新命令
- `TopicQuery.java` - 查询对象

### 数据访问层
- `TopicMapper.java` - MyBatis Mapper接口
- `TopicMapper.xml` - SQL映射文件

### 服务层
- `TopicService.java` - 业务逻辑服务

### 控制器层
- `TopicController.java` - REST API控制器

### 对象转换
- `TopicAssembler.java` - 对象转换器（MapStruct）

## 业务规则

1. **名称唯一性**：同一租户下议题名称不能重复
2. **附件验证**：创建或更新议题时，会验证附件Key的有效性
3. **状态管理**：议题支持启用/停用两种状态
4. **逻辑删除**：删除操作为逻辑删除，数据不会真正删除
5. **自动审计**：自动记录创建人、创建时间、更新人、更新时间

## 附件处理说明

根据项目规范，附件处理遵循以下原则：

1. **存储方式**：只存储TOS对象存储的Key，不存储完整文件信息
2. **字段设计**：使用`attachment_keys`字段存储JSON数组格式的文件Key列表
3. **信息获取**：查询时通过`FileDetailService`根据Key获取完整的文件信息
4. **验证机制**：创建/更新时验证文件Key的有效性

## 依赖说明

- Spring Boot
- MyBatis-Plus
- MapStruct (对象转换)
- X-File-Storage (文件存储)
- Swagger (API文档)

## 注意事项

1. 所有接口都需要通过SSO认证
2. 操作会自动记录操作人信息（通过拦截器）
3. 租户隔离机制自动生效
4. 附件需要先上传获取Key后再关联到议题

## 扩展建议

后续可以根据需要扩展以下功能：

- [ ] 议题分类/标签
- [ ] 议题与会议的关联
- [ ] 议题模板
- [ ] 议题排序
- [ ] 议题批量操作
- [ ] 议题导入/导出

# 会议部门参会人员功能说明

## 功能概述

在创建或更新会议时，除了可以手动指定参会人员（通过 `attendees` 字段），还可以通过指定部门ID（`departmentIds` 字段）来自动添加该部门及其所有子部门下的用户作为参会人员。

## 核心特性

✅ **支持多种参会人员添加方式**
- 仅通过用户ID指定参会人员
- 仅通过部门ID指定参会人员（自动获取部门下所有用户）
- 同时使用用户ID和部门ID（两者合并）

✅ **智能处理**
- 自动递归获取部门及所有子部门的用户
- 自动去重（包括主持人、记录员）
- 单个部门查询失败不影响整体流程

✅ **灵活配置**
- `attendees` 和 `departmentIds` 都是可选的
- 至少需要提供其中一个（或通过主持人、记录员补充）

## API 接口说明

### 创建会议接口

**接口地址**：`POST /new-meeting/create`

**请求参数**：

```json
{
  "meetingName": "部门周例会",
  "meetingDescription": "技术部门周例会",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "meetingLocation": "3楼会议室",
  "attendees": ["user_001", "user_002"],  // 手动指定的参会人员（可为空数组）
  "departmentIds": ["dept_001", "dept_002"],  // 部门ID列表（可选）
  "hostUserId": "user_001",  // 主持人（可选）
  "recorderUserId": "user_002",  // 记录员（可选）
  "enableDocAiSummary": true,
  "meetingTagIds": [1, 2],
  "preMeetingDocumentKeys": ["file_key_001"]
}
```

### 更新会议接口

**接口地址**：`POST /new-meeting/update`

**请求参数**：

```json
{
  "id": 1,
  "meetingName": "部门周例会（更新）",
  "meetingDescription": "技术部门周例会",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "meetingLocation": "3楼会议室",
  "attendees": ["user_003"],  // 新的参会人员
  "departmentIds": ["dept_003"],  // 新的部门ID列表
  "hostUserId": "user_003",
  "recorderUserId": "user_004"
}
```

## 使用场景

### 场景1：纯部门会议

适用于整个部门或多个部门的全员会议。

**示例**：
```json
{
  "meetingName": "全员大会",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "attendees": [],  // 空数组
  "departmentIds": ["dept_tech", "dept_product"]  // 技术部和产品部全员参加
}
```

**结果**：
- 技术部下所有用户（包括所有子部门）
- 产品部下所有用户（包括所有子部门）
- 自动去重后作为参会人员

### 场景2：部门+特定人员

适用于以部门为主，额外邀请其他人员的会议。

**示例**：
```json
{
  "meetingName": "技术评审会",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "attendees": ["ceo_openid", "cto_openid"],  // 额外邀请CEO和CTO
  "departmentIds": ["dept_tech_backend"]  // 后端技术部全员
}
```

**结果**：
- 后端技术部下所有用户
- CEO
- CTO
- 自动去重后作为参会人员

### 场景3：特定人员会议

传统的手动指定参会人员方式。

**示例**：
```json
{
  "meetingName": "项目核心小组会议",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "attendees": ["user_001", "user_002", "user_003"],
  "departmentIds": []  // 或不传此字段
}
```

### 场景4：跨部门协作会议

适用于需要多个部门参与的协作会议。

**示例**：
```json
{
  "meetingName": "产品技术对齐会",
  "startTime": "2025-10-20 14:00:00",
  "endTime": "2025-10-20 15:00:00",
  "attendees": ["pm_lead", "tech_lead"],  // 产品和技术负责人
  "departmentIds": ["dept_product", "dept_tech_frontend", "dept_tech_backend"],
  "hostUserId": "pm_lead"
}
```

**结果**：
- 产品部门全员
- 前端技术部全员
- 后端技术部全员
- 产品负责人（已在部门中会去重）
- 技术负责人（已在部门中会去重）
- 主持人（已添加会去重）

## 参会人员合并逻辑

系统会按以下顺序处理参会人员：

1. **获取部门用户**：如果传入了 `departmentIds`，递归获取所有部门及子部门的用户
2. **合并用户列表**：将部门用户添加到 `attendees` 列表
3. **添加主持人**：如果指定了 `hostUserId`，添加到列表
4. **添加记录员**：如果指定了 `recorderUserId`，添加到列表
5. **去重处理**：对最终的参会人员列表进行去重

**伪代码**：
```
最终参会人员 = distinct(
    attendees + 
    getAllUsersFromDepartments(departmentIds) + 
    [hostUserId] + 
    [recorderUserId]
)
```

## 错误处理

### 参数验证

- `attendees` 字段标记为必填，但可以传空数组 `[]`
- 如果 `attendees` 为空且 `departmentIds` 也为空，需要至少指定 `hostUserId` 或 `recorderUserId`
- 部门ID格式错误时会记录日志但不影响整体流程

### 部门查询失败

- 单个部门查询失败不影响其他部门
- 查询失败会记录错误日志
- 返回空列表继续后续流程

### 去重处理

- 自动去除重复的用户ID
- 包括从不同来源添加的同一用户

## 日志记录

系统会记录详细的日志便于排查问题：

```
开始从部门获取用户，部门数量：2
从部门获取到 15 个用户添加到参会人员列表
最终参会人员数量（去重后）：18
```

## 性能考虑

1. **批量查询**：使用 `getAllUsersFromDepartments` 方法一次性获取所有部门的用户
2. **递归优化**：每个部门只查询一次，避免重复查询
3. **去重处理**：使用 Stream API 的 `distinct()` 方法高效去重
4. **异步不影响**：部门用户查询在主流程中同步执行，确保数据准确性

## 注意事项

⚠️ **权限要求**
- 需要确保飞书应用具有读取通讯录的权限
- 只能查询应用授权范围内的部门和用户

⚠️ **数据一致性**
- 部门用户数据基于创建/更新时的实时数据
- 如果后续部门人员发生变化，已创建的会议不会自动更新

⚠️ **大部门处理**
- 如果部门人员数量很大，查询可能需要较长时间
- 建议合理规划会议参会范围

⚠️ **子部门递归**
- 自动包含所有层级的子部门用户
- 如果只需要直属部门用户，建议使用 `attendees` 手动指定

## 完整示例

### 示例1：创建技术部门周会

```bash
curl -X POST "http://localhost:8080/new-meeting/create" \
  -H "Content-Type: application/json" \
  -d '{
    "meetingName": "技术部周会",
    "meetingDescription": "讨论本周技术进展和下周规划",
    "startTime": "2025-10-20 14:00:00",
    "endTime": "2025-10-20 15:00:00",
    "meetingLocation": "3楼大会议室",
    "attendees": [],
    "departmentIds": ["dept_tech"],
    "hostUserId": "tech_director_openid",
    "enableDocAiSummary": true,
    "meetingTagIds": [1, 2]
  }'
```

### 示例2：更新会议增加产品部门

```bash
curl -X POST "http://localhost:8080/new-meeting/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "meetingName": "技术产品联席会议",
    "meetingDescription": "技术和产品部门联合会议",
    "startTime": "2025-10-20 14:00:00",
    "endTime": "2025-10-20 16:00:00",
    "meetingLocation": "3楼大会议室",
    "attendees": ["ceo_openid"],
    "departmentIds": ["dept_tech", "dept_product"],
    "hostUserId": "tech_director_openid"
  }'
```

### 示例3：创建跨部门项目会议

```bash
curl -X POST "http://localhost:8080/new-meeting/create" \
  -H "Content-Type: application/json" \
  -d '{
    "meetingName": "新产品项目启动会",
    "meetingDescription": "新产品开发项目启动会议",
    "startTime": "2025-10-21 10:00:00",
    "endTime": "2025-10-21 12:00:00",
    "meetingLocation": "会议中心",
    "attendees": ["pm_001", "designer_001"],
    "departmentIds": ["dept_tech_frontend", "dept_tech_backend", "dept_test"],
    "hostUserId": "pm_001",
    "recorderUserId": "admin_001",
    "enableDocAiSummary": true,
    "meetingTagIds": [3, 5],
    "preMeetingDocumentKeys": ["prd_doc_key", "design_doc_key"]
  }'
```

## 相关代码文件

- [`NewMeetingCreateCommand`](d:\Work\july\project\july-orch-meeting\july-orch-meeting-service\src\main\java\cn\july\orch\meeting\domain\command\NewMeetingCreateCommand.java) - 创建会议命令对象
- [`NewMeetingUpdateCommand`](d:\Work\july\project\july-orch-meeting\july-orch-meeting-service\src\main\java\cn\july\orch\meeting\domain\command\NewMeetingUpdateCommand.java) - 更新会议命令对象
- [`NewMeetingActionService`](d:\Work\july\project\july-orch-meeting\july-orch-meeting-service\src\main\java\cn\july\orch\meeting\service\NewMeetingActionService.java) - 会议操作服务
- [`DepartmentUserQueryService`](d:\Work\july\project\july-orch-meeting\july-orch-meeting-service\src\main\java\cn\july\orch\meeting\service\DepartmentUserQueryService.java) - 部门用户查询服务
- [`ContactService`](d:\Work\july\project\july-orch-meeting\july-feishu\src\main\java\cn\july\feishu\service\ContactService.java) - 飞书通讯录服务

## 测试

参考测试类：[`MeetingDepartmentUserTest`](d:\Work\july\project\july-orch-meeting\july-orch-meeting-service\src\test\java\cn\july\orch\meeting\service\MeetingDepartmentUserTest.java)

运行测试：
```bash
mvn test -Dtest=MeetingDepartmentUserTest
```

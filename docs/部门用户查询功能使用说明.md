# 部门用户查询功能使用说明

## 功能概述

本功能提供了根据部门 ID 批量获取部门下所有用户的能力，支持递归获取子部门用户。

## 功能特性

1. **递归获取子部门**：自动递归获取指定部门及其所有层级子部门的用户
2. **批量查询**：支持一次性查询多个部门的用户信息
3. **去重处理**：自动对用户列表进行去重处理
4. **异常处理**：完善的异常处理机制，确保部分查询失败不影响整体结果

## 技术实现

### 1. july-feishu 模块

在 `ContactService` 中新增了以下方法：

#### getAllDepartments
递归获取部门及其所有子部门列表

```java
/**
 * 递归获取部门及其所有子部门列表（包含自身）
 * @param departmentId 部门ID
 * @param fetchChild 是否递归获取子部门，true为递归获取所有层级子部门
 * @return 部门列表，第一个元素为当前部门，后续为所有子部门
 */
public List<Department> getAllDepartments(String departmentId, boolean fetchChild)
```

#### getUsersByDepartments
批量获取多个部门下的所有用户（包含子部门）

```java
/**
 * 批量获取多个部门下的所有用户（包含子部门）
 * @param departmentIds 部门ID列表
 * @return Map<部门ID, 该部门下所有用户的OpenID列表>
 */
public Map<String, List<String>> getUsersByDepartments(List<String> departmentIds)
```

### 2. july-orch-meeting-service 模块

#### DepartmentUserQueryService

提供业务层的部门用户查询服务，包含以下方法：

##### getUsersByDepartments
批量获取多个部门下的所有用户

```java
/**
 * 批量获取多个部门下的所有用户（包含子部门）
 * @param departmentIds 部门ID列表
 * @return Map<部门ID, 该部门及其所有子部门下的用户OpenID列表>
 */
public Map<String, List<String>> getUsersByDepartments(List<String> departmentIds)
```

##### getUsersByDepartment
获取单个部门下的所有用户

```java
/**
 * 获取单个部门下的所有用户（包含子部门）
 * @param departmentId 部门ID
 * @return 该部门及其所有子部门下的用户OpenID列表
 */
public List<String> getUsersByDepartment(String departmentId)
```

##### getAllUsersFromDepartments
获取多个部门下所有用户的汇总列表

```java
/**
 * 获取多个部门下所有用户的汇总列表（去重）
 * @param departmentIds 部门ID列表
 * @return 所有部门下的用户OpenID列表（已去重）
 */
public List<String> getAllUsersFromDepartments(List<String> departmentIds)
```

## API 接口说明

### 1. 批量获取部门用户

**接口地址**：`POST /user/getUsersByDepartments`

**请求参数**：
```json
["dept_id_1", "dept_id_2", "dept_id_3"]
```

**返回结果**：
```json
{
  "dept_id_1": ["open_id_1", "open_id_2", "open_id_3"],
  "dept_id_2": ["open_id_4", "open_id_5"],
  "dept_id_3": ["open_id_6", "open_id_7", "open_id_8"]
}
```

### 2. 获取单个部门用户

**接口地址**：`GET /user/getUsersByDepartment?departmentId={departmentId}`

**请求参数**：
- departmentId: 部门ID（Query参数）

**返回结果**：
```json
["open_id_1", "open_id_2", "open_id_3", "open_id_4"]
```

### 3. 获取部门用户汇总列表

**接口地址**：`POST /user/getAllUsersFromDepartments`

**请求参数**：
```json
["dept_id_1", "dept_id_2", "dept_id_3"]
```

**返回结果**：
```json
["open_id_1", "open_id_2", "open_id_3", "open_id_4", "open_id_5", "open_id_6"]
```

## 使用示例

### 1. 在代码中使用

```java
@Resource
private DepartmentUserQueryService departmentUserQueryService;

public void example() {
    // 获取单个部门的用户
    List<String> users = departmentUserQueryService.getUsersByDepartment("dept_001");
    
    // 批量获取多个部门的用户
    List<String> deptIds = Arrays.asList("dept_001", "dept_002", "dept_003");
    Map<String, List<String>> usersByDepts = departmentUserQueryService.getUsersByDepartments(deptIds);
    
    // 获取多个部门的汇总用户列表（去重）
    List<String> allUsers = departmentUserQueryService.getAllUsersFromDepartments(deptIds);
}
```

### 2. 在会议创建/更新中使用

会议创建和更新功能已集成部门用户查询能力，支持通过部门ID自动添加参会人员：

```java
// 创建会议 - 只传部门ID
NewMeetingCreateCommand command1 = NewMeetingCreateCommand.builder()
    .meetingName("部门例会")
    .startTime(LocalDateTime.now().plusDays(1))
    .endTime(LocalDateTime.now().plusDays(1).plusHours(1))
    .attendees(new ArrayList<>()) // 空列表
    .departmentIds(Arrays.asList("dept_001", "dept_002")) // 部门下所有用户自动加入
    .build();

// 创建会议 - 同时传用户和部门ID
NewMeetingCreateCommand command2 = NewMeetingCreateCommand.builder()
    .meetingName("混合会议")
    .startTime(LocalDateTime.now().plusDays(1))
    .endTime(LocalDateTime.now().plusDays(1).plusHours(1))
    .attendees(Arrays.asList("user_001", "user_002")) // 指定的用户
    .departmentIds(Arrays.asList("dept_001")) // 部门下所有用户也会加入
    .hostUserId("user_001")
    .build();
    // 最终参会人员 = attendees + 部门用户 + hostUserId（自动去重）

// 更新会议
NewMeetingUpdateCommand updateCommand = NewMeetingUpdateCommand.builder()
    .id(1L)
    .meetingName("更新后的会议")
    .startTime(LocalDateTime.now().plusDays(2))
    .endTime(LocalDateTime.now().plusDays(2).plusHours(1))
    .attendees(Arrays.asList("user_003"))
    .departmentIds(Arrays.asList("dept_002", "dept_003")) // 修改参会部门
    .build();
```

**特性说明：**
- ✅ 支持只传部门ID（attendees为空列表）
- ✅ 支持只传用户ID（departmentIds为空或null）
- ✅ 支持同时传用户ID和部门ID
- ✅ 自动递归获取部门及所有子部门的用户
- ✅ 自动去重（包括hostUserId、recorderUserId）
- ✅ 单个部门查询失败不影响其他部门

### 3. 通过 HTTP 调用

```bash
# 获取单个部门用户
curl -X GET "http://localhost:8080/user/getUsersByDepartment?departmentId=dept_001"

# 批量获取部门用户
curl -X POST "http://localhost:8080/user/getUsersByDepartments" \
  -H "Content-Type: application/json" \
  -d '["dept_001", "dept_002", "dept_003"]'

# 获取部门用户汇总列表
curl -X POST "http://localhost:8080/user/getAllUsersFromDepartments" \
  -H "Content-Type: application/json" \
  -d '["dept_001", "dept_002", "dept_003"]'
```

## 实现原理

### 查询流程

```
1. 接收部门ID列表
   ↓
2. 对每个部门ID：
   a. 获取部门信息
   b. 递归获取所有子部门
   c. 对每个部门（含子部门）获取直属用户
   d. 收集所有用户OpenID并去重
   ↓
3. 返回结果（Map或List）
```

### 递归查询逻辑

```java
getAllDepartments(departmentId, true) {
    1. 获取当前部门信息
    2. 添加到结果列表
    3. 获取直接子部门列表
    4. 对每个子部门递归调用 getAllDepartments
    5. 合并所有结果
}
```

## 性能考虑

1. **分页处理**：飞书API每次最多返回50条记录，代码中已实现自动分页获取
2. **批量查询**：部门信息查询使用批量接口，每次最多50个部门
3. **去重优化**：使用 Set 数据结构进行自动去重
4. **异常容错**：单个部门查询失败不影响其他部门的查询结果

## 注意事项

1. **部门ID类型**：默认使用 department_id 类型，如需使用 open_department_id，需要在飞书API调用时指定
2. **权限要求**：需要确保飞书应用具有读取通讯录的权限
3. **数据范围**：仅能查询应用通讯录授权范围内的部门和用户
4. **递归深度**：理论上支持无限层级，但实际受组织架构深度限制

## 错误处理

所有方法都包含完善的错误处理：
- 参数为空时返回空集合而不是null
- 单个部门查询失败不影响其他部门
- 异常信息会记录到日志中，便于排查问题

## 测试

运行测试类验证功能：

```bash
# 运行单元测试
mvn test -Dtest=DepartmentUserQueryServiceTest
```

测试覆盖场景：
- 单个部门用户查询
- 批量部门用户查询
- 用户汇总列表查询
- 空参数处理
- 无效部门ID处理

## 相关文档

- [飞书获取部门直属用户列表API](https://open.feishu.cn/document/server-docs/contact-v3/user/find_by_department)
- [飞书获取子部门列表API](https://open.feishu.cn/document/server-docs/contact-v3/department/children)

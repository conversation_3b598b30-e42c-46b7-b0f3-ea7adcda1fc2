# 议题管理API使用示例

本文档提供议题管理功能的实际使用示例，包括完整的请求和响应数据。

## 前置条件

1. 已执行数据库建表脚本
2. 服务已启动
3. 已通过SSO认证获取Token
4. 如需上传附件，先调用文件上传接口获取fileKey

## 1. 创建议题

### 请求示例

**接口**: `POST /topic/create`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**:
```json
{
  "name": "Q1季度产品规划讨论",
  "description": "讨论Q1季度的产品路线图，包括新功能开发计划、技术架构升级等内容",
  "attachmentKeys": [
    "1234567890abcdef",
    "fedcba0987654321"
  ],
  "isEnabled": 1
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 业务规则
- 议题名称不能为空
- 议题名称不能重复
- attachmentKeys中的文件Key必须真实存在
- isEnabled必须是0或1

---

## 2. 分页查询议题

### 请求示例

**接口**: `POST /topic/page`

**请求体**:
```json
{
  "name": "产品",
  "isEnabled": 1,
  "pageNum": 1,
  "pageSize": 10
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNo": 1,
    "pageSize": 10,
    "total": 25,
    "totalPages": 3,
    "list": [
      {
        "id": 1,
        "name": "Q1季度产品规划讨论",
        "description": "讨论Q1季度的产品路线图，包括新功能开发计划、技术架构升级等内容",
        "attachments": [
          {
            "fileKey": "1234567890abcdef",
            "originalFilename": "产品路线图.pdf",
            "url": "https://tos.example.com/files/1234567890abcdef.pdf"
          },
          {
            "fileKey": "fedcba0987654321",
            "originalFilename": "技术架构图.png",
            "url": "https://tos.example.com/files/fedcba0987654321.png"
          }
        ],
        "isEnabled": 1,
        "createUserId": "ou_xxx",
        "createUserName": "张三",
        "createTime": "2025-11-06 10:00:00",
        "updateUserId": "ou_xxx",
        "updateUserName": "张三",
        "updateTime": "2025-11-06 10:00:00"
      },
      {
        "id": 2,
        "name": "产品需求评审",
        "description": "评审本周提交的产品需求",
        "attachments": [],
        "isEnabled": 1,
        "createUserId": "ou_yyy",
        "createUserName": "李四",
        "createTime": "2025-11-06 09:30:00",
        "updateUserId": "ou_yyy",
        "updateUserName": "李四",
        "updateTime": "2025-11-06 09:30:00"
      }
    ]
  }
}
```

### 查询说明
- `name`: 支持模糊查询，可为空
- `isEnabled`: 精确查询，可为空（空表示查询全部）
- `pageNum`: 页码，默认1
- `pageSize`: 每页大小，默认10

---

## 3. 查询启用的议题列表

### 请求示例

**接口**: `POST /topic/list`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "Q1季度产品规划讨论",
      "description": "讨论Q1季度的产品路线图",
      "attachments": [...],
      "isEnabled": 1,
      "createUserName": "张三",
      "createTime": "2025-11-06 10:00:00"
    },
    {
      "id": 3,
      "name": "技术方案评审",
      "description": "评审本周的技术方案",
      "attachments": [],
      "isEnabled": 1,
      "createUserName": "王五",
      "createTime": "2025-11-05 15:00:00"
    }
  ]
}
```

### 说明
- 只返回启用状态（isEnabled=1）的议题
- 按创建时间倒序排列
- 不支持分页，返回全部启用议题

---

## 4. 查询议题详情

### 请求示例

**接口**: `GET /topic/detail?id=1`

**请求参数**:
- `id`: 议题ID（必填）

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "Q1季度产品规划讨论",
    "description": "讨论Q1季度的产品路线图，包括新功能开发计划、技术架构升级等内容",
    "attachments": [
      {
        "fileKey": "1234567890abcdef",
        "originalFilename": "产品路线图.pdf",
        "url": "https://tos.example.com/files/1234567890abcdef.pdf"
      }
    ],
    "isEnabled": 1,
    "createUserId": "ou_xxx",
    "createUserName": "张三",
    "createTime": "2025-11-06 10:00:00",
    "updateUserId": "ou_xxx",
    "updateUserName": "张三",
    "updateTime": "2025-11-06 10:00:00"
  }
}
```

**议题不存在时**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

---

## 5. 更新议题

### 请求示例

**接口**: `POST /topic/update`

**请求体**:
```json
{
  "id": 1,
  "name": "Q1季度产品规划讨论（修订版）",
  "description": "讨论Q1季度的产品路线图（已更新内容）",
  "attachmentKeys": [
    "1234567890abcdef",
    "fedcba0987654321",
    "newfilekey123456"
  ],
  "isEnabled": 1
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 业务规则
- 必须提供id
- 更新时仍会校验名称唯一性（排除自己）
- 可以添加或删除附件
- 更新人和更新时间会自动记录

---

## 6. 启用议题

### 请求示例

**接口**: `POST /topic/enable/1`

**路径参数**:
- `id`: 议题ID

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 说明
- 将议题的isEnabled设置为1
- 启用后该议题会在列表查询中显示

---

## 7. 停用议题

### 请求示例

**接口**: `POST /topic/disable/1`

**路径参数**:
- `id`: 议题ID

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 说明
- 将议题的isEnabled设置为0
- 停用后该议题不会在列表查询中显示
- 但仍可通过详情接口查询
- 分页查询时可以通过条件查询到

---

## 8. 删除议题

### 请求示例

**接口**: `POST /topic/delete/1`

**路径参数**:
- `id`: 议题ID

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 说明
- 采用逻辑删除，数据不会真正删除
- 删除后无法通过任何查询接口查到
- 可以在数据库层面恢复

---

## 错误响应示例

### 议题名称已存在
```json
{
  "code": 500,
  "message": "议题名称已存在",
  "data": null
}
```

### 议题不存在
```json
{
  "code": 500,
  "message": "议题不存在",
  "data": null
}
```

### 附件不存在
```json
{
  "code": 500,
  "message": "附件不存在: 1234567890abcdef",
  "data": null
}
```

### 参数校验失败
```json
{
  "code": 400,
  "message": "议题名称不能为空",
  "data": null
}
```

---

## 完整使用流程示例

### 场景：创建一个带附件的议题

#### 步骤1: 上传附件文件
```bash
POST /file/upload
Content-Type: multipart/form-data

# 上传文件，获取返回的fileKey
Response: {
  "code": 200,
  "data": {
    "fileKey": "abc123def456",
    "url": "https://..."
  }
}
```

#### 步骤2: 创建议题
```bash
POST /topic/create
Content-Type: application/json

{
  "name": "技术方案评审",
  "description": "评审新架构技术方案",
  "attachmentKeys": ["abc123def456"],
  "isEnabled": 1
}
```

#### 步骤3: 查询议题详情验证
```bash
GET /topic/detail?id=1

Response: {
  "id": 1,
  "name": "技术方案评审",
  "attachments": [
    {
      "fileKey": "abc123def456",
      "originalFilename": "技术方案.pdf",
      "url": "https://..."
    }
  ]
}
```

#### 步骤4: 如需更新，添加新附件
```bash
# 先上传新文件
POST /file/upload
Response: { "fileKey": "xyz789uvw012" }

# 更新议题
POST /topic/update
{
  "id": 1,
  "name": "技术方案评审（已修订）",
  "attachmentKeys": ["abc123def456", "xyz789uvw012"],
  "isEnabled": 1
}
```

---

## 测试建议

### 使用Postman测试
1. 导入API集合
2. 配置环境变量（baseUrl, token）
3. 按顺序执行测试用例

### 使用Swagger UI测试
1. 访问 http://localhost:端口/swagger-ui.html
2. 找到"议题管理"标签
3. 点击Try it out进行测试

### 使用单元测试
```bash
# 运行所有测试
mvn test -Dtest=TopicServiceTest

# 运行单个测试方法
mvn test -Dtest=TopicServiceTest#testCreateTopic
```

---

## 常见问题

### Q: 附件如何处理？
A: 附件需要先通过文件上传接口上传，获取fileKey后再关联到议题。议题只存储fileKey，查询时会自动加载完整的文件信息。

### Q: 删除议题后数据还能恢复吗？
A: 可以，删除是逻辑删除，只是将deleted字段设为1。可以通过数据库直接修改恢复。

### Q: 停用和删除有什么区别？
A: 停用只是改变启用状态，仍可查询和修改；删除后无法通过接口查询，但数据仍在数据库中。

### Q: 议题名称可以重复吗？
A: 不可以，同一租户下议题名称必须唯一。

### Q: 如何批量操作议题？
A: 当前版本不支持批量操作，需要循环调用单个操作接口。

---

## 性能建议

1. **分页查询**: 建议每页不超过50条
2. **附件数量**: 单个议题建议不超过20个附件
3. **并发操作**: 同一议题的更新操作建议加锁控制
4. **缓存策略**: 可考虑对启用列表增加缓存
